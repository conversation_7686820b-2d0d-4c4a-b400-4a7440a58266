# Project-wide .gitignore for Enquiry Management System

# Environment files (project-wide)
.env
.env.local
.env.development
.env.test
.env.production
.env.staging
*.env

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
*.sublime-project
*.sublime-workspace

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini
$RECYCLE.BIN/

# Logs (project-wide)
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Temporary files
tmp/
temp/
.tmp/
*.tmp
*.temp
*.bak
*.backup

# Documentation build outputs
docs/build/
docs/dist/
docs/generated/

# Docker volumes and data
postgres_data/
pgadmin_data/
docker-data/
volumes/

# Docker override files
docker-compose.override.yml
docker-compose.local.yml
docker-compose.dev.yml

# Kubernetes local files
k8s/local/
*.local.yaml
*.local.yml

# Terraform files
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl
terraform.tfvars
terraform.tfvars.json

# Ansible files
*.retry
inventory/local/

# Vagrant
.vagrant/

# Local development scripts
dev.sh
debug.sh
local.sh
start-local.sh
setup-local.sh

# Monitoring and metrics
monitoring/local/
metrics/local/
logs/local/

# SSL certificates and keys
*.pem
*.key
*.crt
*.cert
*.p12
*.pfx
certs/
certificates/
ssl/

# Backup files
*.sql.bak
*.db.bak
backup/
backups/

# Cache directories
.cache/
cache/

# Local configuration overrides
config.local.yaml
config.local.json
config.local.toml
local.config.*

# Performance profiling
*.prof
*.pprof
*.cpuprofile
*.heapprofile

# Security scanning results
security-reports/
vulnerability-reports/
.snyk

# Dependency vulnerability databases
.audit-cache/

# License reports
license-reports/

# Code quality reports
quality-reports/
sonar-reports/

# Test reports (project-wide)
test-reports/
coverage-reports/
junit-reports/

# Build artifacts (project-wide)
build/
dist/
out/
target/

# Package manager files (if managing at root level)
# node_modules/  # Handled by individual projects
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Python virtual environments (if any scripts)
venv/
env/
.venv/
.env/
__pycache__/
*.py[cod]
*$py.class

# Ruby files (if any scripts)
*.gem
*.rbc
.bundle/
vendor/bundle/

# Java files (if any)
*.class
*.jar
*.war
*.ear

# .NET files (if any)
bin/
obj/
*.dll
*.exe
*.pdb

# Rust files (if any)
target/
Cargo.lock

# Go files (handled by backend/.gitignore)
# *.exe
# *.dll
# *.so
# *.dylib

# Database files
*.db
*.sqlite
*.sqlite3
*.mdb

# Archive files
*.zip
*.tar.gz
*.tar.bz2
*.rar
*.7z

# Large media files (uncomment if needed)
# *.mp4
# *.avi
# *.mov
# *.wmv
# *.flv
# *.webm
# *.mp3
# *.wav
# *.flac
# *.aac
# *.ogg

# Design files (uncomment if needed)
# *.psd
# *.ai
# *.sketch
# *.fig
# *.xd

# Documentation source files (if using specific tools)
# *.docx
# *.xlsx
# *.pptx

# Local development databases
local.db
dev.db
test.db

# Local development services
redis-data/
elasticsearch-data/
mongodb-data/

# CI/CD local files
.github/local/
.gitlab-ci.local.yml
.circleci/local/

# Deployment files
deploy/local/
deployment/local/
.deploy/

# Infrastructure as Code local files
infrastructure/local/
infra/local/

# Secrets and credentials
secrets/
credentials/
.secrets/
.credentials/

# Local development tools
.devcontainer/local/
.codespaces/

# Project-specific tools
tools/local/
scripts/local/

# Experimental features
experimental/
experiments/
poc/
prototype/

# Archive and old versions
archive/
old/
deprecated/
legacy/

# Vendor directories
vendor/
third-party/

# Generated API documentation
api-docs/generated/
swagger-ui/

# Localization files (if auto-generated)
locales/generated/
i18n/generated/

# Content management
content/generated/
cms/generated/

# Analytics and tracking
analytics/
tracking/
.analytics/

# A/B testing
ab-testing/
experiments/

# Feature flags
feature-flags/
.feature-flags/

# User data (for development)
user-data/
sample-data/
mock-data/

# Performance benchmarks
benchmarks/
.benchmarks/

# Load testing
load-testing/
stress-testing/

# Integration testing
integration-tests/local/

# End-to-end testing
e2e-tests/local/

# Manual testing
manual-tests/
test-data/

# QA and testing
qa/local/
testing/local/

# Staging environment files
staging/local/

# Production environment files
production/local/

# Maintenance scripts
maintenance/local/

# Migration scripts
migrations/local/

# Seed data
seeds/local/

# Fixtures
fixtures/local/

# Mocks and stubs
mocks/local/
stubs/local/

# Local development documentation
docs/local/
documentation/local/

# Project notes
notes/
.notes/
TODO.local
NOTES.local

# Local project configuration
.project/
.workspace/

# JetBrains IDEs
.idea/
*.iml
*.iws
*.ipr

# Visual Studio
.vs/
*.user
*.suo
*.userosscache
*.sln.docstates

# Visual Studio Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Vim
*.swp
*.swo
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Local history
.history/
.localhistory/

# Finder (macOS)
.fseventsd/
.DocumentRevisions-V100/
.TemporaryItems/
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Linux
.fuse_hidden*
.directory
.Trash-*
.nfs*

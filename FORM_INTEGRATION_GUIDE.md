# Form Submission URL Integration Guide

## Overview

The enquiry management system now supports Formspree-like form submission URLs, allowing you to collect enquiries directly from HTML forms without requiring API keys or JavaScript.

## Features

✅ **Simple HTML Forms** - Direct form submission without JavaScript  
✅ **AJAX Support** - JSON submissions for dynamic forms  
✅ **No API Keys Required** - Uses short form IDs instead  
✅ **Spam Protection** - Built-in honeypot field support  
✅ **Redirect Support** - Automatic redirect after successful submission  
✅ **Custom Fields** - Extra form fields stored as metadata  
✅ **CORS Enabled** - Works from any domain  

## Quick Start

### 1. Get Your Form ID

1. Log in to your CRM as an admin
2. Go to Settings → Security
3. Find your **Form Submission URL** section
4. Copy the form ID from the URL (e.g., `a7k9m2x5`)

### 2. Create Your HTML Form

```html
<form action="https://querycrm.com/f/YOUR_FORM_ID" method="POST">
  <input type="text" name="name" placeholder="Your Name" required>
  <input type="email" name="email" placeholder="Your Email" required>
  <textarea name="message" placeholder="Your Message" required></textarea>
  <button type="submit">Send Message</button>
</form>
```

### 3. Test Your Form

Open the included `test-form.html` file in your browser and replace `YOUR_FORM_ID_HERE` with your actual form ID.

## Integration Methods

### Method 1: HTML Form (Recommended)

**Pros:**
- Works without JavaScript
- Simple to implement
- SEO friendly
- Works with form validation

**Example:**
```html
<form action="https://querycrm.com/f/q3tai2y4og" method="POST">
  <!-- Required fields -->
  <input type="text" name="name" required>
  <input type="email" name="email" required>
  <textarea name="message" required></textarea>

  <!-- Optional fields -->
  <input type="tel" name="phone">
  <input type="text" name="company">

  <!-- Special fields -->
  <input type="hidden" name="_redirect" value="https://yoursite.com/thank-you">
  <input type="text" name="_honeypot" style="display:none">

  <button type="submit">Send</button>
</form>
```

### Method 2: JavaScript/AJAX

**Pros:**
- No page reload
- Better user experience
- Can handle responses programmatically

**Example:**
```javascript
fetch('https://querycrm.com/f/q3tai2y4og', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: 'John Doe',
    email: '<EMAIL>',
    message: 'Hello!',
    custom_field: 'custom_value'
  })
})
.then(response => response.json())
.then(data => {
  console.log('Success:', data);
  // Handle success
})
.catch(error => {
  console.error('Error:', error);
  // Handle error
});
```

## Field Reference

### Required Fields
- `name` - Customer's full name
- `email` - Customer's email address  
- `message` or `description` - Enquiry message

### Optional Fields
- `phone` - Customer's phone number
- Any custom fields (stored as metadata)

### Special Fields
- `_redirect` - URL to redirect after form submission (HTML forms only)
- `_honeypot` - Hidden spam protection field (leave empty)

## Advanced Features

### Spam Protection

Add a honeypot field to catch spam bots:

```html
<input type="text" name="_honeypot" style="display:none" tabindex="-1" autocomplete="off">
```

### Custom Thank You Page

Redirect users after successful submission:

```html
<input type="hidden" name="_redirect" value="https://yoursite.com/thank-you">
```

### Custom Fields

Any additional fields will be stored as metadata:

```html
<input type="text" name="company" placeholder="Company Name">
<select name="budget">
  <option value="under-1k">Under $1,000</option>
  <option value="1k-5k">$1,000 - $5,000</option>
  <option value="over-5k">Over $5,000</option>
</select>
```

## Response Format

### Success Response (JSON)
```json
{
  "success": true,
  "message": "Form submitted successfully",
  "enquiry_id": "123e4567-e89b-12d3-a456-426614174000",
  "slug": "john-doe-a1b2c3d4"
}
```

### Error Response (JSON)
```json
{
  "error": "Name is required"
}
```

## Security

- Form IDs are short, random strings that don't expose organization information
- Built-in spam protection with honeypot fields
- CORS enabled for cross-origin requests
- Input validation and sanitization
- Rate limiting (if configured)

## Troubleshooting

### Common Issues

1. **Form not found error**
   - Check that your form ID is correct
   - Ensure your organization is active

2. **CORS errors**
   - The API includes CORS headers for cross-origin requests
   - If issues persist, check your domain configuration

3. **Required field errors**
   - Ensure `name`, `email`, and `message` fields are included
   - Check field names match exactly

### Testing

1. Use the included `test-form.html` file
2. Check browser developer tools for network requests
3. Verify enquiries appear in your QueryCRM

## Migration from API Key Method

If you're currently using the API key method, you can:

1. Keep using API keys for server-to-server integration
2. Use form URLs for client-side HTML forms
3. Both methods work simultaneously

## Support

For issues or questions:
1. Check the CRM logs for detailed error messages
2. Verify your form ID in the CRM settings
3. Test with the provided HTML file first

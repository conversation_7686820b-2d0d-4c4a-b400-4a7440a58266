# Form URL Architecture - QueryCRM

## Overview

This document explains the clean form submission URL architecture implemented for QueryCRM, similar to Formspree's approach.

## URL Structure

### Production URLs
```
Main Website:     https://querycrm.com
App Dashboard:    https://app.querycrm.com  
Backend API:      https://api.querycrm.com
Form Submissions: https://querycrm.com/f/{form_id}
```

### Development URLs
```
Landing Page:     http://localhost:3001
App Dashboard:    http://localhost:3000
Backend API:      http://localhost:8080
Form Submissions: http://localhost:3001/f/{form_id}
```

## Architecture

### Domain Routing
- **querycrm.com** → Landing page (Next.js app in `landing_page/` folder)
- **app.querycrm.com** → QueryCRM (Next.js app in `ui/` folder)
- **api.querycrm.com** → Backend API (Go/Echo server in `backend/` folder)

### Form Submission Flow
```
Client Website → https://querycrm.com/f/{form_id} → https://api.querycrm.com/api/public/forms/{form_id}
```

## Implementation Details

### 1. Route Handler Location
**File:** `landing_page/app/f/[form_id]/route.js`

**Why Landing Page?**
- Landing page is served from `querycrm.com` domain
- UI folder is served from `app.querycrm.com` domain
- We want form URLs to use the main `querycrm.com` domain

### 2. Proxy Functionality
The route handler acts as a proxy:
- Receives form submissions at `querycrm.com/f/{form_id}`
- Forwards requests to `api.querycrm.com/api/public/forms/{form_id}`
- Returns backend responses to the client
- Handles CORS headers for cross-origin requests

### 3. Supported Methods
- **POST**: Form submissions (HTML forms and AJAX)
- **GET**: Form information and documentation
- **OPTIONS**: CORS preflight requests

### 4. Content Type Support
- `application/x-www-form-urlencoded` (HTML forms)
- `application/json` (AJAX requests)
- `multipart/form-data` (future file uploads)

## Security Benefits

### 1. Hidden Backend
- Backend API domain (`api.querycrm.com`) is never exposed to client websites
- Clients only see the clean `querycrm.com/f/{form_id}` URL
- Backend infrastructure remains private

### 2. CSRF Protection
- Form submissions are public, stateless endpoints
- No authentication cookies or sessions involved
- Origin validation can be added if needed
- Rate limiting can be implemented at proxy level

### 3. Professional Branding
- All form URLs use the main QueryCRM domain
- Consistent branding across all client integrations
- No exposure of internal API structure

## Client Integration

### HTML Form Example
```html
<form action="https://querycrm.com/f/q3tai2y4og" method="POST">
  <input type="text" name="name" required>
  <input type="email" name="email" required>
  <textarea name="message" required></textarea>
  <button type="submit">Send</button>
</form>
```

### JavaScript/AJAX Example
```javascript
fetch('https://querycrm.com/f/q3tai2y4og', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: 'John Doe',
    email: '<EMAIL>',
    message: 'Hello!'
  })
})
.then(response => response.json())
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error));
```

## Backward Compatibility

### Existing API Endpoints
The original API endpoints remain functional:
- `https://api.querycrm.com/api/public/forms/{form_id}` (still works)
- `https://api.querycrm.com/api/public/enquiries` (API key method)

### Migration Strategy
- New integrations use the clean URLs: `querycrm.com/f/{form_id}`
- Existing integrations can continue using API endpoints
- Gradual migration to new URLs as needed

## Monitoring & Logging

### Request Logging
The proxy logs:
- Form ID and organization lookup
- Client IP and origin information
- Backend response status
- Error conditions

### Analytics Potential
- Track form submission rates per form
- Monitor client origins and usage patterns
- Identify popular forms and organizations
- Debug integration issues

## Future Enhancements

### 1. Custom Domains
Allow organizations to use their own domains:
```
https://forms.clientdomain.com/submit
```

### 2. Advanced Analytics
- Form submission analytics dashboard
- Conversion tracking
- A/B testing capabilities

### 3. Enhanced Security
- Rate limiting per form/IP
- Spam detection algorithms
- Custom validation rules

### 4. Performance Optimization
- CDN integration for global form submissions
- Caching strategies for form metadata
- Load balancing for high-traffic forms

## Testing

### Development Testing
1. Start landing page: `cd landing_page && npm run dev` (port 3001)
2. Start backend: `cd backend && go run main.go` (port 8080)
3. Test form submission: `http://localhost:3001/f/test123`

### Test Files
- `test-form.html` - Basic form submission test
- `test-new-url.html` - Comprehensive testing interface

### Production Testing
1. Deploy landing page to `querycrm.com`
2. Deploy backend to `api.querycrm.com`
3. Test form submissions from external websites

## Deployment Notes

### Environment Variables
```bash
# Landing page environment
NEXT_PUBLIC_API_BASE_URL=https://api.querycrm.com

# Backend environment
CORS_ALLOWED_ORIGINS=https://querycrm.com,https://app.querycrm.com
```

### DNS Configuration
```
querycrm.com        → Landing page server
app.querycrm.com    → UI application server  
api.querycrm.com    → Backend API server
```

This architecture provides a professional, secure, and scalable form submission system that matches industry standards while maintaining complete control over the backend infrastructure.

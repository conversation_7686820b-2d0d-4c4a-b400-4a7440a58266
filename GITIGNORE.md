# .gitignore Configuration Guide

This document explains the .gitignore configuration for the Enquiry Management System project.

## Overview

The project uses a multi-level .gitignore strategy to ensure proper version control hygiene across different components:

1. **Root Level** (`./.gitignore`) - Project-wide patterns
2. **Backend** (`./backend/.gitignore`) - Go/Echo API specific patterns  
3. **Frontend** (`./ui/.gitignore`) - Next.js/React specific patterns

## File Structure

```
enquiry-management-system/
├── .gitignore                 # Project-wide patterns
├── backend/
│   └── .gitignore            # Go/Echo API patterns
└── ui/
    └── .gitignore            # Next.js/React patterns
```

## Root Level .gitignore

**Purpose**: Handles project-wide files and directories that should never be committed.

**Key Categories**:
- Environment files (`.env*`)
- IDE and editor files (`.vscode/`, `.idea/`)
- OS generated files (`.DS_Store`, `Thumbs.db`)
- Docker volumes and override files
- SSL certificates and keys
- Local development scripts
- Infrastructure and deployment files
- Temporary and backup files

## Backend .gitignore (Go/Echo API)

**Purpose**: Handles Go-specific build artifacts, dependencies, and development files.

**Key Categories**:
- Go binaries (`*.exe`, `*.dll`, `*.so`)
- Build outputs (`/bin/`, `/build/`, `main`)
- Go module cache and workspace files
- Test coverage files (`*.cover`, `*.out`)
- Environment files specific to backend
- Database files (`*.db`, `*.sqlite`)
- Air live reload configuration
- Delve debugger files
- Performance profiling files (`*.prof`, `*.pprof`)

**Go-Specific Patterns**:
```gitignore
# Binaries
*.exe
*.dll
*.so
*.dylib

# Build output
/bin/
main
enquiry-management-api

# Go workspace
go.work
go.sum.backup

# Test coverage
*.cover
*.out
```

## Frontend .gitignore (Next.js/React)

**Purpose**: Handles Node.js, Next.js, and React-specific files and build artifacts.

**Key Categories**:
- Node.js dependencies (`node_modules/`)
- Next.js build outputs (`.next/`, `/out/`)
- Package manager logs and cache
- TypeScript build info (`*.tsbuildinfo`)
- Testing artifacts (`/coverage`, `test-results/`)
- Bundle analysis reports
- PWA service worker files
- Storybook build outputs
- Auto-generated API clients

**Next.js-Specific Patterns**:
```gitignore
# Dependencies
/node_modules
/.pnp
.pnp.*

# Next.js
/.next/
/out/
next-env.d.ts

# Production builds
/build
/dist

# TypeScript
*.tsbuildinfo
```

## Environment Files Strategy

All .gitignore files exclude environment files to prevent accidental commit of sensitive data:

```gitignore
# Environment files
.env
.env.local
.env.development
.env.test
.env.production
.env.staging
```

**Best Practices**:
- Use `.env.example` files to document required environment variables
- Never commit actual `.env` files
- Use different environment files for different stages
- Document environment variables in README files

## IDE and Editor Files

All levels exclude common IDE and editor files:

```gitignore
# IDE files
.vscode/
.idea/
*.swp
*.swo
*~
```

**Rationale**:
- IDE settings are personal preferences
- Prevents conflicts between team members using different editors
- Keeps repository clean and focused on source code

## OS-Specific Files

All levels exclude OS-generated files:

```gitignore
# macOS
.DS_Store
._*

# Windows
Thumbs.db
Desktop.ini

# Linux
.directory
.Trash-*
```

## Development and Build Artifacts

Each level excludes relevant build artifacts:

**Backend (Go)**:
- Compiled binaries
- Test coverage files
- Vendor directories (if used)

**Frontend (Node.js/Next.js)**:
- `node_modules/`
- Build outputs (`.next/`, `/build/`)
- Package manager cache

**Project-wide**:
- Docker volumes
- Local development scripts
- Temporary files

## Security Considerations

The .gitignore files help prevent accidental commit of sensitive data:

- Environment files with secrets
- SSL certificates and private keys
- Database files with real data
- Configuration files with credentials
- Local development overrides

## Customization Guidelines

### Adding New Patterns

1. **Determine the appropriate level**:
   - Project-wide → Root `.gitignore`
   - Go-specific → `backend/.gitignore`
   - Node.js-specific → `ui/.gitignore`

2. **Use specific patterns**:
   ```gitignore
   # Good - specific
   *.log
   /build/
   node_modules/
   
   # Avoid - too broad
   *
   temp*
   ```

3. **Add comments for clarity**:
   ```gitignore
   # Build outputs
   /dist/
   /build/
   
   # Environment files
   .env*
   ```

### Common Additions

**For Docker development**:
```gitignore
# Docker
docker-compose.override.yml
.docker/
```

**For testing**:
```gitignore
# Test artifacts
coverage/
test-results/
*.test
```

**For documentation**:
```gitignore
# Generated docs
docs/build/
*.pdf
```

## Verification

To verify .gitignore is working correctly:

```bash
# Check what files Git is tracking
git ls-files

# Check what files would be ignored
git status --ignored

# Test specific file
git check-ignore path/to/file
```

## Troubleshooting

### File Already Tracked

If a file is already tracked by Git, adding it to .gitignore won't ignore it:

```bash
# Remove from tracking but keep local file
git rm --cached filename

# Remove directory from tracking
git rm -r --cached directory/

# Commit the removal
git commit -m "Remove tracked files now in .gitignore"
```

### Global .gitignore

For personal preferences (like specific editor files), consider a global .gitignore:

```bash
# Set global .gitignore
git config --global core.excludesfile ~/.gitignore_global

# Add personal patterns to ~/.gitignore_global
echo ".vscode/" >> ~/.gitignore_global
```

## Best Practices Summary

1. **Layer appropriately** - Use the right .gitignore level for each pattern
2. **Be specific** - Avoid overly broad patterns
3. **Document patterns** - Add comments for complex or project-specific patterns
4. **Test regularly** - Verify .gitignore is working as expected
5. **Review periodically** - Update patterns as project evolves
6. **Coordinate with team** - Ensure team agrees on ignored patterns
7. **Use examples** - Provide `.example` files for configuration templates

## Resources

- [Git Documentation - gitignore](https://git-scm.com/docs/gitignore)
- [GitHub .gitignore Templates](https://github.com/github/gitignore)
- [gitignore.io](https://www.toptal.com/developers/gitignore) - Generate .gitignore files

# User Onboarding Flow

## Overview

This document describes the user onboarding flow for new users who sign up via Supabase but don't have a record in our users table yet.

## Problem Solved

Previously, when a new user signed up via Supabase:
1. They would get a Supabase auth record
2. But no corresponding record in our `users` table
3. The `/api/v1/users/me` endpoint would fail
4. The UI would redirect to login, creating a loop

## Solution

### Backend Implementation

#### New API Endpoint: `POST /api/v1/onboard`

**Purpose**: Onboard new users by creating their organization and user record.

**Authentication**: Requires Supabase JWT token (but not our auth middleware since user doesn't exist in our DB yet).

**Request Body**:
```json
{
  "organization_name": "My Company"
}
```

**Response**:
```json
{
  "user": {
    "id": "uuid",
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "role": "admin",
    "organization_id": "uuid",
    ...
  },
  "organization": {
    "id": "uuid",
    "name": "My Company",
    "slug": "my-company",
    ...
  },
  "message": "Onboarding completed successfully"
}
```

**Process**:
1. Extract user data from Supabase JWT (user ID, email, name)
2. Validate organization name
3. Create organization with unique slug
4. Create user record with admin role (first user is always admin)
5. Return complete user and organization data

#### Files Added/Modified:
- `backend/models/onboard.go` - Request/response models
- `backend/handlers/onboard.go` - Onboarding logic
- `backend/routes/onboard.go` - Route setup
- `backend/main.go` - Added onboarding routes

### Frontend Implementation

#### New Onboarding Page: `/onboard`

**Purpose**: Collect organization name from new users.

**Features**:
- Shows user info from Supabase
- Organization name input with validation
- Benefits list explaining what they'll get
- Calls onboarding API and redirects to organization dashboard

#### Updated Redirect Logic

**Main Page (`/app/page.js`)**:
```javascript
// Old logic
if (!user) redirect('/login')
if (user.org_slug) redirect(`/org/${user.org_slug}`)
else redirect('/login') // This caused the loop

// New logic
if (!basicUser) redirect('/login')
if (basicUser && !completeUser) redirect('/onboard') // NEW
if (completeUser && completeUser.org_slug) redirect(`/org/${completeUser.org_slug}`)
```

**Middleware (`/middleware.js`)**:
- Added `/onboard` as a protected route requiring Supabase authentication
- Prevents unauthenticated access to onboarding

#### Files Added/Modified:
- `ui/app/onboard/page.js` - Onboarding page component
- `ui/lib/api/onboard.js` - API client for onboarding
- `ui/app/page.js` - Updated redirect logic
- `ui/middleware.js` - Added onboard route protection

## User Flow

### New User Journey

1. **User visits app** → Redirected to `/login`
2. **User signs up/in with Google or email** → Supabase creates auth record
3. **Auth callback** → Redirected to `/` (main page)
4. **Main page logic**:
   - Has `basicUser` (Supabase) ✓
   - No `completeUser` (our backend) ✗
   - **Redirected to `/onboard`**
5. **Onboarding page**:
   - Shows user info from Supabase
   - User enters organization name
   - Calls `POST /api/v1/onboard`
6. **Onboarding API**:
   - Creates organization with user as admin
   - Creates user record in our database
   - Returns complete data
7. **Redirect to organization dashboard** → `/org/{slug}`

### Existing User Journey

1. **User visits app** → Redirected to `/login` (if not authenticated)
2. **User signs in** → Supabase auth
3. **Auth callback** → Redirected to `/`
4. **Main page logic**:
   - Has `basicUser` (Supabase) ✓
   - Has `completeUser` (our backend) ✓
   - **Redirected to `/org/{slug}`** (existing flow)

## Key Features

### Security
- First user of an organization is automatically admin
- Organization data isolation maintained
- JWT token validation for onboarding

### User Experience
- Clear onboarding flow with benefits explanation
- No confusing redirect loops
- Immediate access to organization dashboard after onboarding

### Data Integrity
- Unique organization slugs generated automatically
- Transaction-based creation (rollback on failure)
- Proper error handling and validation

## Testing

To test the onboarding flow:

1. **Start the backend**: `make up` (or `cd backend && go run .`)
2. **Start the frontend**: `cd ui && npm run dev`
3. **Clear any existing user data** from Supabase dashboard
4. **Sign up with a new email** or Google account
5. **Verify redirect to `/onboard`**
6. **Complete onboarding** with organization name
7. **Verify redirect to organization dashboard**

## Environment Variables

Make sure these are set in your `.env` file:

```env
# Supabase (for frontend)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# API URL (for frontend)
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080

# JWT (for backend)
JWT_SECRET=your_jwt_secret
JWT_ISSUER=your_supabase_url
```

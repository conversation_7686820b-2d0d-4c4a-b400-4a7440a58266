# Organization Security & Data Isolation

## 🚨 Security Issue Identified & Resolved

### **Problem:**
The original implementation had a critical security vulnerability where users could potentially access other organizations' data by manipulating the organization slug in the URL.

**Example Attack Vector:**
```
User from Organization A: /org/company-a/enquiries
Attacker changes URL to: /org/company-b/enquiries
Without proper validation, could access Company B's data
```

### **Root Cause:**
1. **Frontend passed org slug in URL** but backend didn't validate it against user's actual organization
2. **Backend relied only on JWT user context** without cross-referencing the requested organization
3. **No validation of organization context** in API requests

## 🔒 Security Solution Implemented

### **Dual Validation Approach:**

#### 1. **Frontend Organization Validation**
- **OrganizationContext** validates URL slug matches user's organization
- **API utilities** include organization validation in all requests
- **Automatic organization ID injection** in API calls

#### 2. **Backend Organization Validation**
- **Organization validation middleware** on all protected routes
- **Organization-scoped database queries** for all data access
- **Request organization validation** for API calls with org parameters

## 🛡️ Implementation Details

### **Backend Security Layers**

#### **Middleware Stack:**
```go
// All protected routes now include organization validation
protected := api.Group("", 
    middleware.RequireAuth(cfg, db),           // 1. Authentication
    middleware.RequireOrganizationValidation() // 2. Organization validation
)
```

#### **Organization Validation Middleware:**
```go
// Validates org slug in URL matches user's organization
func ValidateOrganizationSlug(c echo.Context) error {
    authUser := c.Get("user").(*models.AuthUser)
    orgSlug := c.Param("orgSlug")
    
    if orgSlug != "" && orgSlug != authUser.OrgSlug {
        return echo.NewHTTPError(403, "Access denied: organization mismatch")
    }
    return nil
}
```

#### **Organization-Scoped Database Queries:**
```go
// All handlers now use organization-scoped DB
func (h *EnquiryHandler) GetEnquiries(c echo.Context) error {
    // This automatically filters by user's organization
    db := middleware.GetOrganizationScopedDB(c, h.db)
    query := db.Model(&models.Enquiry{})
    // ... rest of query
}
```

### **Frontend Security Layers**

#### **Organization Context Validation:**
```javascript
// OrganizationContext validates URL slug
useEffect(() => {
    if (orgSlug && orgSlug !== user.org_slug) {
        setError('Access denied to this organization');
        // Redirect to user's correct organization
        router.replace(`/org/${user.org_slug}`);
    }
}, [user, orgSlug]);
```

#### **API Request Validation:**
```javascript
// All API calls include organization validation
export async function makeOrganizationValidatedRequest(endpoint, options, orgSlug) {
    // 1. Validate org slug matches user's organization
    const userOrg = await validateOrganizationContext(orgSlug);
    
    // 2. Add organization ID to request for backend validation
    const url = new URL(`${API_BASE_URL}${endpoint}`);
    url.searchParams.append('org_id', userOrg.id);
    
    return fetch(url.toString(), options);
}
```

## 🔐 Security Features

### **1. URL Manipulation Protection**
- **Frontend:** Automatic redirect if URL org doesn't match user's org
- **Backend:** 403 error if org slug in request doesn't match user's org

### **2. API Parameter Validation**
- **Frontend:** Organization ID automatically added to all requests
- **Backend:** Validates org_id parameter matches user's organization

### **3. Database Query Isolation**
- **All queries automatically scoped** to user's organization
- **No cross-organization data leakage** possible
- **Organization ID enforced** at database level

### **4. Request Body Validation**
- **Organization ID always taken from JWT** (not request body)
- **No trust in client-provided organization data**
- **Server-side organization assignment**

## 📋 Updated API Behavior

### **Before (Vulnerable):**
```javascript
// Frontend could send any org data
fetch('/api/v1/enquiries', {
    body: JSON.stringify({ organization_id: 'any-org-id' })
});

// Backend trusted the request
enquiry.OrganizationID = req.OrganizationID; // VULNERABLE!
```

### **After (Secure):**
```javascript
// Frontend includes validation
const response = await makeOrganizationValidatedRequest('/api/v1/enquiries', {
    method: 'POST',
    body: JSON.stringify(enquiryData)
}, currentOrgSlug);

// Backend enforces user's organization
enquiry.OrganizationID = user.OrganizationID; // SECURE!
```

## 🧪 Testing Security

### **Test Cases:**

1. **URL Manipulation Test:**
   ```
   1. Login as user from Organization A
   2. Try to access /org/organization-b/enquiries
   3. Should redirect to /org/organization-a/enquiries
   ```

2. **API Parameter Manipulation:**
   ```
   1. Make API call with different org_id parameter
   2. Should return 403 Forbidden
   ```

3. **Cross-Organization Data Access:**
   ```
   1. Try to access enquiry ID from different organization
   2. Should return 404 Not Found (not 403 to avoid info disclosure)
   ```

## 🚀 Migration Impact

### **Routes Updated:**
- ✅ `/api/v1/enquiries/*` - All enquiry endpoints
- ✅ `/api/v1/users/*` - All user endpoints  
- ✅ `/api/v1/team/*` - Team management endpoints
- ✅ `/api/v1/dashboard/*` - Dashboard endpoints
- ✅ `/api/v1/organizations/*` - Organization endpoints

### **Handlers Updated:**
- ✅ **EnquiryHandler** - Organization-scoped queries
- ✅ **UserHandler** - Organization validation
- ✅ **DashboardHandler** - Organization isolation
- ✅ **OrganizationHandler** - Self-organization access only

### **Frontend Updated:**
- ✅ **API utilities** - Organization validation helpers
- ✅ **OrganizationContext** - URL validation
- ✅ **All API calls** - Organization-aware requests

## 🔍 Security Checklist

- ✅ **URL slug validation** - Frontend redirects on mismatch
- ✅ **API parameter validation** - Backend validates org_id
- ✅ **Database query scoping** - All queries organization-filtered
- ✅ **JWT organization enforcement** - Server-side org assignment
- ✅ **Cross-organization access prevention** - Multiple validation layers
- ✅ **Information disclosure prevention** - 404 instead of 403 for missing resources
- ✅ **Middleware protection** - All routes protected
- ✅ **Request body validation** - No client-provided org data trusted

## 🎯 Result

**Before:** Users could potentially access other organizations' data through URL/parameter manipulation.

**After:** Complete organization data isolation with multiple validation layers ensuring users can only access their own organization's data.

The system now implements **defense in depth** with validation at:
1. **Frontend URL level**
2. **Frontend API level** 
3. **Backend middleware level**
4. **Backend handler level**
5. **Database query level**

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build output
/bin/
main
enquiry-management-api

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Database files
*.db
*.sqlite

# Air live reload
.air.toml
tmp/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test coverage
*.cover
coverage.html

# Delve debugger
__debug_bin

# Performance profiling
*.prof
*.pprof

# Enquiry Management System - Backend Makefile

.PHONY: help build run test clean up down logs

# Default target
help:
	@echo "Available commands:"
	@echo "  make build    - Build the Go application"
	@echo "  make run      - Run the application locally"
	@echo "  make test     - Run tests"
	@echo "  make clean    - Clean build artifacts"
	@echo "  make up       - Start Docker containers"
	@echo "  make down     - Stop Docker containers"
	@echo "  make logs     - View Docker logs"

# Build the Go application
build:
	go build -o bin/main .

# Run the application locally
run:
	go run .

# Run tests
test:
	go test ./...

# Clean build artifacts
clean:
	rm -rf bin/
	go clean

# Start Docker containers
up:
	docker-compose up -d

# Stop Docker containers
down:
	docker-compose down

# View Docker logs
logs:
	docker-compose logs -f

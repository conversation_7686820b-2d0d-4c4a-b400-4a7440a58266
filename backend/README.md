# Enquiry Management System - Backend API

A minimal Go backend API using Echo framework and PostgreSQL with GORM.

## Tech Stack

- **Language**: Go 1.21
- **Framework**: Echo v4
- **Database**: PostgreSQL
- **ORM**: GORM
- **Containerization**: Docker & Docker Compose

## Project Structure

```
backend/
├── config/          # Configuration management
├── database/        # Database connection and setup
├── handlers/        # HTTP request handlers (empty - to be implemented)
├── models/          # Database models (empty - to be implemented)
├── routes/          # Route definitions (empty - to be implemented)
├── main.go          # Application entry point
├── Dockerfile       # Docker configuration
├── docker-compose.yml # Docker services
├── Makefile         # Build and deployment commands
└── .env.example     # Environment variables template
```

## Quick Start

### Prerequisites

- Go 1.21 or higher
- Docker and Docker Compose
- PostgreSQL (if running locally without Docker)

### Environment Setup

1. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

2. Update the `.env` file with your configuration.

### Running with Docker (Recommended)

1. Start the services:
   ```bash
   make up
   ```

2. Check the logs:
   ```bash
   make logs
   ```

3. Stop the services:
   ```bash
   make down
   ```

### Running Locally

1. Install dependencies:
   ```bash
   go mod download
   ```

2. Run the application:
   ```bash
   make run
   ```

## Available Endpoints

- `GET /health` - Health check endpoint

## Development

### Building

```bash
make build
```

### Testing

```bash
make test
```

### Cleaning

```bash
make clean
```

## API Documentation

API documentation will be added when endpoints are implemented.

## Database

The application uses PostgreSQL with GORM. Database migrations will be handled automatically when models are added.

## Docker Services

- **postgres**: PostgreSQL database (port 5432)
- **api**: Go API server (port 8080)

## Environment Variables

See `.env.example` for all available configuration options.

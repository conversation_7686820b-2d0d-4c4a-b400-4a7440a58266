# Testing Team Member Invitations

This guide shows you how to test the invitation system without needing to check emails.

## 🚀 Quick Test Setup

### 1. Start the Backend
```bash
cd backend
./enquiry-management-api
```

### 2. Start the Frontend
```bash
cd ui
npm run dev
```

### 3. Run the Database Migration
```bash
# Make sure your database is running, then:
psql $DATABASE_URL -f migrations/005_create_invitations_table.sql
```

## 📧 Testing Without Email

When you create a team member, the backend will now log the invitation URL directly to the console!

### Expected Console Output

When you create a team member via the API or UI, you'll see:

```bash
🔗 INVITATION URL FOR TESTING:
http://localhost:3000/invite/accept?token=abc123def456...

📧 Preparing to send invitation email to: <EMAIL>

# If SendGrid is configured:
✅ Invitation email sent <NAME_EMAIL>
💡 You can also use the invitation URL above for direct testing

# If SendGrid is NOT configured:
⚠️  Email service not configured, skipping invitation <NAME_EMAIL>
💡 Use the invitation URL above to test the invitation flow
```

### How to Test

1. **Create a team member** (as admin via API or UI)
2. **Copy the logged URL** from the backend console
3. **Paste it in your browser** to test the invitation flow
4. **Complete the signup process** to verify everything works

## 🧪 Test Scenarios

### Scenario 1: Valid Invitation
```bash
# Create team member
POST /api/v1/team/members
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "role": "agent"
}

# Copy URL from console logs
# Visit: http://localhost:3000/invite/accept?token=...
# Expected: Beautiful onboarding form with organization info
```

### Scenario 2: Expired Invitation
```bash
# Manually update invitation in database to be expired
UPDATE invitations SET expires_at = NOW() - INTERVAL '1 day' WHERE token = 'your-token';

# Visit the URL
# Expected: "Invitation Expired" error page
```

### Scenario 3: Already Accepted
```bash
# Accept an invitation first, then visit the same URL again
# Expected: "Already Accepted" error page with login link
```

### Scenario 4: Invalid Token
```bash
# Visit: http://localhost:3000/invite/accept?token=invalid-token
# Expected: "Invalid Invitation" error page
```

### Scenario 5: Rate Limiting
```bash
# Make 11+ requests to the verify endpoint within 1 minute
# Expected: "Too Many Requests" error page
```

### Scenario 6: New Onboard Endpoint (Recommended)
```bash
# After user completes Supabase authentication, test the new onboard endpoint
POST /api/v1/invitations/onboard
{
  "invitation_token": "abc123def456...",
  "auth_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "name": "John Doe"
}

# Expected: Success response with user and organization data
# User status changes to 'active', auth_id is set to Supabase user ID
```

## 🔍 What to Look For

### Backend Logs
```bash
[AUTH] Starting authentication for POST /api/v1/team/members
[TeamAPI] Creating team member...
🔗 INVITATION URL FOR TESTING:
http://localhost:3000/invite/accept?token=abc123...
📧 Preparing to send invitation email to: <EMAIL>
✅ Invitation email sent <NAME_EMAIL>
```

### Frontend Console (Browser DevTools)
```bash
[InvitationAPI] Starting verifyInvitation request with token: abc123...
[InvitationAPI] Response status: 200
[InvitationAPI] Response data: {valid: true, invitation: {...}}
```

### Database Changes
```sql
-- Check invitation record
SELECT * FROM invitations WHERE email = '<EMAIL>';

-- Check user status
SELECT * FROM users WHERE email = '<EMAIL>';
-- Should show status = 'pending' initially, then 'active' after acceptance
```

## 🎯 Complete Test Flow

### Step 1: Create Invitation
```bash
# As admin, create team member
curl -X POST http://localhost:8080/api/v1/team/members \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>", 
    "role": "agent"
  }'
```

### Step 2: Copy URL from Logs
```bash
# Look for this in backend console:
🔗 INVITATION URL FOR TESTING:
http://localhost:3000/invite/accept?token=abc123def456...
```

### Step 3: Test Frontend
```bash
# Visit the URL in browser
# Should see:
# 1. Loading spinner (brief)
# 2. Beautiful onboarding form with:
#    - Organization name
#    - Role badge
#    - Pre-filled email
#    - Name input field
#    - Google signin button
#    - Email/password form
```

### Step 4: Complete Signup
```bash
# Fill in name: "Test User"
# Choose either:
# - Google signin (will redirect to Google OAuth)
# - Email/password (create password, submit form)

# On success:
# - User status changes to 'active' in database
# - Invitation status changes to 'accepted'
# - Redirect to login page or dashboard
```

## 🆕 Testing the New Onboard Endpoint

The new `/api/v1/invitations/onboard` endpoint provides better integration with Supabase authentication.

### Step 1: Get Invitation Token
```bash
# Create team member and copy token from console logs (same as above)
```

### Step 2: Complete Supabase Authentication
```bash
# User signs up with Supabase (Google or email/password)
# Frontend gets Supabase JWT token from session
```

### Step 3: Test Onboard Endpoint
```bash
curl -X POST http://localhost:8080/api/v1/invitations/onboard \
  -H "Content-Type: application/json" \
  -d '{
    "invitation_token": "your-invitation-token-here",
    "supabase_token": "your-supabase-jwt-token-here",
    "name": "John Doe"
  }'
```

### Expected Success Response
```json
{
  "success": true,
  "user": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "agent"
  },
  "organization": {
    "id": "789e0123-e89b-12d3-a456-************",
    "name": "Acme Corp",
    "slug": "acme-corp"
  },
  "redirect_url": "/org/acme-corp/dashboard"
}
```

### Error Testing Scenarios

#### Email Mismatch
```bash
# Use Supabase token with different email than invitation
# Expected: 400 error with "email_mismatch"
```

#### Invalid Supabase Token
```bash
# Use expired or malformed Supabase token
# Expected: 401 error with "invalid_supabase_token"
```

#### Duplicate User Linking
```bash
# Try to link same Supabase user ID to multiple accounts
# Expected: 409 error with "user_already_linked"
```

#### Already Processed Invitation
```bash
# Use invitation token that was already accepted
# Expected: 400 error with "invitation_not_pending"
```

## 🐛 Troubleshooting

### No URL in Logs?
- Check that you're creating team members as an admin user
- Verify the backend is running and processing the request
- Check for any error messages in the logs

### Frontend Not Loading?
- Verify frontend is running on http://localhost:3000
- Check browser console for JavaScript errors
- Ensure the token parameter is in the URL

### Database Errors?
- Run the migration: `psql $DATABASE_URL -f migrations/005_create_invitations_table.sql`
- Check database connection in backend logs
- Verify user has proper permissions

### API Errors?
- Check JWT token is valid and user is admin
- Verify organization context is correct
- Check rate limiting (wait 1 minute if hit limit)

## 📊 Success Indicators

✅ **Backend**: URL logged to console  
✅ **Frontend**: Onboarding form displays  
✅ **Database**: User status = 'pending', then 'active'  
✅ **Email**: Sent successfully (if configured)  
✅ **Signup**: User can complete registration  
✅ **Login**: User can sign in after acceptance  

---

## 🎉 Ready to Test!

The invitation system is fully functional. Use the logged URLs to test the complete flow without needing to check emails. This makes development and testing much faster and easier!

**Pro Tip**: Keep the backend console visible while testing so you can quickly copy invitation URLs as you create them.

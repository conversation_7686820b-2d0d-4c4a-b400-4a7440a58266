package config

import (
	"fmt"
	"log"
	"net/url"
	"os"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
)

type Config struct {
	Port     string
	Database DatabaseConfig
	Server   ServerConfig
	JWT      JWTConfig
	Email    EmailConfig
	App      AppConfig
}

type DatabaseConfig struct {
	// Required fields
	Host     string `validate:"required"`
	Port     int    `validate:"required,min=1,max=65535"`
	User     string `validate:"required"`
	Password string `validate:"required"`
	Name     string `validate:"required"`

	// Optional fields
	SSLMode         string // Optional: defaults to "disable"
	MaxOpenConns    int    // Optional: defaults to 25
	MaxIdleConns    int    // Optional: defaults to 5
	ConnMaxLifetime string // Optional: defaults to "300s"
	ConnMaxIdleTime string // Optional: defaults to "60s"
}

type ServerConfig struct {
	Env string
}

type JWTConfig struct {
	Secret    string
	Issuer    string
	Audience  string
	Algorithm string
}

type EmailConfig struct {
	ZeptoAPIKey string // Changed from SendGridAPIKey
	FromEmail   string
	FromName    string
	APIBaseURL  string // Added for ZeptoMail API endpoint
}

type AppConfig struct {
	BaseURL string
}

func Load() *Config {
	// Load .env file if it exists
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	// Parse database URL
	databaseURL := getEnv("DATABASE_URL", "postgres://user:password@localhost:5432/enquiry_db?sslmode=disable")
	dbConfig := parseDatabaseURL(databaseURL)

	// Add connection pool settings from environment
	dbConfig.MaxOpenConns = getEnvInt("DB_MAX_OPEN_CONNS", 25)
	dbConfig.MaxIdleConns = getEnvInt("DB_MAX_IDLE_CONNS", 5)
	dbConfig.ConnMaxLifetime = getEnv("DB_CONN_MAX_LIFETIME", "300s")
	dbConfig.ConnMaxIdleTime = getEnv("DB_CONN_MAX_IDLE_TIME", "60s")

	// Validate required database fields
	if err := validateDatabaseConfig(dbConfig); err != nil {
		log.Fatalf("Database configuration validation failed: %v", err)
	}

	config := &Config{
		Port:     getEnv("PORT", "8080"),
		Database: dbConfig,
		Server: ServerConfig{
			Env: getEnv("ENVIRONMENT", "development"),
		},
		JWT: JWTConfig{
			Secret:    getEnv("JWT_SECRET", "your-super-secret-jwt-key-change-in-production"),
			Issuer:    getEnv("JWT_ISSUER", "enquiry-management-system"),
			Audience:  getEnv("JWT_AUDIENCE", "enquiry-management-api"),
			Algorithm: getEnv("JWT_ALGORITHM", "HS256"),
		},
		Email: EmailConfig{
			ZeptoAPIKey: getEnv("ZEPTO_API_KEY", ""),
			FromEmail:   getEnv("ZEPTO_FROM_EMAIL", "<EMAIL>"),
			FromName:    getEnv("ZEPTO_FROM_NAME", "QueryCRM"),
			APIBaseURL:  getEnv("ZEPTO_API_BASE_URL", "https://api.zeptomail.in/v1.1"),
		},
		App: AppConfig{
			BaseURL: getEnv("APP_BASE_URL", "http://localhost:3000"),
		},
	}

	return config
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
		log.Printf("Invalid integer value for %s: %s, using default: %d", key, value, defaultValue)
	}
	return defaultValue
}

func parseDatabaseURL(databaseURL string) DatabaseConfig {
	// Parse the database URL
	u, err := url.Parse(databaseURL)
	if err != nil {
		log.Printf("Error parsing database URL: %v, using defaults", err)
		return DatabaseConfig{
			Host:     "localhost",
			Port:     5432,
			User:     "user",
			Password: "password",
			Name:     "enquiry_db",
			SSLMode:  "disable",
		}
	}

	// Extract components
	host := u.Hostname()
	if host == "" {
		host = "localhost"
	}

	port := 5432
	if u.Port() != "" {
		if p, err := strconv.Atoi(u.Port()); err == nil {
			port = p
		}
	}

	user := "user"
	password := "password"
	if u.User != nil {
		user = u.User.Username()
		if p, ok := u.User.Password(); ok {
			password = p
		}
	}

	dbName := strings.TrimPrefix(u.Path, "/")
	if dbName == "" {
		dbName = "enquiry_db"
	}

	sslMode := "disable"
	if u.Query().Get("sslmode") != "" {
		sslMode = u.Query().Get("sslmode")
	}

	return DatabaseConfig{
		Host:            host,
		Port:            port,
		User:            user,
		Password:        password,
		Name:            dbName,
		SSLMode:         sslMode,
		MaxOpenConns:    25,     // Default values, will be overridden by env vars
		MaxIdleConns:    5,      // Default values, will be overridden by env vars
		ConnMaxLifetime: "300s", // Default values, will be overridden by env vars
		ConnMaxIdleTime: "60s",  // Default values, will be overridden by env vars
	}
}

func validateDatabaseConfig(config DatabaseConfig) error {
	// Validate required fields
	if strings.TrimSpace(config.Host) == "" {
		return fmt.Errorf("database host is required")
	}
	if config.Port <= 0 || config.Port > 65535 {
		return fmt.Errorf("database port must be between 1 and 65535, got: %d", config.Port)
	}
	if strings.TrimSpace(config.User) == "" {
		return fmt.Errorf("database user is required")
	}
	if strings.TrimSpace(config.Password) == "" {
		return fmt.Errorf("database password is required")
	}
	if strings.TrimSpace(config.Name) == "" {
		return fmt.Errorf("database name is required")
	}

	// Set defaults for optional fields
	if config.SSLMode == "" {
		config.SSLMode = "disable"
	}
	if config.MaxOpenConns <= 0 {
		config.MaxOpenConns = 25
	}
	if config.MaxIdleConns <= 0 {
		config.MaxIdleConns = 5
	}
	if config.ConnMaxLifetime == "" {
		config.ConnMaxLifetime = "300s"
	}
	if config.ConnMaxIdleTime == "" {
		config.ConnMaxIdleTime = "60s"
	}

	return nil
}

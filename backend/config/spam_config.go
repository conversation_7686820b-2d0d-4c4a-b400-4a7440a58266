package config

import (
	"os"
	"strconv"
)

// SpamConfig holds configuration for spam filtering
type SpamConfig struct {
	Enabled   bool    `json:"enabled"`
	Threshold float64 `json:"threshold"`
	APIKey    string  `json:"-"` // Don't expose in JSON
	Model     string  `json:"model"`
}

// GetSpamConfig returns the spam filtering configuration from environment variables
func GetSpamConfig() *SpamConfig {
	config := &SpamConfig{
		Enabled:   true,
		Threshold: 0.5,
		APIKey:    os.Getenv("OPENAI_API_KEY"),
		Model:     "gpt-4o-mini",
	}

	// Parse enabled flag
	if envEnabled := os.Getenv("SPAM_DETECTION_ENABLED"); envEnabled != "" {
		if parsed, err := strconv.ParseBool(envEnabled); err == nil {
			config.Enabled = parsed
		}
	}

	// Parse threshold
	if envThreshold := os.Getenv("SPAM_THRESHOLD"); envThreshold != "" {
		if parsed, err := strconv.ParseFloat(envThreshold, 64); err == nil {
			config.Threshold = parsed
		}
	}

	// Parse model
	if envModel := os.Getenv("GPT_MODEL"); envModel != "" {
		config.Model = envModel
	}

	return config
}

// IsConfigured returns true if the spam filtering is properly configured
func (c *SpamConfig) IsConfigured() bool {
	return c.APIKey != "" && c.Enabled
}

// GetRequiredEnvVars returns a list of required environment variables for spam filtering
func GetRequiredEnvVars() []string {
	return []string{
		"OPENAI_API_KEY",
	}
}

// GetOptionalEnvVars returns a list of optional environment variables for spam filtering
func GetOptionalEnvVars() map[string]string {
	return map[string]string{
		"SPAM_DETECTION_ENABLED": "true/false - Enable or disable spam detection (default: true)",
		"SPAM_THRESHOLD":         "0.0-1.0 - Threshold for spam classification (default: 0.5)",
		"GPT_MODEL":              "OpenAI model to use for spam detection (default: gpt-4o-mini)",
	}
}

version: '3.8'

services:
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: enquiry_postgres
  #   environment:
  #     POSTGRES_USER: ${POSTGRES_USER:-user}
  #     POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
  #     POSTGRES_DB: ${POSTGRES_DB:-enquiry_db}
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-user}"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  api:
    build: .
    container_name: enquiry_api
    environment:
      PORT: 8080
      DATABASE_URL: postgres://${POSTGRES_USER:-user}:${POSTGRES_PASSWORD:-password}@postgres:5432/${POSTGRES_DB:-enquiry_db}?sslmode=disable
      ENVIRONMENT: development
    ports:
      - "8080:8080"
    # depends_on:
    #   postgres:
    #     condition: service_healthy
    restart: unless-stopped

volumes:
  postgres_data:

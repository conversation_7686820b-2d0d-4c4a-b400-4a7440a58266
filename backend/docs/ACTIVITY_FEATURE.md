# Activity Timeline Feature

## Overview

The Activity Timeline feature provides comprehensive tracking of all actions performed on enquiries. It automatically creates activity records when users interact with enquiries and notes, providing a complete audit trail and timeline view.

## Features

### Automatic Activity Tracking
- **Enquiry Creation**: Tracks when new enquiries are submitted
- **Status Changes**: Records status transitions (New → In Progress → Closed)
- **Priority Changes**: Logs priority adjustments (High/Medium/Low)
- **Assignment Changes**: Tracks when enquiries are assigned/unassigned
- **Note Management**: Records note additions and deletions
- **General Updates**: Captures other enquiry field modifications

### Rich Activity Data
- **Timestamps**: Precise creation and update times
- **User Attribution**: Links activities to the users who performed them
- **Detailed Descriptions**: Human-readable activity descriptions
- **Metadata**: JSON-formatted additional context for complex changes
- **Type Classification**: Categorized activity types for filtering and display

## Database Schema

### Activities Table
```sql
CREATE TABLE activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    enquiry_id UUID NOT NULL REFERENCES enquiries(id),
    user_id UUID NOT NULL REFERENCES users(id),
    type VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

### Indexes
- `idx_activities_enquiry_id` - Fast enquiry activity lookups
- `idx_activities_user_id` - User activity queries
- `idx_activities_type` - Activity type filtering
- `idx_activities_enquiry_created_at` - Optimized timeline queries

## API Endpoints

### Get Activity Timeline
```
GET /api/v1/enquiries/{enquiry_id}/activities
```

Returns all activities for an enquiry, ordered by creation date (newest first).

**Response Format:**
```json
[
  {
    "id": "uuid",
    "enquiry_id": "uuid",
    "user_id": "uuid",
    "type": "Activity Type",
    "description": "Human readable description",
    "metadata": "JSON string with additional context",
    "created_at": "ISO 8601 timestamp",
    "updated_at": "ISO 8601 timestamp",
    "user": {
      "id": "uuid",
      "name": "User Name",
      "email": "<EMAIL>",
      "role": "agent"
    }
  }
]
```

## Activity Types

| Type | Description | Metadata Example |
|------|-------------|------------------|
| `Enquiry Created` | New enquiry submitted | `{}` |
| `Status Changed` | Status transition | `{"old_status":"New","new_status":"In Progress"}` |
| `Priority Changed` | Priority modification | `{"old_priority":"Medium","new_priority":"High"}` |
| `Assignment Changed` | Assignment update | `{"old_assignee":"John","new_assignee":"Jane"}` |
| `Note Added` | Note creation | `{"note_content":"Customer called..."}` |
| `Note Deleted` | Note removal | `{}` |
| `Enquiry Updated` | General updates | `{"changes":"status, priority"}` |

## Implementation Details

### Automatic Tracking
Activities are automatically created through handler methods:

```go
// In enquiry creation
activityHandler.TrackEnquiryCreated(enquiry.ID, userID, enquiry.Name)

// In status updates
activityHandler.TrackStatusChanged(enquiry.ID, userID, oldStatus, newStatus)

// In note creation
activityHandler.TrackNoteAdded(enquiry.ID, userID, noteContent)
```

### User Context
Currently uses a placeholder system user ID (`00000000-0000-0000-0000-000000000000`) since authentication is not implemented. This will be replaced with actual user context when authentication is added.

### Error Handling
- Validates enquiry existence before creating activities
- Graceful handling of missing user information
- Proper HTTP error responses for invalid requests

## Frontend Integration

The activity data is designed to work with the existing `EnquiryActivity` React component:

```javascript
// Fetch activities
const response = await fetch(`/api/v1/enquiries/${enquiryId}/activities`);
const activities = await response.json();

// Use with existing component
<EnquiryActivity activities={activities} />
```

The API response format matches the expected component props structure.

## Testing

Comprehensive test suite includes:
- Activity retrieval for valid enquiries
- Error handling for invalid enquiry IDs
- Helper function validation
- Database integration testing
- Activity ordering verification

Run tests with:
```bash
cd backend
go test ./handlers -v
```

## Migration

To set up the activities table:

```bash
# Run the migration
psql -d your_database -f backend/migrations/002_create_activities_table.sql
```

## Future Enhancements

### When Authentication is Implemented
- Replace placeholder user ID with actual authenticated user
- Add user permission checks for activity access
- Implement user-specific activity filtering

### Potential Features
- Activity filtering by type and date range
- Activity search functionality
- Bulk activity operations
- Activity export capabilities
- Real-time activity notifications
- Activity templates for common actions

## Performance Considerations

- Activities are indexed for fast enquiry-based queries
- Metadata stored as JSONB for efficient querying
- Soft deletes maintain audit trail integrity
- Pagination support for large activity sets (future enhancement)

## Security Notes

- Activities provide audit trail for compliance
- Soft deletes preserve historical data
- User attribution enables accountability
- Metadata sanitization prevents injection attacks

# JWT Authentication & Role-Based Access Control

## Overview
This system implements JWT-based authentication with role-based access control (RBAC). It supports any JWT provider and validates tokens against configurable parameters.

## Configuration

### Environment Variables
Add these to your `.env` file:

```env
# JWT Authentication Settings
JWT_SECRET=your-super-secret-jwt-key-change-in-production-min-32-chars
JWT_ISSUER=enquiry-management-system
JWT_AUDIENCE=enquiry-management-api
JWT_ALGORITHM=HS256
```

### Configuration Fields
- `JWT_SECRET`: Secret key for token validation (minimum 32 characters recommended)
- `JWT_ISSUER`: Expected token issuer (optional, leave empty to skip validation)
- `JWT_AUDIENCE`: Expected token audience (optional, leave empty to skip validation)
- `JWT_ALGORITHM`: Signing algorithm (HS256, HS384, HS512, RS256, etc.)

## JWT Token Format

### Required Claims
Your JWT tokens must include these claims:

```json
{
  "user_id": "123e4567-e89b-12d3-a456-************",
  "email": "<EMAIL>",
  "role": "admin",
  "name": "John Doe",
  "sub": "123e4567-e89b-12d3-a456-************",
  "iss": "your-auth-provider",
  "aud": "enquiry-management-api",
  "exp": **********,
  "iat": **********,
  "nbf": **********
}
```

### Claim Descriptions
- `user_id`: UUID of the user in the database
- `email`: User's email address
- `role`: User's role (admin, manager, agent)
- `name`: User's display name
- `sub`: Subject (typically same as user_id)
- `iss`: Issuer (validated if JWT_ISSUER is set)
- `aud`: Audience (validated if JWT_AUDIENCE is set)
- `exp`: Expiration timestamp
- `iat`: Issued at timestamp
- `nbf`: Not before timestamp

## User Roles

### Available Roles
- **admin**: Full system access, can manage users and settings
- **manager**: Can view team members and manage enquiries
- **agent**: Can view and update assigned enquiries

### Role Hierarchy
```
admin > manager > agent
```

## Authentication Flow

### 1. Token Validation
```
1. Extract token from Authorization header: "Bearer <token>"
2. Parse JWT and validate signature using JWT_SECRET
3. Validate issuer and audience (if configured)
4. Check token expiration
5. Extract user claims
6. Verify user exists in database and is active
7. Store user context for request
```

### 2. Request Headers
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

## Middleware Usage

### Basic Authentication
```go
// Require any authenticated user
protected := api.Group("", middleware.RequireAuth(cfg, db))
protected.GET("/profile", userHandler.GetProfile)
```

### Role-Based Access
```go
// Admin only
adminOnly := api.Group("/admin", middleware.RequireAdmin(cfg, db))
adminOnly.POST("/users", userHandler.CreateUser)

// Manager or Admin
teamView := api.Group("/team", middleware.RequireAdminOrManager(cfg, db))
teamView.GET("/members", userHandler.GetTeamMembers)

// Specific roles
multiRole := api.Group("/reports", middleware.RequireRole(cfg, db, "admin", "manager"))
multiRole.GET("/analytics", reportHandler.GetAnalytics)
```

### Available Middleware Functions
- `RequireAuth(cfg, db)` - Basic authentication required
- `RequireAdmin(cfg, db)` - Admin role required
- `RequireManager(cfg, db)` - Manager role required
- `RequireAgent(cfg, db)` - Agent role required
- `RequireAdminOrManager(cfg, db)` - Admin or Manager role required
- `RequireRole(cfg, db, roles...)` - Any of specified roles required

## Handler Implementation

### Getting Authenticated User
```go
func MyHandler(c echo.Context) error {
    // Get authenticated user from context
    user := middleware.GetAuthUser(c)
    if user == nil {
        return echo.NewHTTPError(401, "User not authenticated")
    }

    // Use user information
    userID := user.ID
    userRole := user.Role
    userName := user.Name
    userEmail := user.Email

    // Role checks
    if user.IsAdmin() {
        // Admin-specific logic
    } else if user.IsManager() {
        // Manager-specific logic
    } else if user.IsAgent() {
        // Agent-specific logic
    }

    return c.JSON(200, map[string]interface{}{
        "user_id": userID,
        "message": "Hello " + userName,
    })
}
```

### User Context Methods
```go
user := middleware.GetAuthUser(c)

// Role checking methods
user.IsAdmin()                    // true if role is "admin"
user.IsManager()                  // true if role is "manager"
user.IsAgent()                    // true if role is "agent"
user.IsAdminOrManager()           // true if role is "admin" or "manager"
user.HasRole("admin")             // true if role matches
user.HasAnyRole("admin", "manager") // true if role matches any
```

## Error Responses

### Authentication Errors
```json
// Missing token
{
  "code": "missing_token",
  "message": "Authorization token is required"
}

// Invalid token
{
  "code": "invalid_token", 
  "message": "Invalid or malformed token"
}

// Expired token
{
  "code": "expired_token",
  "message": "Token has expired"
}

// User not found
{
  "code": "user_not_found",
  "message": "User not found in database"
}

// Insufficient permissions
{
  "code": "insufficient_role",
  "message": "Insufficient permissions for this action"
}
```

### HTTP Status Codes
- `401 Unauthorized`: Authentication required or failed
- `403 Forbidden`: Insufficient permissions
- `500 Internal Server Error`: Database or system error

## Security Considerations

### Token Security
- Use strong JWT secrets (minimum 32 characters)
- Implement token expiration (recommended: 24 hours)
- Use HTTPS in production
- Store secrets securely (environment variables, not in code)

### Database Security
- User status must be "active" for authentication
- Soft delete inactive users (don't hard delete)
- Validate user exists on every request

### Role Security
- Roles are validated on every request
- Role changes take effect immediately
- Use principle of least privilege

## Testing

### Example Requests
```bash
# Get user profile (any authenticated user)
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/v1/profile

# Get team members (admin or manager only)
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/v1/team/members

# Create team member (admin only)
curl -X POST \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"name":"John Doe","email":"<EMAIL>","role":"agent"}' \
     http://localhost:8080/api/v1/team/members
```

### Token Generation (for testing)
Use the JWT utility to generate test tokens:

```go
jwtService := utils.NewJWTService(&cfg.JWT)
token, err := jwtService.GenerateToken(user, 24*time.Hour)
```

## Integration with Auth Providers

This system works with any JWT provider (Auth0, Firebase, Supabase, etc.) as long as they:

1. Issue JWT tokens with required claims
2. Use supported signing algorithms
3. Include user information in token claims
4. Allow custom audience/issuer configuration

The backend validates tokens independently and doesn't need to communicate with the auth provider during request processing.

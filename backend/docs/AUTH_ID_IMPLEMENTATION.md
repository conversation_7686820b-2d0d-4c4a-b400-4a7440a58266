# Auth ID Implementation

## Overview

This document describes the implementation of the `auth_id` column in the users table to separate internal user IDs from Supabase authentication IDs.

## Problem Statement

Previously, the `users.id` field served dual purposes:
1. Internal user ID for database relationships
2. Supabase authentication user ID

This caused issues with team member invitations:
- When inviting a team member, we create a user with a UUID different from their eventual Supabase user ID
- When they sign up with Supabase, we can't update the `id` field because it's the primary key
- This breaks the authentication flow

## Solution

### Database Changes

1. **Added `auth_id` column** to `users` table:
   - Type: `UUID` (nullable)
   - Purpose: Store Supabase authentication user ID
   - Index: Unique index for fast lookups
   - Nullable: Allows invited users to exist without Supabase auth ID

2. **Migration file**: `006_add_auth_id_to_users.sql`

### Code Changes

#### 1. User Model (`models/user.go`)
- Added `AuthID *uuid.UUID` field
- Field is nullable to support invited users

#### 2. Authentication Middleware (`middleware/auth.go`)
- Changed user lookup from `id = ?` to `auth_id = ?`
- Now authenticates using Supabase user ID stored in `auth_id`

#### 3. Invitation Handler (`handlers/invitation.go`)
- Added JWT token parsing to extract Supabase user ID
- Sets `auth_id` when invitation is accepted
- Maintains backward compatibility

#### 4. Onboarding Handler (`handlers/onboard.go`)
- Generates separate internal user ID and auth ID
- Sets both `id` (internal) and `auth_id` (Supabase) during user creation
- Checks for existing users by `auth_id` instead of `id`

## Flow Diagrams

### Team Member Invitation Flow

```
1. Admin invites user (<EMAIL>)
   ↓
2. Create user record:
   - id: internal-uuid-1234
   - auth_id: NULL
   - email: <EMAIL>
   - status: pending
   ↓
3. User receives invitation email
   ↓
4. User signs up with Supabase
   ↓
5. User accepts invitation with JWT token
   ↓
6. Update user record:
   - id: internal-uuid-1234 (unchanged)
   - auth_id: supabase-uuid-5678
   - status: active
   ↓
7. User can now authenticate using auth_id
```

### Direct Onboarding Flow

```
1. User signs up with Supabase
   ↓
2. User completes onboarding
   ↓
3. Create user record:
   - id: internal-uuid-1234
   - auth_id: supabase-uuid-5678
   - status: active
   ↓
4. User can authenticate immediately
```

## Authentication Flow

```
1. Frontend sends JWT token with Supabase user ID
   ↓
2. Middleware extracts user ID from JWT claims
   ↓
3. Database lookup: WHERE auth_id = supabase-user-id
   ↓
4. AuthUser context uses internal user ID for all operations
```

## Benefits

1. **Separation of Concerns**: Internal ID vs Authentication ID
2. **Invitation Support**: Users can be created before Supabase signup
3. **Data Integrity**: Primary key remains stable
4. **Backward Compatibility**: Existing code using AuthUser.ID continues to work

## Migration Steps

1. Apply migration: `006_add_auth_id_to_users.sql`
2. Deploy backend changes
3. Test invitation flow
4. Test authentication flow

## Testing

Use the test script `scripts/test_auth_id.sql` to verify:
- Column exists
- Index is created
- Existing users have NULL auth_id (expected)

## Rollback

If needed, use `006_add_auth_id_to_users_rollback.sql` to remove the changes.

## Notes

- Existing users will have `auth_id = NULL` until they re-authenticate
- For existing users, you may need a data migration script to populate `auth_id`
- The system gracefully handles both scenarios (with and without auth_id)

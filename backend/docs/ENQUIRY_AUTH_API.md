# Enquiry Management API with Authentication

## Overview
The Enquiry Management API now supports JWT authentication with role-based access control. Different user roles have different permissions for enquiry operations.

## Authentication
All endpoints require a valid JWT token in the Authorization header:
```
Authorization: Bearer <jwt-token>
```

## Role-Based Permissions

### Admin
- **Full Access**: Can perform all operations
- View all enquiries
- Create new enquiries
- Update all enquiries (status, priority, assignment)
- Delete enquiries
- Assign/reassign enquiries to users

### Manager
- **Management Access**: Can manage enquiries but not delete
- View all enquiries
- Create new enquiries
- Update all enquiries (status, priority, assignment)
- Assign/reassign enquiries to users
- **Cannot**: Delete enquiries

### Agent
- **Limited Access**: Can only work with assigned enquiries
- View only enquiries assigned to them
- Create new enquiries
- Update only assigned enquiries (status only)
- **Cannot**: Change priority, assign/reassign enquiries, delete enquiries

## Endpoints

### 1. Get Enquiries
**Endpoint:** `GET /api/v1/enquiries`

**Authentication:** Required (Any role)

**Role Behavior:**
- **Admin/Manager**: See all enquiries
- **Agent**: See only enquiries assigned to them

**Query Parameters:**
- `search` (optional): Search by name, email, or description
- `status` (optional): Filter by status
- `priority` (optional): Filter by priority
- `assigned_to_id` (optional): Filter by assigned user
  - **Note**: Agents cannot filter by other users' assignments
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)

**Example Request:**
```bash
curl -H "Authorization: Bearer <token>" \
     "http://localhost:8080/api/v1/enquiries?status=New&page=1&limit=10"
```

**Response:**
```json
{
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "slug": "john-doe-enquiry-abc123",
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "assigned_to_id": "456e7890-e89b-12d3-a456-426614174001",
      "status": "New",
      "priority": "Medium",
      "source": "Website Form",
      "description": "Enquiry description",
      "submitted_on": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 25,
  "page": 1,
  "limit": 10
}
```

### 2. Get Single Enquiry
**Endpoint:** `GET /api/v1/enquiries/{id}`

**Authentication:** Required (Any role)

**Role Behavior:**
- **Admin/Manager**: Can view any enquiry
- **Agent**: Can only view enquiries assigned to them

**Example Request:**
```bash
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/v1/enquiries/123e4567-e89b-12d3-a456-426614174000
```

### 3. Create Enquiry
**Endpoint:** `POST /api/v1/enquiries`

**Authentication:** Required (Any role)

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "priority": "High",
  "source": "Phone Call",
  "description": "Customer enquiry description"
}
```

**Example Request:**
```bash
curl -X POST \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"name":"John Doe","email":"<EMAIL>","description":"Test enquiry"}' \
     http://localhost:8080/api/v1/enquiries
```

### 4. Update Enquiry
**Endpoint:** `PUT /api/v1/enquiries/{id}`

**Authentication:** Required (Any role)

**Role Permissions:**
- **Admin/Manager**: Can update all fields (status, priority, assignment)
- **Agent**: Can only update status of assigned enquiries

**Request Body:**
```json
{
  "assigned_to_id": "456e7890-e89b-12d3-a456-426614174001",
  "status": "In Progress",
  "priority": "High"
}
```

**Role Restrictions:**
- **Agents cannot**:
  - Update priority
  - Assign/reassign enquiries
  - Update enquiries not assigned to them

**Example Request:**
```bash
curl -X PUT \
     -H "Authorization: Bearer <token>" \
     -H "Content-Type: application/json" \
     -d '{"status":"In Progress"}' \
     http://localhost:8080/api/v1/enquiries/123e4567-e89b-12d3-a456-426614174000
```

### 5. Delete Enquiry
**Endpoint:** `DELETE /api/v1/enquiries/{id}`

**Authentication:** Required (Admin only)

**Example Request:**
```bash
curl -X DELETE \
     -H "Authorization: Bearer <token>" \
     http://localhost:8080/api/v1/enquiries/123e4567-e89b-12d3-a456-426614174000
```

## Activity Tracking

All enquiry operations are automatically tracked with the authenticated user's information:

- **Enquiry Created**: Tracked when new enquiry is created
- **Status Changed**: Tracked when status is updated
- **Priority Changed**: Tracked when priority is updated (Admin/Manager only)
- **Assignment Changed**: Tracked when enquiry is assigned/reassigned (Admin/Manager only)
- **Enquiry Updated**: General update tracking

## Error Responses

### Authentication Errors
```json
// Missing or invalid token
{
  "message": "Authentication required"
}
```

### Authorization Errors
```json
// Insufficient permissions
{
  "message": "You can only view enquiries assigned to you"
}

// Agent trying to change priority
{
  "message": "Agents cannot change enquiry priority"
}

// Agent trying to assign enquiry
{
  "message": "Agents cannot assign or reassign enquiries"
}

// Non-admin trying to delete
{
  "message": "Only administrators can delete enquiries"
}
```

### Validation Errors
```json
{
  "message": "Invalid status. Must be one of: New, In Progress, Closed"
}
```

## Security Features

1. **Role-Based Access**: Each role has specific permissions
2. **Data Isolation**: Agents can only access their assigned enquiries
3. **Activity Tracking**: All changes are logged with user information
4. **Input Validation**: All inputs are validated before processing
5. **Soft Delete**: Enquiries are soft-deleted (admin only)

## Migration from Non-Auth Version

If you're migrating from the non-authenticated version:

1. **Remove query parameters**: No more `user_id` or `admin_id` parameters
2. **Add Authorization header**: Include JWT token in all requests
3. **Handle role-based responses**: Different roles see different data
4. **Update error handling**: New authentication/authorization errors

## Testing

Use the JWT test utilities to generate tokens for different roles:

```go
// Generate test tokens
generator := utils.NewTestJWTGenerator("secret", "issuer", "audience", "HS256")

adminToken, _ := generator.GenerateAdminToken(userID, email, name, 24*time.Hour)
managerToken, _ := generator.GenerateManagerToken(userID, email, name, 24*time.Hour)
agentToken, _ := generator.GenerateAgentToken(userID, email, name, 24*time.Hour)
```

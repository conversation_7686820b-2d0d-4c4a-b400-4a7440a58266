# Invitation API Documentation

This document describes the invitation system API endpoints for team member onboarding with email notifications.

## Overview

The invitation system allows administrators to invite new team members via email. The flow includes:
1. <PERSON><PERSON> creates team member → System sends invitation email
2. User clicks email link → Frontend verifies token
3. User completes signup → Account activated

## Rate Limiting

All public invitation endpoints are rate-limited to **10 requests per minute per IP** to prevent token brute force attacks.

## Endpoints

### 1. Verify Invitation Token

Verifies if an invitation token is valid and returns invitation details.

**Endpoint:** `GET /api/v1/invitations/verify?token={token}`

**Rate Limited:** Yes (10 req/min per IP)

**Query Parameters:**
- `token` (required): The invitation token from the email link

**Success Response (200):**
```json
{
  "valid": true,
  "invitation": {
    "email": "<EMAIL>",
    "role": "agent",
    "organization_name": "Acme Corp",
    "organization_slug": "acme-corp",
    "expires_at": "2024-12-26T10:30:00Z",
    "expires_in_hours": 48
  }
}
```

**Error Responses:**

**Missing Token (400):**
```json
{
  "valid": false,
  "error": "missing_token",
  "message": "Invitation token is required"
}
```

**Invalid Token (404):**
```json
{
  "valid": false,
  "error": "invitation_not_found",
  "message": "Invalid invitation link"
}
```

**Expired Token (410):**
```json
{
  "valid": false,
  "error": "invitation_expired",
  "message": "This invitation has expired",
  "expired_at": "2024-12-19T10:30:00Z"
}
```

**Already Accepted (409):**
```json
{
  "valid": false,
  "error": "invitation_already_accepted",
  "message": "This invitation has already been accepted",
  "accepted_at": "2024-12-20T15:45:00Z"
}
```

**Cancelled Invitation (410):**
```json
{
  "valid": false,
  "error": "invitation_cancelled",
  "message": "This invitation has been cancelled"
}
```

**Rate Limit Exceeded (429):**
```json
{
  "error": "rate_limit_exceeded",
  "message": "Too many requests. Please try again later."
}
```

### 2. Accept Invitation

Accepts an invitation and activates the user account.

**Endpoint:** `POST /api/v1/invitations/accept`

**Rate Limited:** Yes (10 req/min per IP)

**Request Body:**
```json
{
  "token": "abc123...",
  "name": "John Doe",
  "password": "optional-for-google-signin"
}
```

**Fields:**
- `token` (required): The invitation token
- `name` (required): User's full name
- `password` (optional): Required for email/password signup, optional for Google signin

**Success Response (200):**
```json
{
  "success": true,
  "message": "Invitation accepted successfully",
  "user": {
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "agent",
    "organization_name": "Acme Corp",
    "organization_slug": "acme-corp"
  }
}
```

**Error Responses:**

**Invalid Request (400):**
```json
{
  "success": false,
  "error": "invalid_request",
  "message": "Invalid request body"
}
```

**Missing Fields (400):**
```json
{
  "success": false,
  "error": "missing_name",
  "message": "Name is required"
}
```

**All verification errors from the verify endpoint apply here as well**

### 3. Onboard Invited User

Onboards an invited user after they have completed Supabase authentication. This endpoint validates both the invitation token and Supabase JWT token, then links the Supabase user ID to the internal user account.

**Endpoint:** `POST /api/v1/invitations/onboard`

**Rate Limited:** Yes (10 req/min per IP)

**Request Body:**
```json
{
  "invitation_token": "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456",
  "supabase_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "name": "John Doe"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "user": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "agent"
  },
  "organization": {
    "id": "789e0123-e89b-12d3-a456-************",
    "name": "Acme Corp",
    "slug": "acme-corp"
  },
  "redirect_url": "/org/acme-corp/dashboard"
}
```

**Error Responses:**

**Missing Fields (400):**
```json
{
  "success": false,
  "error": "missing_invitation_token",
  "message": "Invitation token is required"
}
```

**Invalid Invitation Token (404):**
```json
{
  "success": false,
  "error": "invitation_not_found",
  "message": "Invalid invitation token"
}
```

**Invitation Already Processed (400):**
```json
{
  "success": false,
  "error": "invitation_not_pending",
  "message": "Invitation has already been processed"
}
```

**Invitation Expired (400):**
```json
{
  "success": false,
  "error": "invitation_expired",
  "message": "Invitation has expired"
}
```

**Invalid Supabase Token (401):**
```json
{
  "success": false,
  "error": "invalid_supabase_token",
  "message": "Invalid or expired Supabase authentication token"
}
```

**Email Mismatch (400):**
```json
{
  "success": false,
  "error": "email_mismatch",
  "message": "Email in Supabase token does not match invitation email"
}
```

**User Already Linked (409):**
```json
{
  "success": false,
  "error": "user_already_linked",
  "message": "This Supabase account is already linked to another user"
}
```

### 5. List Invitations (Admin Only)

Lists all invitations for the organization.

**Endpoint:** `GET /api/v1/invitations`

**Authentication:** Required (Admin only)

**Response (200):**
```json
[
  {
    "id": "123e4567-e89b-12d3-a456-************",
    "email": "<EMAIL>",
    "role": "agent",
    "status": "pending",
    "expires_at": "2024-12-26T10:30:00Z",
    "accepted_at": null,
    "invited_by": "456e7890-e89b-12d3-a456-************",
    "inviter_name": "Admin User",
    "organization_id": "789e0123-e89b-12d3-a456-************",
    "created_at": "2024-12-19T10:30:00Z"
  }
]
```

### 6. Cancel Invitation (Admin Only)

Cancels a pending invitation.

**Endpoint:** `DELETE /api/v1/invitations/{id}`

**Authentication:** Required (Admin only)

**Response (200):**
```json
{
  "message": "Invitation cancelled successfully"
}
```

## Frontend Integration

### Member Onboarding Page Flow

1. **Page Load:** `/invite/accept?token=xxx`
   - Extract token from URL
   - Call `GET /api/v1/invitations/verify?token=xxx`
   - If valid: Show signup form with organization info
   - If invalid: Show appropriate error page

2. **Signup Form:**
   - Pre-fill email (read-only)
   - Show organization name and role
   - Provide both Google signin and email/password options

3. **After Supabase Authentication:**
   - Get Supabase JWT token from session
   - Call `POST /api/v1/invitations/onboard` with invitation token, Supabase token, and name
   - On success: Redirect to organization dashboard using returned redirect_url
   - On error: Show appropriate error message

4. **Legacy Flow (Deprecated):**
   - For backward compatibility, `POST /api/v1/invitations/accept` still works
   - But new implementations should use the `/onboard` endpoint

### Error Handling

- **Expired:** Show "Invitation Expired" page with contact admin message
- **Already Accepted:** Show "Already Accepted" page with login link
- **Invalid/Not Found:** Show "Invalid Link" page
- **Rate Limited:** Show temporary error with retry message

## Security Features

- **Rate Limiting:** 10 requests per minute per IP
- **Secure Tokens:** 64-character cryptographically secure tokens
- **Token Expiration:** 7-day automatic expiration
- **Re-verification:** Token verified on both verify and accept endpoints
- **Organization Isolation:** Users can only accept invitations for their organization

## Notes

- Invitations expire after 7 days
- Users with pending status cannot log in until invitation is accepted
- Email service is optional - invitations work without email if SendGrid is not configured
- All timestamps are in RFC3339 format (ISO 8601)
- The system prevents duplicate invitations for the same email

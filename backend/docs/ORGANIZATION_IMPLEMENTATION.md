# Organization Multi-Tenancy Implementation

This document describes the multi-tenant organization implementation for the enquiry management system.

## Overview

The system now supports multiple organizations with complete data isolation. Each organization operates independently with its own users, enquiries, and activities.

## Database Schema Changes

### New Tables

#### Organizations Table
```sql
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    domain VARCHAR(255) UNIQUE,
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    plan VARCHAR(50) NOT NULL DEFAULT 'starter',
    settings JSONB,
    created_by UUID NOT NULL,
    max_users INTEGER DEFAULT 10,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

### Updated Tables

All existing tables now include `organization_id`:
- `users` - Links users to their organization
- `enquiries` - Links enquiries to organizations
- `activities` - Links activities to organizations

## API Endpoints

### Organization Management

#### Get Current Organization
```
GET /api/v1/organizations/current
```
Returns the current user's organization details.

#### Update Current Organization
```
PUT /api/v1/organizations/current
```
Updates organization settings (admin only).

**Request Body:**
```json
{
  "name": "Updated Organization Name",
  "domain": "custom.domain.com",
  "plan": "pro",
  "settings": {
    "branding": {
      "logo": "https://example.com/logo.png",
      "primary_color": "#4F46E5"
    }
  }
}
```

#### Get Organization Statistics
```
GET /api/v1/organizations/stats
```
Returns organization usage statistics (admin/manager only).

**Response:**
```json
{
  "total_users": 15,
  "active_users": 12,
  "total_enquiries": 150,
  "max_users": 50,
  "plan_limits": {
    "users": 50,
    "storage_gb": 25,
    "features": ["basic_enquiries", "basic_reports", "advanced_reports"]
  }
}
```

#### Create Organization (Super Admin)
```
POST /api/v1/organizations
```
Creates a new organization.

**Request Body:**
```json
{
  "name": "New Organization",
  "slug": "new-org",
  "domain": "neworg.example.com",
  "plan": "starter"
}
```

## Data Isolation

### Automatic Filtering
All database queries are automatically scoped to the user's organization using middleware:

```go
// Get organization-scoped database instance
db := middleware.GetOrganizationScopedDB(c, db)

// All queries will be automatically filtered by organization_id
var enquiries []models.Enquiry
db.Find(&enquiries) // Only returns enquiries from user's organization
```

### Manual Validation
For critical operations, you can manually validate organization access:

```go
err := middleware.ValidateOrganizationAccess(c, db, "enquiries", enquiryID)
if err != nil {
    return err // Returns 404 if resource doesn't belong to organization
}
```

## Authentication Changes

### Enhanced AuthUser Context
The `AuthUser` model now includes organization information:

```go
type AuthUser struct {
    ID             uuid.UUID `json:"id"`
    Email          string    `json:"email"`
    Role           string    `json:"role"`
    Name           string    `json:"name"`
    OrganizationID uuid.UUID `json:"organization_id"`
    OrgSlug        string    `json:"org_slug"`
    OrgName        string    `json:"org_name"`
}
```

### Organization Context Middleware
The authentication middleware now automatically loads organization context for each request.

## Subscription Plans

### Plan Types
- **Starter**: 10 users, 5GB storage, basic features
- **Pro**: 50 users, 25GB storage, advanced features
- **Enterprise**: 500 users, 100GB storage, all features

### Plan Features
```go
var PlanLimits = map[string]struct {
    MaxUsers  int
    StorageGB int
    Features  []string
}{
    "starter": {
        MaxUsers:  10,
        StorageGB: 5,
        Features:  []string{"basic_enquiries", "basic_reports"},
    },
    // ... other plans
}
```

## Migration

### Running the Migration
```bash
# Apply the organization migration
psql -d your_database -f migrations/004_add_organizations.sql
```

### Default Organization
The migration creates a default organization and assigns all existing data to it, ensuring backward compatibility.

## Security Considerations

1. **Row-Level Security**: All queries are automatically scoped by organization
2. **Organization Status**: Inactive organizations cannot access the system
3. **Role-Based Access**: Organization roles control access within each organization
4. **Resource Validation**: Critical operations validate organization ownership

## Usage Examples

### Creating Organization-Scoped Queries
```go
func (h *EnquiryHandler) GetEnquiries(c echo.Context) error {
    // Get organization-scoped database
    db := middleware.GetOrganizationScopedDB(c, h.db)
    
    var enquiries []models.Enquiry
    if err := db.Find(&enquiries).Error; err != nil {
        return err
    }
    
    return c.JSON(http.StatusOK, enquiries)
}
```

### Checking Organization Features
```go
// Middleware to require specific feature
routes.GET("/advanced-reports", handler.GetAdvancedReports, 
    middleware.RequireOrganizationFeature("advanced_reports"))
```

### Role-Based Access Within Organization
```go
// Only org admins and managers can access
routes.GET("/team/members", handler.GetTeamMembers,
    middleware.RequireOrganizationRole("admin", "manager"))
```

## Next Steps

1. **Frontend Integration**: Update frontend to handle organization context
2. **User Invitations**: Implement organization-based user invitations
3. **Custom Domains**: Add support for custom domain routing
4. **Billing Integration**: Connect with payment providers for subscription management
5. **Advanced Features**: Implement organization-specific branding and settings

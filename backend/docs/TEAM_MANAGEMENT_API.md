# Team Management API Documentation

## Overview
The Team Management API provides endpoints for managing team members. Only admins can add, update, and remove team members. Managers can view team members but cannot modify them.

## Permissions
- **Admin**: Full access (view, add, update, delete team members)
- **Manager**: Read-only access (view team members only)
- **Agent**: No access to team management

## Endpoints

### 1. Get Team Members
Retrieve a list of team members with filtering and pagination.

**Endpoint:** `GET /api/v1/team/members?admin_id={admin_id}`

**Query Parameters:**
- `admin_id` (required): UUID of the requesting user (admin or manager)
- `search` (optional): Search by name or email
- `role` (optional): Filter by role (admin, manager, agent, or "all")
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)

**Example Request:**
```
GET /api/v1/team/members?admin_id=123e4567-e89b-12d3-a456-************&search=sarah&role=admin&page=1&limit=10
```

**Response:**
```json
{
  "members": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "name": "Sarah Brown",
      "email": "<EMAIL>",
      "role": "admin",
      "status": "active",
      "last_login": "2024-04-24 10:30 PM",
      "joined_date": "2024-01-15",
      "avatar": "SB"
    },
    {
      "id": "456e7890-e89b-12d3-a456-************",
      "name": "Katherine Howard",
      "email": "<EMAIL>",
      "role": "manager",
      "status": "active",
      "last_login": "2024-04-24 09:15 PM",
      "joined_date": "2024-02-01",
      "avatar": "KH"
    }
  ],
  "total": 5,
  "page": 1,
  "limit": 10
}
```

### 2. Add Team Member
Add a new team member to the system. Only admins can perform this action.

**Endpoint:** `POST /api/v1/team/members?admin_id={admin_id}`

**Query Parameters:**
- `admin_id` (required): UUID of the requesting admin user

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "role": "agent"
}
```

**Response:**
```json
{
  "id": "789e0123-e89b-12d3-a456-************",
  "name": "John Doe",
  "email": "<EMAIL>",
  "role": "agent",
  "status": "active",
  "last_login": "Never",
  "joined_date": "2024-04-24",
  "avatar": "JD"
}
```

### 3. Update Team Member
Update a team member's role or status. Only admins can perform this action.

**Endpoint:** `PUT /api/v1/team/members/{member_id}?admin_id={admin_id}`

**Path Parameters:**
- `member_id` (required): UUID of the team member to update

**Query Parameters:**
- `admin_id` (required): UUID of the requesting admin user

**Request Body:**
```json
{
  "role": "manager",
  "status": "active"
}
```

**Response:**
```json
{
  "id": "789e0123-e89b-12d3-a456-************",
  "name": "John Doe",
  "email": "<EMAIL>",
  "role": "manager",
  "status": "active",
  "last_login": "Never",
  "joined_date": "2024-04-24",
  "avatar": "JD"
}
```

### 4. Delete Team Member
Soft delete a team member (sets status to inactive). Only admins can perform this action.

**Endpoint:** `DELETE /api/v1/team/members/{member_id}?admin_id={admin_id}`

**Path Parameters:**
- `member_id` (required): UUID of the team member to delete

**Query Parameters:**
- `admin_id` (required): UUID of the requesting admin user

**Response:**
```json
{
  "message": "Team member deleted successfully"
}
```

## Field Descriptions

### Team Member Fields
- `id`: User's unique identifier (UUID)
- `name`: User's full name
- `email`: User's email address (must be unique)
- `role`: User's system role (admin, manager, agent)
- `status`: User's account status (active, inactive)
- `last_login`: Formatted timestamp of last login or "Never"
- `joined_date`: Date when user joined the system (YYYY-MM-DD format)
- `avatar`: Generated initials from user's name

### Valid Roles
- `admin`: Full system access, can manage users and settings
- `manager`: Can view team members and manage enquiries
- `agent`: Can view and update assigned enquiries

### Valid Statuses
- `active`: User can access the system
- `inactive`: User is soft-deleted and cannot access the system

## Error Responses

### 400 Bad Request
```json
{
  "message": "admin_id is required"
}
```

### 403 Forbidden
```json
{
  "message": "Only admins can add team members"
}
```

### 404 Not Found
```json
{
  "message": "Team member not found"
}
```

### 409 Conflict
```json
{
  "message": "User with this email already exists"
}
```

## Notes

1. **Soft Delete**: Deleting a team member sets their status to "inactive" rather than removing the record.
2. **Email Uniqueness**: Each team member must have a unique email address.
3. **Avatar Generation**: Avatars are automatically generated from the user's name initials.
4. **Pagination**: Results are paginated with configurable page size.
5. **Search**: Search functionality works across name and email fields (case-insensitive).
6. **Role Validation**: Only predefined roles (admin, manager, agent) are accepted.
7. **Permission Checks**: All endpoints verify the requesting user's permissions before proceeding.

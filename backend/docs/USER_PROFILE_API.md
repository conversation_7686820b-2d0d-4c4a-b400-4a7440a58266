# User Profile API Documentation

## Overview
The User Profile API provides endpoints for managing user profile information including personal details, job information, and preferences.

## Database Setup

Before using the profile endpoints, you need to add the new profile fields to the users table:

```sql
-- Run the migration script
\i backend/migrations/003_add_user_profile_fields.sql
```

## Endpoints

### 1. Get User Profile
Retrieve the current user's profile information.

**Endpoint:** `GET /api/v1/profile?user_id={user_id}`

**Query Parameters:**
- `user_id` (required): UUID of the user

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "name": "<PERSON><PERSON> <PERSON>",
  "email": "<EMAIL>",
  "phone": "+****************",
  "job_title": "CRM Administrator",
  "department": "Sales & Marketing",
  "timezone": "America/New_York",
  "bio": "Experienced CRM administrator with a passion for optimizing customer relationships and driving business growth.",
  "role": "admin",
  "status": "active",
  "last_login": "2024-01-15T10:30:00Z",
  "joined_date": "2024-01-01T00:00:00Z",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### 2. Update User Profile
Update the current user's profile information.

**Endpoint:** `PUT /api/v1/profile?user_id={user_id}`

**Query Parameters:**
- `user_id` (required): UUID of the user

**Request Body:**
```json
{
  "phone": "+****************",
  "job_title": "Senior CRM Administrator", 
  "department": "Sales & Marketing",
  "timezone": "America/New_York",
  "bio": "Updated bio text with new information about experience and skills."
}
```

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "name": "Suraj Sharma",
  "email": "<EMAIL>",
  "phone": "+****************",
  "job_title": "Senior CRM Administrator",
  "department": "Sales & Marketing", 
  "timezone": "America/New_York",
  "bio": "Updated bio text with new information about experience and skills.",
  "role": "admin",
  "status": "active",
  "last_login": "2024-01-15T10:30:00Z",
  "joined_date": "2024-01-01T00:00:00Z",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-15T10:35:00Z"
}
```

## Field Descriptions

### Read-Only Fields
- `id`: User's unique identifier (UUID)
- `name`: User's full name (managed by authentication system)
- `email`: User's email address (managed by authentication system)
- `role`: User's system role (admin, agent, etc.)
- `status`: User's account status (active, inactive)
- `last_login`: Timestamp of last login
- `joined_date`: Date when user joined the system
- `created_at`: Record creation timestamp
- `updated_at`: Record last update timestamp

### Editable Fields
- `phone`: User's phone number (optional)
- `job_title`: User's job title/position (optional)
- `department`: User's department or team (optional)
- `timezone`: User's timezone preference (optional)
- `bio`: User's biography or description (optional)

## Notes

1. **No Authentication**: Currently, these endpoints do not require authentication (as per project requirements).
2. **UUID Format**: All user IDs must be valid UUIDs.
3. **Partial Updates**: Only provided fields in the update request will be modified.
4. **Read-Only Fields**: Name and email cannot be updated through these endpoints.
5. **Optional Fields**: All profile fields (phone, job_title, department, timezone, bio) are optional.

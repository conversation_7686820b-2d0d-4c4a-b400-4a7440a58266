package handlers

import (
	"net/http"
	"time"

	"enquiry-management-system/models"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type ActivityHandler struct {
	db *gorm.DB
}

func NewActivityHandler(db *gorm.DB) *ActivityHandler {
	return &ActivityHandler{db: db}
}

// GetActivities retrieves all activities for an enquiry
func (h *ActivityHandler) GetActivities(c echo.Context) error {
	enquiryID := c.Param("id")
	if enquiryID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Enquiry ID is required")
	}

	// Parse enquiry ID
	enquiryUUID, err := uuid.Parse(enquiryID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid enquiry ID format")
	}

	// Check if enquiry exists
	var enquiry models.Enquiry
	if err := h.db.First(&enquiry, "id = ?", enquiryUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Database error")
	}

	// Get activities with user information
	var activities []models.Activity
	if err := h.db.Where("enquiry_id = ?", enquiryUUID).Order("created_at DESC").Find(&activities).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch activities")
	}

	// Prepare response with user information
	var responses []models.ActivityResponse
	for _, activity := range activities {
		var user models.User
		h.db.First(&user, "id = ?", activity.UserID)
		var userName string
		if user.ID == uuid.Nil || user.Name == "" {
			userName = "System"
		} else if user.Role == "system" {
			userName = "Form Submission"
		} else {
			userName = user.Name
		}
		response := models.ActivityResponse{
			ID:          activity.ID,
			EnquiryID:   activity.EnquiryID,
			UserID:      activity.UserID,
			Type:        activity.Type,
			Description: activity.Description,
			Metadata:    string(activity.Metadata),
			CreatedAt:   activity.CreatedAt.Format(time.RFC3339),
			UpdatedAt:   activity.UpdatedAt.Format(time.RFC3339),
			User:        userName,
		}
		responses = append(responses, response)
	}

	return c.JSON(http.StatusOK, responses)
}

// CreateActivity creates a new activity (internal use)
func (h *ActivityHandler) CreateActivity(organizationID, enquiryID, userID uuid.UUID, activityType, description string, metadata *models.ActivityMetadata) error {
	activity := models.CreateActivity(organizationID, enquiryID, userID, activityType, description, metadata)

	if err := h.db.Create(&activity).Error; err != nil {
		return err
	}

	return nil
}

// Helper function to track enquiry creation
func (h *ActivityHandler) TrackEnquiryCreated(organizationID, enquiryID, userID uuid.UUID, enquiryName string) error {
	description := "Enquiry '" + enquiryName + "' was created"
	return h.CreateActivity(organizationID, enquiryID, userID, models.ActivityEnquiryCreated, description, nil)
}

// Helper function to track status changes
func (h *ActivityHandler) TrackStatusChanged(organizationID, enquiryID, userID uuid.UUID, oldStatus, newStatus string) error {
	description := "Status changed from '" + oldStatus + "' to '" + newStatus + "'"
	metadata := &models.ActivityMetadata{
		OldStatus: oldStatus,
		NewStatus: newStatus,
	}
	return h.CreateActivity(organizationID, enquiryID, userID, models.ActivityStatusChanged, description, metadata)
}

// Helper function to track priority changes
func (h *ActivityHandler) TrackPriorityChanged(organizationID, enquiryID, userID uuid.UUID, oldPriority, newPriority string) error {
	description := "Priority changed from '" + oldPriority + "' to '" + newPriority + "'"
	metadata := &models.ActivityMetadata{
		OldPriority: oldPriority,
		NewPriority: newPriority,
	}
	return h.CreateActivity(organizationID, enquiryID, userID, models.ActivityPriorityChanged, description, metadata)
}

// Helper function to track assignment changes
func (h *ActivityHandler) TrackAssignmentChanged(organizationID, enquiryID, userID uuid.UUID, oldAssignee, newAssignee string) error {
	var description string
	if oldAssignee == "" {
		description = "Enquiry assigned to " + newAssignee
	} else if newAssignee == "" {
		description = "Enquiry unassigned from " + oldAssignee
	} else {
		description = "Assignment changed from " + oldAssignee + " to " + newAssignee
	}

	metadata := &models.ActivityMetadata{
		OldAssignee: oldAssignee,
		NewAssignee: newAssignee,
	}
	return h.CreateActivity(organizationID, enquiryID, userID, models.ActivityAssignmentChanged, description, metadata)
}

// Helper function to track note addition
func (h *ActivityHandler) TrackNoteAdded(organizationID, enquiryID, userID uuid.UUID, noteContent string) error {
	description := "Added a note"
	if len(noteContent) > 50 {
		description += ": " + noteContent[:50] + "..."
	} else {
		description += ": " + noteContent
	}

	metadata := &models.ActivityMetadata{
		NoteContent: noteContent,
	}
	return h.CreateActivity(organizationID, enquiryID, userID, models.ActivityNoteAdded, description, metadata)
}

// Helper function to track note deletion
func (h *ActivityHandler) TrackNoteDeleted(organizationID, enquiryID, userID uuid.UUID) error {
	description := "Deleted a note"
	return h.CreateActivity(organizationID, enquiryID, userID, models.ActivityNoteDeleted, description, nil)
}

// Helper function to track general enquiry updates
// NOTE: This function is deprecated and should not be used to avoid duplicate activities.
// Use specific tracking functions instead: TrackStatusChanged, TrackPriorityChanged, TrackAssignmentChanged
func (h *ActivityHandler) TrackEnquiryUpdated(organizationID, enquiryID, userID uuid.UUID, changes string) error {
	description := "Enquiry details updated"
	if changes != "" {
		description += ": " + changes
	}

	metadata := &models.ActivityMetadata{
		Changes: changes,
	}
	return h.CreateActivity(organizationID, enquiryID, userID, models.ActivityEnquiryUpdated, description, metadata)
}

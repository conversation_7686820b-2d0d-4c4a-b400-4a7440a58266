package handlers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"enquiry-management-system/models"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupActivityTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}

	// Auto-migrate the schema
	db.AutoMigrate(&models.User{}, &models.Enquiry{}, &models.Note{}, &models.Activity{})

	return db
}

func createActivityTestData(db *gorm.DB) (models.User, models.Enquiry) {
	// Create test organization first
	org := models.Organization{
		Name:      "Test Organization",
		Slug:      "test-org",
		Status:    "active",
		Plan:      "starter",
		CreatedBy: uuid.New(), // Dummy creator ID
		MaxUsers:  10,
	}
	db.Create(&org)

	// Create test user
	user := models.User{
		OrganizationID: org.ID,
		Name:           "Test User",
		Email:          "<EMAIL>",
		Role:           "agent",
		Status:         "active",
	}
	db.Create(&user)

	// Create test enquiry
	enquiry := models.Enquiry{
		OrganizationID: org.ID,
		Slug:           "test-enquiry",
		Name:           "Test Enquiry",
		Email:          "<EMAIL>",
		Phone:          "1234567890",
		Status:         "New",
		Priority:       "Medium",
		Source:         "Website Form",
		Description:    "Test enquiry description",
	}
	db.Create(&enquiry)

	return user, enquiry
}

func TestGetActivities(t *testing.T) {
	// Setup
	db := setupActivityTestDB()
	user, enquiry := createActivityTestData(db)
	handler := NewActivityHandler(db)

	// Create test activities
	activity1 := models.CreateActivity(
		user.OrganizationID,
		enquiry.ID,
		user.ID,
		models.ActivityEnquiryCreated,
		"Enquiry 'Test Enquiry' was created",
		nil,
	)
	activity2 := models.CreateActivity(
		user.OrganizationID,
		enquiry.ID,
		user.ID,
		models.ActivityStatusChanged,
		"Status changed from 'New' to 'In Progress'",
		&models.ActivityMetadata{
			OldStatus: "New",
			NewStatus: "In Progress",
		},
	)
	db.Create(&activity1)
	db.Create(&activity2)

	// Create request
	req := httptest.NewRequest(http.MethodGet, "/", nil)
	rec := httptest.NewRecorder()

	e := echo.New()
	c := e.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(enquiry.ID.String())

	// Execute
	err := handler.GetActivities(c)

	// Assert
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if rec.Code != http.StatusOK {
		t.Fatalf("Expected status %d, got %d", http.StatusOK, rec.Code)
	}

	var response []models.ActivityResponse
	json.Unmarshal(rec.Body.Bytes(), &response)

	if len(response) != 2 {
		t.Fatalf("Expected 2 activities, got %d", len(response))
	}

	// Activities should be ordered by created_at DESC (newest first)
	if response[0].Type != models.ActivityStatusChanged {
		t.Fatalf("Expected first activity to be 'Status Changed', got %s", response[0].Type)
	}

	// Check metadata
	if response[0].Metadata == "" {
		t.Fatalf("Expected metadata for status change activity")
	}

	// Check user information is included
	if response[0].User == "" {
		t.Fatalf("Expected user information to be included")
	}

	if response[0].User != user.Name {
		t.Fatalf("Expected user name %s, got %s", user.Name, response[0].User)
	}
}

func TestGetActivitiesInvalidEnquiry(t *testing.T) {
	// Setup
	db := setupActivityTestDB()
	handler := NewActivityHandler(db)

	// Create request with invalid enquiry ID
	req := httptest.NewRequest(http.MethodGet, "/", nil)
	rec := httptest.NewRecorder()

	e := echo.New()
	c := e.NewContext(req, rec)
	c.SetParamNames("id")
	c.SetParamValues(uuid.New().String()) // Random UUID that doesn't exist

	// Execute
	err := handler.GetActivities(c)

	// Assert
	if err == nil {
		t.Fatalf("Expected error for invalid enquiry ID")
	}

	httpError, ok := err.(*echo.HTTPError)
	if !ok {
		t.Fatalf("Expected HTTPError, got %T", err)
	}

	if httpError.Code != http.StatusNotFound {
		t.Fatalf("Expected status %d, got %d", http.StatusNotFound, httpError.Code)
	}
}

func TestActivityHelperFunctions(t *testing.T) {
	// Setup
	db := setupActivityTestDB()
	user, enquiry := createActivityTestData(db)
	handler := NewActivityHandler(db)

	// Test TrackEnquiryCreated
	err := handler.TrackEnquiryCreated(user.OrganizationID, enquiry.ID, user.ID, enquiry.Name)
	if err != nil {
		t.Fatalf("Expected no error for TrackEnquiryCreated, got %v", err)
	}

	// Test TrackStatusChanged
	err = handler.TrackStatusChanged(user.OrganizationID, enquiry.ID, user.ID, "New", "In Progress")
	if err != nil {
		t.Fatalf("Expected no error for TrackStatusChanged, got %v", err)
	}

	// Test TrackNoteAdded
	err = handler.TrackNoteAdded(user.OrganizationID, enquiry.ID, user.ID, "This is a test note")
	if err != nil {
		t.Fatalf("Expected no error for TrackNoteAdded, got %v", err)
	}

	// Verify activities were created
	var activities []models.Activity
	db.Where("enquiry_id = ?", enquiry.ID).Find(&activities)

	if len(activities) != 3 {
		t.Fatalf("Expected 3 activities, got %d", len(activities))
	}

	// Check activity types
	expectedTypes := []string{
		models.ActivityEnquiryCreated,
		models.ActivityStatusChanged,
		models.ActivityNoteAdded,
	}

	for i, activity := range activities {
		if activity.Type != expectedTypes[i] {
			t.Fatalf("Expected activity type %s, got %s", expectedTypes[i], activity.Type)
		}
	}
}

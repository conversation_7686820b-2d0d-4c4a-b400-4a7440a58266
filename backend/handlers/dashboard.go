package handlers

import (
	"net/http"
	"strconv"
	"time"

	"enquiry-management-system/models"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type DashboardHandler struct {
	db *gorm.DB
}

func NewDashboardHandler(db *gorm.DB) *DashboardHandler {
	return &DashboardHandler{db: db}
}

// GetDashboardOverview returns combined dashboard data
func (h *DashboardHandler) GetDashboardOverview(c echo.Context) error {
	// Parse query parameters with defaults
	recentActivitiesLimit := 20 // Increased default since it's now the main content
	if limit := c.QueryParam("recent_activities_limit"); limit != "" {
		if parsed, err := strconv.Atoi(limit); err == nil && parsed > 0 && parsed <= 100 {
			recentActivitiesLimit = parsed
		}
	}

	activitiesDays := 14 // Increased default to show more activity history
	if days := c.QueryParam("activities_days"); days != "" {
		if parsed, err := strconv.Atoi(days); err == nil && parsed > 0 && parsed <= 90 {
			activitiesDays = parsed
		}
	}

	// Get dashboard stats (organization-scoped)
	stats, err := h.getDashboardStats(c)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch dashboard stats")
	}

	// Get recent activities (organization-scoped)
	recentActivities, err := h.getRecentActivities(c, recentActivitiesLimit, activitiesDays)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch recent activities")
	}

	// Combine all data
	overview := models.DashboardOverview{
		Stats:            stats,
		RecentEnquiries:  []models.RecentEnquiry{}, // Empty array since we no longer use this
		RecentActivities: recentActivities,
	}

	return c.JSON(http.StatusOK, overview)
}

// getDashboardStats calculates dashboard statistics (organization-scoped)
func (h *DashboardHandler) getDashboardStats(c echo.Context) (models.DashboardStats, error) {
	var stats models.DashboardStats

	// Get authenticated user from context
	authUser := c.Get("user")
	if authUser == nil {
		return stats, echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}
	user := authUser.(*models.AuthUser)

	// Total enquiries (organization-scoped) - Fresh query
	if err := h.db.Where("organization_id = ?", user.OrganizationID).
		Model(&models.Enquiry{}).Count(&stats.TotalEnquiries).Error; err != nil {
		return stats, err
	}

	// New today (enquiries created today, organization-scoped) - Fresh query
	today := time.Now().Format("2006-01-02")
	if err := h.db.Where("organization_id = ?", user.OrganizationID).
		Model(&models.Enquiry{}).
		Where("DATE(created_at) = ?", today).
		Count(&stats.NewToday).Error; err != nil {
		return stats, err
	}

	// In Progress enquiries (organization-scoped) - Fresh query
	if err := h.db.Where("organization_id = ?", user.OrganizationID).
		Model(&models.Enquiry{}).
		Where("status = ?", "In Progress").
		Count(&stats.InProgress).Error; err != nil {
		return stats, err
	}

	// Closed enquiries (organization-scoped) - Fresh query
	if err := h.db.Where("organization_id = ?", user.OrganizationID).
		Model(&models.Enquiry{}).
		Where("status = ?", "Closed").
		Count(&stats.Closed).Error; err != nil {
		return stats, err
	}

	return stats, nil
}

// getRecentActivities fetches recent activities for dashboard (organization-scoped)
func (h *DashboardHandler) getRecentActivities(c echo.Context, limit int, days int) ([]models.RecentActivity, error) {
	var activities []models.Activity
	var recentActivities []models.RecentActivity

	// Get authenticated user from context
	authUser := c.Get("user")
	if authUser == nil {
		return recentActivities, echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}
	user := authUser.(*models.AuthUser)

	// Calculate date range
	since := time.Now().AddDate(0, 0, -days)

	// Fetch recent activities within date range (organization-scoped)
	if err := h.db.Where("organization_id = ? AND created_at >= ?", user.OrganizationID, since).
		Order("created_at DESC").
		Limit(limit).
		Find(&activities).Error; err != nil {
		return recentActivities, err
	}

	// Convert to dashboard format with additional context
	for _, activity := range activities {
		var enquiry models.Enquiry
		var activityUser models.User

		// Get enquiry details (organization-scoped)
		enquiryFound := h.db.Where("id = ? AND organization_id = ?", activity.EnquiryID, user.OrganizationID).
			First(&enquiry).Error == nil

		// Get user details (organization-scoped)
		userFound := h.db.Where("id = ? AND organization_id = ?", activity.UserID, user.OrganizationID).
			First(&activityUser).Error == nil

		// Prepare activity data
		recentActivity := models.RecentActivity{
			ID:          activity.ID,
			Type:        activity.Type,
			Description: activity.Description,
			Timestamp:   activity.CreatedAt.Format(time.RFC3339),
			Metadata:    string(activity.Metadata),
		}

		// Set enquiry details if found
		if enquiryFound {
			// Handle cases where name might be empty (guest submissions)
			displayName := enquiry.Name
			if displayName == "" {
				displayName = "Guest"
			}

			recentActivity.EnquiryName = displayName
			recentActivity.EnquirySlug = enquiry.Slug
			recentActivity.EnquiryEmail = enquiry.Email
			recentActivity.EnquiryMessage = enquiry.Description
			recentActivity.EnquiryStatus = enquiry.Status
			recentActivity.EnquiryPriority = enquiry.Priority
			recentActivity.EnquirySource = enquiry.Source
		} else {
			recentActivity.EnquiryName = "Unknown Enquiry"
			recentActivity.EnquiryEmail = ""
			recentActivity.EnquiryMessage = ""
		}

		// Set user details if found
		if userFound {
			if activityUser.Role == "system" {
				recentActivity.UserName = "Form Submission"
				recentActivity.UserRole = "system"
			} else {
				recentActivity.UserName = activityUser.Name
				recentActivity.UserRole = activityUser.Role
			}
		} else {
			recentActivity.UserName = "System"
			recentActivity.UserRole = "system"
		}

		recentActivities = append(recentActivities, recentActivity)
	}

	return recentActivities, nil
}

// GetActivities returns paginated activities for infinite scroll
func (h *DashboardHandler) GetActivities(c echo.Context) error {
	// Get authenticated user from context
	authUser := c.Get("user")
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}
	user := authUser.(*models.AuthUser)

	// Parse pagination parameters
	page := 1
	if p := c.QueryParam("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}

	limit := 20
	if l := c.QueryParam("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	days := 90 // Default to 90 days for infinite scroll
	if d := c.QueryParam("days"); d != "" {
		if parsed, err := strconv.Atoi(d); err == nil && parsed > 0 && parsed <= 365 {
			days = parsed
		}
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Calculate date range
	since := time.Now().AddDate(0, 0, -days)

	// Get total count for pagination info
	var totalCount int64
	if err := h.db.Model(&models.Activity{}).
		Where("organization_id = ? AND created_at >= ?", user.OrganizationID, since).
		Count(&totalCount).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to count activities")
	}

	// Fetch activities with pagination
	var activities []models.Activity
	if err := h.db.Where("organization_id = ? AND created_at >= ?", user.OrganizationID, since).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&activities).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch activities")
	}

	// Convert to response format with additional context
	var recentActivities []models.RecentActivity
	for _, activity := range activities {
		var enquiry models.Enquiry
		var activityUser models.User

		// Get enquiry details (organization-scoped)
		enquiryFound := h.db.Where("id = ? AND organization_id = ?", activity.EnquiryID, user.OrganizationID).
			First(&enquiry).Error == nil

		// Get user details (organization-scoped)
		userFound := h.db.Where("id = ? AND organization_id = ?", activity.UserID, user.OrganizationID).
			First(&activityUser).Error == nil

		// Prepare activity data
		recentActivity := models.RecentActivity{
			ID:          activity.ID,
			Type:        activity.Type,
			Description: activity.Description,
			Timestamp:   activity.CreatedAt.Format(time.RFC3339),
			Metadata:    string(activity.Metadata),
		}

		// Set enquiry details if found
		if enquiryFound {
			// Handle cases where name might be empty (guest submissions)
			displayName := enquiry.Name
			if displayName == "" {
				displayName = "Guest"
			}

			recentActivity.EnquiryName = displayName
			recentActivity.EnquirySlug = enquiry.Slug
			recentActivity.EnquiryEmail = enquiry.Email
			recentActivity.EnquiryMessage = enquiry.Description
			recentActivity.EnquiryStatus = enquiry.Status
			recentActivity.EnquiryPriority = enquiry.Priority
			recentActivity.EnquirySource = enquiry.Source
		} else {
			recentActivity.EnquiryName = "Unknown Enquiry"
			recentActivity.EnquiryEmail = ""
			recentActivity.EnquiryMessage = ""
		}

		// Set user details if found
		if userFound {
			if activityUser.Role == "system" {
				recentActivity.UserName = "Form Submission"
				recentActivity.UserRole = "system"
			} else {
				recentActivity.UserName = activityUser.Name
				recentActivity.UserRole = activityUser.Role
			}
		} else {
			recentActivity.UserName = "System"
			recentActivity.UserRole = "system"
		}

		recentActivities = append(recentActivities, recentActivity)
	}

	// Calculate pagination info
	totalPages := int((totalCount + int64(limit) - 1) / int64(limit))
	hasMore := page < totalPages

	// Response with pagination metadata
	response := map[string]interface{}{
		"activities": recentActivities,
		"pagination": map[string]interface{}{
			"page":        page,
			"limit":       limit,
			"total_count": totalCount,
			"total_pages": totalPages,
			"has_more":    hasMore,
		},
	}

	return c.JSON(http.StatusOK, response)
}

package handlers

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	"regexp"
	"strings"
	"time"

	"enquiry-management-system/middleware"
	"enquiry-management-system/models"
	"enquiry-management-system/services"
	"enquiry-management-system/utils"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type EnquiryHandler struct {
	db         *gorm.DB
	spamFilter *services.SpamFilterService
}

func NewEnquiryHandler(db *gorm.DB) *EnquiryHandler {
	return &EnquiryHandler{
		db:         db,
		spamFilter: services.NewSpamFilterService(db),
	}
}

func (h *EnquiryHandler) GetEnquiries(c echo.Context) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	var filters models.EnquiryFilters
	if err := c.Bind(&filters); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid query parameters")
	}

	// Set defaults
	if filters.Page <= 0 {
		filters.Page = 1
	}
	if filters.Limit <= 0 {
		filters.Limit = 10
	}

	// Use organization-scoped database
	db := middleware.GetOrganizationScopedDB(c, h.db)
	query := db.Model(&models.Enquiry{})

	// Role-based access control
	if user.IsAgent() {
		// Agents can only see enquiries assigned to them
		query = query.Where("assigned_to_id = ?", user.ID)
	}
	// Managers and Admins can see all enquiries (no additional filter)

	// Add filters
	if filters.Search != "" {
		searchTerm := "%" + filters.Search + "%"
		query = query.Where("name ILIKE ? OR email ILIKE ? OR description ILIKE ?", searchTerm, searchTerm, searchTerm)
	}

	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}

	if filters.Priority != "" {
		query = query.Where("priority = ?", filters.Priority)
	}

	if filters.AssignedToID != "" {
		// Parse UUID for assigned_to_id filter
		if assignedToID, err := uuid.Parse(filters.AssignedToID); err == nil {
			// Agents cannot filter by other users' assignments
			if user.IsAgent() && assignedToID != user.ID {
				return echo.NewHTTPError(http.StatusForbidden, "Agents can only view their own assigned enquiries")
			}
			query = query.Where("assigned_to_id = ?", assignedToID)
		}
	}

	// Get total count for pagination
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to count enquiries")
	}

	// Apply pagination and ordering
	var enquiries []models.Enquiry
	offset := (filters.Page - 1) * filters.Limit
	if err := query.Order("submitted_on DESC").Limit(filters.Limit).Offset(offset).Find(&enquiries).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch enquiries")
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"data":  enquiries,
		"total": total,
		"page":  filters.Page,
		"limit": filters.Limit,
	})
}

func (h *EnquiryHandler) GetEnquiry(c echo.Context) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid enquiry ID")
	}

	// Use organization-scoped database with role-based access control
	db := middleware.GetOrganizationScopedDB(c, h.db)
	query := db.Where("id = ?", id)

	// Agents can only view enquiries assigned to them
	if user.IsAgent() {
		query = query.Where("assigned_to_id = ?", user.ID)
	}
	// Managers and Admins can view all enquiries

	var enquiry models.Enquiry
	if err := query.First(&enquiry).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			if user.IsAgent() {
				return echo.NewHTTPError(http.StatusForbidden, "You can only view enquiries assigned to you")
			}
			return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch enquiry")
	}

	return c.JSON(http.StatusOK, enquiry)
}

func (h *EnquiryHandler) GetEnquiryBySlug(c echo.Context) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	slug := c.Param("slug")
	if slug == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Slug is required")
	}

	// Use organization-scoped database with role-based access control
	db := middleware.GetOrganizationScopedDB(c, h.db)
	query := db.Where("slug = ?", slug)

	// Agents can only view enquiries assigned to them
	if user.IsAgent() {
		query = query.Where("assigned_to_id = ?", user.ID)
	}
	// Managers and Admins can view all enquiries

	var enquiry models.Enquiry
	if err := query.First(&enquiry).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			if user.IsAgent() {
				return echo.NewHTTPError(http.StatusForbidden, "You can only view enquiries assigned to you")
			}
			return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch enquiry")
	}

	return c.JSON(http.StatusOK, enquiry)
}

func (h *EnquiryHandler) CreateEnquiry(c echo.Context) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	var req models.CreateEnquiryRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// Organization ID is always taken from authenticated user context for security
	// Validate required fields
	if req.Name == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Name is required")
	}
	if req.Email == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Email is required")
	}

	// Validate email format
	if !isValidEmail(req.Email) {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid email format")
	}

	// Generate slug from name
	slug := generateSlug(req.Name)

	// Set defaults and validate optional fields
	if req.Priority == "" {
		req.Priority = "Medium"
	} else if !isValidPriority(req.Priority) {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid priority. Must be one of: High, Medium, Low")
	}

	if req.Source == "" {
		req.Source = "Website Form"
	} else if !isValidSource(req.Source) {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid source. Must be one of: Website Form, Phone Call, Email, Referral, Social Media")
	}

	// Convert metadata to map for AI processing
	var metadataMap map[string]interface{}
	if req.Metadata != nil {
		metadataMap, _ = utils.ConvertJSONToMap(req.Metadata)
	}

	// Generate AI summary
	var summary string
	if req.Name != "" || req.Description != "" {
		aiSummary, err := utils.GenerateEnquirySummary(req.Name, req.Description, metadataMap)
		if err != nil {
			// Log the error but don't fail the request
			log.Printf("Failed to generate AI summary for enquiry '%s': %v", req.Name, err)
			// You could set a fallback summary or leave it empty
			summary = "" // or summary = "Summary generation failed"
		} else {
			summary = aiSummary
		}
	}

	// Process source field - if it's a URL, extract domain, otherwise keep as is
	processedSource := utils.ProcessSourceField(req.Source)
	if processedSource == "" {
		processedSource = "Website Form" // Default fallback
	}

	enquiry := models.Enquiry{
		OrganizationID: user.OrganizationID, // Always use user's organization
		Slug:           slug,
		Name:           req.Name,
		Email:          req.Email,
		Phone:          req.Phone,
		FormType:       req.FormType,
		Priority:       req.Priority,
		Source:         processedSource, // Store processed source (domain if URL, otherwise original)
		Description:    req.Description,
		Metadata:       req.Metadata,
		Summary:        summary, // Add the AI-generated summary
		SubmittedOn:    time.Now(),
	}

	if err := h.db.Create(&enquiry).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create enquiry")
	}

	// Perform spam analysis asynchronously (don't block enquiry creation)
	go func() {
		if err := h.spamFilter.AnalyzeEnquiry(&enquiry); err != nil {
			log.Printf("Failed to analyze spam for enquiry %s: %v", enquiry.ID, err)
		}
	}()

	// Create activity for enquiry creation using authenticated user
	activityHandler := NewActivityHandler(h.db)
	activityHandler.TrackEnquiryCreated(user.OrganizationID, enquiry.ID, user.ID, enquiry.Name)

	return c.JSON(http.StatusCreated, enquiry)
}

func (h *EnquiryHandler) UpdateEnquiry(c echo.Context) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid enquiry ID")
	}

	var req models.UpdateEnquiryRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// Use organization-scoped database with role-based access control
	db := middleware.GetOrganizationScopedDB(c, h.db)
	query := db.Where("id = ?", id)

	// Agents can only update enquiries assigned to them
	if user.IsAgent() {
		query = query.Where("assigned_to_id = ?", user.ID)
	}
	// Managers and Admins can update all enquiries

	// Find the enquiry
	var enquiry models.Enquiry
	if err := query.First(&enquiry).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			if user.IsAgent() {
				return echo.NewHTTPError(http.StatusForbidden, "You can only update enquiries assigned to you")
			}
			return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to find enquiry")
	}

	// Store original values for activity tracking
	originalStatus := enquiry.Status
	originalPriority := enquiry.Priority
	var originalAssignee string
	if enquiry.AssignedToID != nil {
		var assignedUser models.User
		if err := h.db.First(&assignedUser, "id = ?", *enquiry.AssignedToID).Error; err == nil {
			originalAssignee = assignedUser.Name
		}
	}

	// Update only non-empty fields (with validation)
	updates := make(map[string]interface{})

	// Use authenticated user for activity tracking
	activityHandler := NewActivityHandler(h.db)

	// AssignedToID - role-based assignment permissions
	if req.AssignedToID != nil {
		// Validate that the assigned user exists
		var assignedUser models.User
		if err := h.db.First(&assignedUser, "id = ?", *req.AssignedToID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return echo.NewHTTPError(http.StatusBadRequest, "Assigned user not found")
			}
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to validate assigned user")
		}

		// Role-based assignment validation
		if user.IsAgent() {
			// Agents can only assign to admin/manager users (escalation)
			if assignedUser.Role != "admin" && assignedUser.Role != "manager" {
				return echo.NewHTTPError(http.StatusForbidden, "Agents can only assign enquiries to administrators or managers")
			}
		}
		// Admin/Manager can assign to anyone (no additional validation needed)

		updates["assigned_to_id"] = req.AssignedToID

		// Track assignment change
		var newAssignee string
		if *req.AssignedToID != uuid.Nil {
			newAssignee = assignedUser.Name
		}

		if originalAssignee != newAssignee {
			activityHandler.TrackAssignmentChanged(user.OrganizationID, enquiry.ID, user.ID, originalAssignee, newAssignee)
		}
	}

	// Status - only update if non-empty and valid
	if strings.TrimSpace(req.Status) != "" {
		status := strings.TrimSpace(req.Status)
		if !isValidStatus(status) {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid status. Must be one of: New, In Progress, Closed")
		}
		if status != originalStatus {
			updates["status"] = status
			activityHandler.TrackStatusChanged(user.OrganizationID, enquiry.ID, user.ID, originalStatus, status)
		}
	}

	// Priority - only admins and managers can change priority
	if strings.TrimSpace(req.Priority) != "" {
		if user.IsAgent() {
			return echo.NewHTTPError(http.StatusForbidden, "Agents cannot change enquiry priority")
		}

		priority := strings.TrimSpace(req.Priority)
		if !isValidPriority(priority) {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid priority. Must be one of: High, Medium, Low")
		}
		if priority != originalPriority {
			updates["priority"] = priority
			activityHandler.TrackPriorityChanged(user.OrganizationID, enquiry.ID, user.ID, originalPriority, priority)
		}
	}

	// Source - only update if non-empty
	if strings.TrimSpace(req.Source) != "" {
		updates["source"] = strings.TrimSpace(req.Source)
	}

	// IsSpam - only admins and managers can change spam status
	if req.IsSpam != nil {
		if user.IsAgent() {
			return echo.NewHTTPError(http.StatusForbidden, "Agents cannot change spam status")
		}
		updates["is_spam"] = *req.IsSpam
	}

	if len(updates) == 0 {
		return echo.NewHTTPError(http.StatusBadRequest, "No fields to update")
	}

	// Perform update
	if err := h.db.Model(&enquiry).Updates(updates).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update enquiry")
	}

	// No general activity tracking - only specific activities are created above

	return c.JSON(http.StatusOK, enquiry)
}

func (h *EnquiryHandler) DeleteEnquiry(c echo.Context) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Only admins can delete enquiries
	if !user.IsAdmin() {
		return echo.NewHTTPError(http.StatusForbidden, "Only administrators can delete enquiries")
	}

	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid enquiry ID")
	}

	// Use organization-scoped database
	db := middleware.GetOrganizationScopedDB(c, h.db)

	// Check if enquiry exists before deletion
	var enquiry models.Enquiry
	if err := db.Where("id = ?", id).First(&enquiry).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to find enquiry")
	}

	// Perform soft delete
	result := db.Where("id = ?", id).Delete(&models.Enquiry{})
	if result.Error != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to delete enquiry")
	}

	if result.RowsAffected == 0 {
		return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Enquiry deleted successfully"})
}

// MarkEnquiryAsRead marks an enquiry as read
func (h *EnquiryHandler) MarkEnquiryAsRead(c echo.Context) error {
	return h.updateReadStatus(c, true)
}

// MarkEnquiryAsUnread marks an enquiry as unread
func (h *EnquiryHandler) MarkEnquiryAsUnread(c echo.Context) error {
	return h.updateReadStatus(c, false)
}

// GetEnquiryCounts returns unread counts for genuine and spam enquiries
func (h *EnquiryHandler) GetEnquiryCounts(c echo.Context) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Use organization-scoped database
	db := middleware.GetOrganizationScopedDB(c, h.db)

	// First DB call: Count unread genuine enquiries (not spam)
	var unreadGenuine int64
	genuineQuery := db.Model(&models.Enquiry{}).
		Where("is_spam = ?", false).
		Where("is_read = ?", false)

	if user.IsAgent() {
		genuineQuery = genuineQuery.Where("assigned_to_id = ?", user.ID)
	}

	genuineErr := genuineQuery.Count(&unreadGenuine).Error
	if genuineErr != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to count unread genuine enquiries")
	}

	// Second DB call: Count unread spam enquiries
	db = middleware.GetOrganizationScopedDB(c, h.db)
	var unreadSpam int64
	spamQuery := db.Model(&models.Enquiry{}).
		Where("is_spam = ?", true).
		Where("is_read = ?", false)

	if user.IsAgent() {
		spamQuery = spamQuery.Where("assigned_to_id = ?", user.ID)
	}

	spamErr := spamQuery.Count(&unreadSpam).Error
	if spamErr != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to count unread spam enquiries")
	}

	counts := map[string]int64{
		"unread_genuine": unreadGenuine,
		"unread_spam":    unreadSpam,
	}

	// Debug logging
	fmt.Printf("[GetEnquiryCounts] User: %s, Genuine: %d, Spam: %d\n", user.Email, unreadGenuine, unreadSpam)

	return c.JSON(http.StatusOK, counts)
}

// updateReadStatus is a helper function to update the read status of an enquiry
func (h *EnquiryHandler) updateReadStatus(c echo.Context, isRead bool) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	idParam := c.Param("id")
	id, err := uuid.Parse(idParam)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid enquiry ID")
	}

	// Use organization-scoped database with role-based access control
	db := middleware.GetOrganizationScopedDB(c, h.db)
	query := db.Where("id = ?", id)

	// Agents can only update enquiries assigned to them
	if user.IsAgent() {
		query = query.Where("assigned_to_id = ?", user.ID)
	}
	// Managers and Admins can update all enquiries

	// Find the enquiry
	var enquiry models.Enquiry
	if err := query.First(&enquiry).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			if user.IsAgent() {
				return echo.NewHTTPError(http.StatusForbidden, "You can only access enquiries assigned to you")
			}
			return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to find enquiry")
	}

	// Update the read status
	if err := h.db.Model(&enquiry).Update("is_read", isRead).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update read status")
	}

	// Update the enquiry object for response
	enquiry.IsRead = isRead

	return c.JSON(http.StatusOK, enquiry)
}

// Helper functions
func generateSlug(name string) string {
	// Simple slug generation - replace spaces with hyphens and convert to lowercase
	slug := strings.ToLower(strings.ReplaceAll(name, " ", "-"))
	// Add UUID suffix to ensure uniqueness
	uniqueID := uuid.New().String()[:8]
	return fmt.Sprintf("%s-%s", slug, uniqueID)
}

func isValidEmail(email string) bool {
	// Simple email validation regex
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

func isValidPriority(priority string) bool {
	validPriorities := []string{"High", "Medium", "Low"}
	for _, validPriority := range validPriorities {
		if priority == validPriority {
			return true
		}
	}
	return false
}

func isValidSource(source string) bool {
	validSources := []string{"Website Form", "Phone Call", "Email", "Referral", "Social Media"}
	for _, validSource := range validSources {
		if source == validSource {
			return true
		}
	}
	return false
}

func isValidStatus(status string) bool {
	validStatuses := []string{"New", "In Progress", "Closed"}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

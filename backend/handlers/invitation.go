package handlers

import (
	"net/http"
	"time"

	"enquiry-management-system/config"
	"enquiry-management-system/middleware"
	"enquiry-management-system/models"
	"enquiry-management-system/services/email"
	"enquiry-management-system/utils"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type InvitationHandler struct {
	db           *gorm.DB
	cfg          *config.Config
	emailService *email.Service
	jwtService   *utils.JWTService
}

func NewInvitationHandler(db *gorm.DB, cfg *config.Config, emailService *email.Service) *InvitationHandler {
	jwtService := utils.NewJWTService(&cfg.JWT)

	return &InvitationHandler{
		db:           db,
		cfg:          cfg,
		emailService: emailService,
		jwtService:   jwtService,
	}
}

// AcceptInvitation handles invitation acceptance with re-verification
func (h *InvitationHandler) AcceptInvitation(c echo.Context) error {
	var req models.AcceptInvitationRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "invalid_request",
			"message": "Invalid request body",
		})
	}

	// Validate required fields
	if req.Token == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "missing_token",
			"message": "Invitation token is required",
		})
	}

	if req.Name == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "missing_name",
			"message": "Name is required",
		})
	}

	// Password is optional for Google signin, required for email signup
	// Frontend should handle this validation based on signup method

	// Find invitation by token
	var invitation models.Invitation
	if err := h.db.Where("token = ?", req.Token).First(&invitation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.JSON(http.StatusNotFound, map[string]interface{}{
				"success": false,
				"error":   "invitation_not_found",
				"message": "Invalid invitation link",
			})
		}
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "server_error",
			"message": "Failed to verify invitation",
		})
	}

	// Fetch organization separately
	var organization models.Organization
	if err := h.db.First(&organization, "id = ?", invitation.OrganizationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.JSON(http.StatusNotFound, map[string]interface{}{
				"success": false,
				"error":   "organization_not_found",
				"message": "Organization not found",
			})
		}
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "server_error",
			"message": "Failed to fetch organization",
		})
	}

	// Set organization in invitation for response
	invitation.Organization = organization

	// Re-verify invitation status (same checks as VerifyInvitation)
	if invitation.IsExpired() {
		return c.JSON(http.StatusGone, map[string]interface{}{
			"success":    false,
			"error":      "invitation_expired",
			"message":    "This invitation has expired",
			"expired_at": invitation.ExpiresAt.Format(time.RFC3339),
		})
	}

	if invitation.IsAccepted() {
		acceptedAt := ""
		if invitation.AcceptedAt != nil {
			acceptedAt = invitation.AcceptedAt.Format(time.RFC3339)
		}
		return c.JSON(http.StatusConflict, map[string]interface{}{
			"success":     false,
			"error":       "invitation_already_accepted",
			"message":     "This invitation has already been accepted",
			"accepted_at": acceptedAt,
		})
	}

	if invitation.Status == "cancelled" {
		return c.JSON(http.StatusGone, map[string]interface{}{
			"success": false,
			"error":   "invitation_cancelled",
			"message": "This invitation has been cancelled",
		})
	}

	if !invitation.IsPending() {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "invitation_invalid_status",
			"message": "Invitation is not available for acceptance",
			"status":  invitation.Status,
		})
	}

	// Start transaction
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update user with name and set status to active
	now := time.Now()
	if err := tx.Model(&models.User{}).Where("id = ?", invitation.UserID).Updates(map[string]interface{}{
		"name":   req.Name,
		"status": "active",
	}).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "server_error",
			"message": "Failed to activate user account",
		})
	}

	// Update invitation status
	if err := tx.Model(&invitation).Updates(map[string]interface{}{
		"status":      "accepted",
		"accepted_at": &now,
	}).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "server_error",
			"message": "Failed to update invitation status",
		})
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "server_error",
			"message": "Failed to complete invitation acceptance",
		})
	}

	// Return success response with user and organization details
	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"message": "Invitation accepted successfully",
		"user": map[string]interface{}{
			"email":             invitation.Email,
			"name":              req.Name,
			"role":              invitation.Role,
			"organization_name": invitation.Organization.Name,
			"organization_slug": invitation.Organization.Slug,
		},
	})
}

// VerifyInvitation verifies invitation token and returns invitation details
func (h *InvitationHandler) VerifyInvitation(c echo.Context) error {
	token := c.QueryParam("token")
	if token == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"valid":   false,
			"error":   "missing_token",
			"message": "Invitation token is required",
		})
	}

	var invitation models.Invitation
	if err := h.db.Where("token = ?", token).First(&invitation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.JSON(http.StatusNotFound, map[string]interface{}{
				"valid":   false,
				"error":   "invitation_not_found",
				"message": "Invalid invitation link",
			})
		}
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"valid":   false,
			"error":   "server_error",
			"message": "Failed to verify invitation",
		})
	}

	// Fetch organization separately
	var organization models.Organization
	if err := h.db.First(&organization, "id = ?", invitation.OrganizationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.JSON(http.StatusNotFound, map[string]interface{}{
				"valid":   false,
				"error":   "organization_not_found",
				"message": "Organization not found",
			})
		}
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"valid":   false,
			"error":   "server_error",
			"message": "Failed to fetch organization",
		})
	}

	// Set organization in invitation for response
	invitation.Organization = organization

	// Check if invitation is expired
	if invitation.IsExpired() {
		return c.JSON(http.StatusGone, map[string]interface{}{
			"valid":      false,
			"error":      "invitation_expired",
			"message":    "This invitation has expired",
			"expired_at": invitation.ExpiresAt.Format(time.RFC3339),
		})
	}

	// Check if invitation is already accepted
	if invitation.IsAccepted() {
		acceptedAt := ""
		if invitation.AcceptedAt != nil {
			acceptedAt = invitation.AcceptedAt.Format(time.RFC3339)
		}
		return c.JSON(http.StatusConflict, map[string]interface{}{
			"valid":       false,
			"error":       "invitation_already_accepted",
			"message":     "This invitation has already been accepted",
			"accepted_at": acceptedAt,
		})
	}

	// Check if invitation is cancelled
	if invitation.Status == "cancelled" {
		return c.JSON(http.StatusGone, map[string]interface{}{
			"valid":   false,
			"error":   "invitation_cancelled",
			"message": "This invitation has been cancelled",
		})
	}

	// Check if invitation status is pending
	if !invitation.IsPending() {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"valid":   false,
			"error":   "invitation_invalid_status",
			"message": "Invitation is not available for acceptance",
			"status":  invitation.Status,
		})
	}

	// Calculate hours until expiry
	hoursUntilExpiry := int(time.Until(invitation.ExpiresAt).Hours())
	if hoursUntilExpiry < 0 {
		hoursUntilExpiry = 0
	}

	// Return valid invitation details
	response := map[string]interface{}{
		"valid": true,
		"invitation": map[string]interface{}{
			"email":             invitation.Email,
			"role":              invitation.Role,
			"organization_name": invitation.Organization.Name,
			"organization_slug": invitation.Organization.Slug,
			"expires_at":        invitation.ExpiresAt.Format(time.RFC3339),
			"expires_in_hours":  hoursUntilExpiry,
		},
	}

	return c.JSON(http.StatusOK, response)
}

// GetInvitation retrieves invitation details by token (for invitation acceptance page)
// DEPRECATED: Use VerifyInvitation instead
func (h *InvitationHandler) GetInvitation(c echo.Context) error {
	token := c.QueryParam("token")
	if token == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Token is required")
	}

	var invitation models.Invitation
	if err := h.db.Where("token = ?", token).First(&invitation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Invalid or expired invitation")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to find invitation")
	}

	// Fetch organization separately
	var organization models.Organization
	if err := h.db.First(&organization, "id = ?", invitation.OrganizationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Organization not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch organization")
	}

	// Set organization in invitation for response
	invitation.Organization = organization

	// Check if invitation is expired
	if invitation.IsExpired() {
		return echo.NewHTTPError(http.StatusBadRequest, "Invitation has expired")
	}

	// Check if invitation is already accepted
	if invitation.IsAccepted() {
		return echo.NewHTTPError(http.StatusBadRequest, "Invitation has already been accepted")
	}

	// Check if invitation status is pending
	if !invitation.IsPending() {
		return echo.NewHTTPError(http.StatusBadRequest, "Invitation is not available for acceptance")
	}

	response := models.InvitationResponse{
		ID:             invitation.ID.String(),
		Email:          invitation.Email,
		Role:           invitation.Role,
		Status:         invitation.Status,
		ExpiresAt:      invitation.ExpiresAt.Format(time.RFC3339),
		OrganizationID: invitation.OrganizationID.String(),
		CreatedAt:      invitation.CreatedAt.Format(time.RFC3339),
	}

	return c.JSON(http.StatusOK, response)
}

// ListInvitations lists all invitations for the organization (admin only)
func (h *InvitationHandler) ListInvitations(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Check if requesting user is admin
	if authUser.Role != "admin" {
		return echo.NewHTTPError(http.StatusForbidden, "Only admins can view invitations")
	}

	var invitations []models.Invitation
	if err := h.db.Where("organization_id = ?", authUser.OrganizationID).Order("created_at DESC").Find(&invitations).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch invitations")
	}

	// Convert to response format
	responses := make([]models.InvitationResponse, len(invitations))
	for i, inv := range invitations {
		acceptedAt := ""
		if inv.AcceptedAt != nil {
			acceptedAt = inv.AcceptedAt.Format(time.RFC3339)
		}

		// Fetch inviter separately
		var inviter models.User
		inviterName := "Unknown"
		if err := h.db.First(&inviter, "id = ?", inv.InvitedBy).Error; err == nil {
			inviterName = inviter.Name
		}

		responses[i] = models.InvitationResponse{
			ID:             inv.ID.String(),
			Email:          inv.Email,
			Role:           inv.Role,
			Status:         inv.Status,
			ExpiresAt:      inv.ExpiresAt.Format(time.RFC3339),
			AcceptedAt:     acceptedAt,
			InvitedBy:      inv.InvitedBy.String(),
			InviterName:    inviterName,
			OrganizationID: inv.OrganizationID.String(),
			CreatedAt:      inv.CreatedAt.Format(time.RFC3339),
		}
	}

	return c.JSON(http.StatusOK, responses)
}

// CancelInvitation cancels a pending invitation (admin only)
func (h *InvitationHandler) CancelInvitation(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Check if requesting user is admin
	if authUser.Role != "admin" {
		return echo.NewHTTPError(http.StatusForbidden, "Only admins can cancel invitations")
	}

	// Parse invitation ID
	invitationID := c.Param("id")
	invitationUUID, err := uuid.Parse(invitationID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid invitation ID")
	}

	// Find invitation
	var invitation models.Invitation
	if err := h.db.Where("id = ? AND organization_id = ?", invitationUUID, authUser.OrganizationID).First(&invitation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Invitation not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to find invitation")
	}

	// Check if invitation can be cancelled
	if !invitation.IsPending() {
		return echo.NewHTTPError(http.StatusBadRequest, "Only pending invitations can be cancelled")
	}

	// Update invitation status
	if err := h.db.Model(&invitation).Update("status", "cancelled").Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to cancel invitation")
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "Invitation cancelled successfully",
	})
}

// OnboardInvitedUser handles onboarding of invited users after Auth signup
func (h *InvitationHandler) OnboardInvitedUser(c echo.Context) error {
	var req models.OnboardInvitedUserRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "invalid_request",
			"message": "Invalid request body",
		})
	}

	// Validate required fields
	if req.InvitationToken == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "missing_invitation_token",
			"message": "Invitation token is required",
		})
	}

	if req.AuthToken == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "missing_auth_token",
			"message": "Auth token is required",
		})
	}

	if req.Name == "" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "missing_name",
			"message": "Name is required",
		})
	}

	// Find invitation by token
	var invitation models.Invitation
	if err := h.db.Where("token = ?", req.InvitationToken).First(&invitation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.JSON(http.StatusNotFound, map[string]interface{}{
				"success": false,
				"error":   "invitation_not_found",
				"message": "Invalid invitation token",
			})
		}
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "server_error",
			"message": "Failed to verify invitation",
		})
	}

	// Fetch organization separately
	var organization models.Organization
	if err := h.db.First(&organization, "id = ?", invitation.OrganizationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return c.JSON(http.StatusNotFound, map[string]interface{}{
				"success": false,
				"error":   "organization_not_found",
				"message": "Organization not found",
			})
		}
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "server_error",
			"message": "Failed to fetch organization",
		})
	}

	// Set organization in invitation for response
	invitation.Organization = organization

	// Validate invitation status and expiry
	if invitation.Status != "pending" {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "invitation_not_pending",
			"message": "Invitation has already been processed",
		})
	}

	if time.Now().After(invitation.ExpiresAt) {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "invitation_expired",
			"message": "Invitation has expired",
		})
	}

	// Validate Auth JWT token
	claims, err := h.jwtService.ValidateToken(req.AuthToken)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, map[string]interface{}{
			"success": false,
			"error":   "invalid_auth_token",
			"message": "Invalid or expired Auth authentication token",
		})
	}

	// Extract Auth user ID and email from token
	authUserID, err := uuid.Parse(claims.UserID)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "invalid_user_id",
			"message": "Invalid user ID in Auth token",
		})
	}

	// Validate email consistency
	if claims.Email != invitation.Email {
		return c.JSON(http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "email_mismatch",
			"message": "Email in Auth token does not match invitation email",
		})
	}

	// Check if Auth user ID is already linked to another user
	var existingUser models.User
	if err := h.db.Where("auth_id = ?", authUserID).First(&existingUser).Error; err == nil {
		return c.JSON(http.StatusConflict, map[string]interface{}{
			"success": false,
			"error":   "user_already_linked",
			"message": "This Auth account is already linked to another user",
		})
	} else if err != gorm.ErrRecordNotFound {
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "server_error",
			"message": "Failed to check user linkage",
		})
	}

	// Start database transaction
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Update user with auth_id, name, and activate status
	now := time.Now()
	if err := tx.Model(&models.User{}).Where("id = ?", invitation.UserID).Updates(map[string]interface{}{
		"auth_id": authUserID,
		"name":    req.Name,
		"status":  "active",
	}).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "server_error",
			"message": "Failed to update user account",
		})
	}

	// Update invitation status
	if err := tx.Model(&invitation).Updates(map[string]interface{}{
		"status":      "accepted",
		"accepted_at": &now,
	}).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "server_error",
			"message": "Failed to update invitation status",
		})
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "server_error",
			"message": "Failed to complete onboarding",
		})
	}

	// Get updated user for response
	var user models.User
	if err := h.db.Where("id = ?", invitation.UserID).First(&user).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]interface{}{
			"success": false,
			"error":   "server_error",
			"message": "Failed to retrieve user data",
		})
	}

	// Return success response
	return c.JSON(http.StatusOK, map[string]interface{}{
		"success": true,
		"user": map[string]interface{}{
			"id":    user.ID.String(),
			"name":  user.Name,
			"email": user.Email,
			"role":  user.Role,
		},
		"organization": map[string]interface{}{
			"id":   invitation.Organization.ID.String(),
			"name": invitation.Organization.Name,
			"slug": invitation.Organization.Slug,
		},
		"redirect_url": "/org/" + invitation.Organization.Slug,
	})
}

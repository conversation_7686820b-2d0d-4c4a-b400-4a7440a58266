package handlers

import (
	"net/http"
	"time"

	"enquiry-management-system/middleware"
	"enquiry-management-system/models"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type NoteHandler struct {
	db *gorm.DB
}

func NewNoteHandler(db *gorm.DB) *NoteHandler {
	return &NoteHandler{db: db}
}

// CreateNote adds a new note to an enquiry
func (h *NoteHandler) CreateNote(c echo.Context) error {
	// Get authenticated user
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	enquiryID := c.Param("id")
	if enquiryID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Enquiry ID is required")
	}

	// Parse enquiry ID
	enquiryUUID, err := uuid.Parse(enquiryID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid enquiry ID format")
	}

	// Check if enquiry exists
	var enquiry models.Enquiry
	if err := h.db.First(&enquiry, "id = ?", enquiryUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Database error")
	}

	// Bind request
	var req models.CreateNoteRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// Validate request
	if req.Content == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Content is required")
	}

	// Get user details from database (we have the ID from auth context)
	var user models.User
	if err := h.db.First(&user, "id = ?", authUser.ID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "User not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Database error")
	}

	// Create note
	note := models.Note{
		EnquiryID: enquiryUUID,
		UserID:    authUser.ID,
		Content:   req.Content,
	}

	if err := h.db.Create(&note).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create note")
	}

	// Create activity for note addition
	activityHandler := NewActivityHandler(h.db)
	activityHandler.TrackNoteAdded(authUser.OrganizationID, note.EnquiryID, note.UserID, note.Content)

	// Prepare response
	response := models.NoteResponse{
		ID:        note.ID,
		EnquiryID: note.EnquiryID,
		UserID:    note.UserID,
		Content:   note.Content,
		CreatedAt: note.CreatedAt.Format(time.RFC3339),
		UpdatedAt: note.UpdatedAt.Format(time.RFC3339),
		User:      &user,
	}

	return c.JSON(http.StatusCreated, response)
}

// GetNotes retrieves all notes for an enquiry
func (h *NoteHandler) GetNotes(c echo.Context) error {
	enquiryID := c.Param("id")
	if enquiryID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Enquiry ID is required")
	}

	// Parse enquiry ID
	enquiryUUID, err := uuid.Parse(enquiryID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid enquiry ID format")
	}

	// Check if enquiry exists
	var enquiry models.Enquiry
	if err := h.db.First(&enquiry, "id = ?", enquiryUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Database error")
	}

	// Get notes with user information
	var notes []models.Note
	if err := h.db.Where("enquiry_id = ?", enquiryUUID).Order("created_at DESC").Find(&notes).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch notes")
	}

	// Prepare response with user information
	responses := make([]models.NoteResponse, 0)
	for _, note := range notes {
		var user models.User
		h.db.First(&user, "id = ?", note.UserID)

		response := models.NoteResponse{
			ID:        note.ID,
			EnquiryID: note.EnquiryID,
			UserID:    note.UserID,
			Content:   note.Content,
			CreatedAt: note.CreatedAt.Format(time.RFC3339),
			UpdatedAt: note.UpdatedAt.Format(time.RFC3339),
			User:      &user,
		}
		responses = append(responses, response)
	}

	return c.JSON(http.StatusOK, responses)
}

// DeleteNote deletes a specific note
func (h *NoteHandler) DeleteNote(c echo.Context) error {
	// Get authenticated user
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	enquiryID := c.Param("id")
	noteID := c.Param("note_id")

	if enquiryID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Enquiry ID is required")
	}
	if noteID == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Note ID is required")
	}

	// Parse IDs
	enquiryUUID, err := uuid.Parse(enquiryID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid enquiry ID format")
	}

	noteUUID, err := uuid.Parse(noteID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid note ID format")
	}

	// Check if enquiry exists
	var enquiry models.Enquiry
	if err := h.db.First(&enquiry, "id = ?", enquiryUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Database error")
	}

	// Check if note exists and belongs to the enquiry
	var note models.Note
	if err := h.db.First(&note, "id = ? AND enquiry_id = ?", noteUUID, enquiryUUID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Note not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Database error")
	}

	// Create activity for note deletion before deleting (use authenticated user for tracking)
	activityHandler := NewActivityHandler(h.db)
	activityHandler.TrackNoteDeleted(authUser.OrganizationID, note.EnquiryID, authUser.ID)

	// Delete the note
	if err := h.db.Delete(&note).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to delete note")
	}

	return c.JSON(http.StatusOK, map[string]string{
		"message": "Note deleted successfully",
	})
}

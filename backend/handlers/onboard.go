package handlers

import (
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"enquiry-management-system/models"

	"github.com/golang-jwt/jwt"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type OnboardHandler struct {
	db *gorm.DB
}

func NewOnboardHandler(db *gorm.DB) *OnboardHandler {
	return &OnboardHandler{db: db}
}

// OnboardUser handles the user onboarding process
func (h *OnboardHandler) OnboardUser(c echo.Context) error {
	// Parse request body
	var req models.OnboardRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request format")
	}

	// Validate organization name
	if strings.TrimSpace(req.OrganizationName) == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Organization name is required")
	}

	if len(req.OrganizationName) < 2 || len(req.OrganizationName) > 100 {
		return echo.NewHTTPError(http.StatusBadRequest, "Organization name must be between 2 and 100 characters")
	}

	// Validate and extract domain
	parsedDomain, err := h.validateAndExtractDomain(req.Domain)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// Check if domain is already taken by another organization
	var existingOrg models.Organization
	if err := h.db.Where("domain = ?", parsedDomain).First(&existingOrg).Error; err == nil {
		return echo.NewHTTPError(http.StatusConflict, "Domain is already taken by another organization")
	}

	// Extract user data from JWT token
	onboardingData, err := h.extractUserDataFromJWT(c)
	if err != nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Invalid authentication token")
	}

	// Check if user already exists
	var existingUser models.User
	if err := h.db.Where("auth_id = ?", onboardingData.UserID).First(&existingUser).Error; err == nil {
		return echo.NewHTTPError(http.StatusConflict, "User already onboarded")
	}

	// Start transaction
	tx := h.db.Begin()
	if tx.Error != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to start transaction")
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Parse user ID
	userID, err := uuid.Parse(onboardingData.UserID)
	if err != nil {
		tx.Rollback()
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid user ID format")
	}

	// Generate organization slug
	orgSlug := h.generateSlug(req.OrganizationName)

	// Ensure slug is unique
	orgSlug, err = h.ensureUniqueSlug(tx, orgSlug)
	if err != nil {
		tx.Rollback()
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to generate unique organization slug")
	}

	// Create organization
	organization := models.Organization{
		Name:      req.OrganizationName,
		Slug:      orgSlug,
		Domain:    parsedDomain,
		Status:    "active",
		Plan:      "starter",
		CreatedBy: userID,
		MaxUsers:  10,
	}
	organization.ID = uuid.New()

	// Generate unique API key for the organization
	if err := organization.GenerateUniqueAPIKey(tx); err != nil {
		tx.Rollback()
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to generate unique API key: "+err.Error())
	}

	// Generate unique form ID for the organization
	if err := organization.GenerateUniqueFormID(tx); err != nil {
		tx.Rollback()
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to generate unique form ID: "+err.Error())
	}

	if err := tx.Create(&organization).Error; err != nil {
		tx.Rollback()
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create organization")
	}

	// Create user (first user is always admin)
	user := models.User{
		OrganizationID: organization.ID,
		Name:           onboardingData.Name,
		Email:          onboardingData.Email,
		AuthID:         &userID,
		Role:           "admin", // First user is always admin
		Status:         "active",
		JoinedDate:     time.Now(),
	}
	user.ID = userID

	if err := tx.Create(&user).Error; err != nil {
		tx.Rollback()
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create user")
	}

	// Create system user for automated actions (form submissions, etc.)
	systemUser := models.User{
		OrganizationID: organization.ID,
		Name:           "System",
		Email:          fmt.Sprintf("<EMAIL>", orgSlug),
		AuthID:         nil, // System user doesn't have Supabase auth
		Role:           "system",
		Status:         "active",
		JoinedDate:     time.Now(),
	}

	if err := tx.Create(&systemUser).Error; err != nil {
		tx.Rollback()
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create system user")
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to complete onboarding")
	}

	// Prepare response
	response := models.OnboardResponse{
		User:         user,
		Organization: organization,
		Message:      "Onboarding completed successfully",
	}

	return c.JSON(http.StatusCreated, response)
}

// extractUserDataFromJWT extracts user data from the JWT token
func (h *OnboardHandler) extractUserDataFromJWT(c echo.Context) (*models.OnboardingData, error) {
	// Get token from Authorization header
	authHeader := c.Request().Header.Get("Authorization")
	if authHeader == "" {
		return nil, fmt.Errorf("missing authorization header")
	}

	// Extract token from "Bearer <token>"
	tokenString := strings.TrimPrefix(authHeader, "Bearer ")
	if tokenString == authHeader {
		return nil, fmt.Errorf("invalid authorization header format")
	}

	// Parse token without verification (we trust Supabase tokens)
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %v", err)
	}

	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}

	// Extract user data
	userID, ok := claims["sub"].(string)
	if !ok || userID == "" {
		return nil, fmt.Errorf("missing user ID in token")
	}

	email, ok := claims["email"].(string)
	if !ok || email == "" {
		return nil, fmt.Errorf("missing email in token")
	}

	// Name might be in different fields depending on auth provider
	var name string
	if fullName, ok := claims["name"].(string); ok && fullName != "" {
		name = fullName
	} else if userMetadata, ok := claims["user_metadata"].(map[string]interface{}); ok {
		if fullName, ok := userMetadata["full_name"].(string); ok && fullName != "" {
			name = fullName
		} else if firstName, ok := userMetadata["name"].(string); ok && firstName != "" {
			name = firstName
		}
	}

	// Fallback to email prefix if no name found
	if name == "" {
		name = strings.Split(email, "@")[0]
	}

	return &models.OnboardingData{
		UserID: userID,
		Email:  email,
		Name:   name,
	}, nil
}

// generateSlug creates a URL-friendly slug from organization name
func (h *OnboardHandler) generateSlug(name string) string {
	// Convert to lowercase
	slug := strings.ToLower(name)

	// Replace spaces and special characters with hyphens
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	slug = reg.ReplaceAllString(slug, "-")

	// Remove leading/trailing hyphens
	slug = strings.Trim(slug, "-")

	// Limit length
	if len(slug) > 50 {
		slug = slug[:50]
	}

	return slug
}

// ensureUniqueSlug ensures the slug is unique by appending numbers if needed
func (h *OnboardHandler) ensureUniqueSlug(tx *gorm.DB, baseSlug string) (string, error) {
	slug := baseSlug
	counter := 1

	for {
		var count int64
		if err := tx.Model(&models.Organization{}).Where("slug = ?", slug).Count(&count).Error; err != nil {
			return "", err
		}

		if count == 0 {
			return slug, nil
		}

		slug = fmt.Sprintf("%s-%d", baseSlug, counter)
		counter++

		// Prevent infinite loop
		if counter > 1000 {
			return "", fmt.Errorf("unable to generate unique slug")
		}
	}
}

// validateAndExtractDomain validates and extracts clean domain from URL
func (h *OnboardHandler) validateAndExtractDomain(domain string) (string, error) {
	if domain == "" {
		return "", fmt.Errorf("domain is required")
	}

	// Add protocol if missing
	fullURL := domain
	if !strings.HasPrefix(domain, "http://") && !strings.HasPrefix(domain, "https://") {
		fullURL = "https://" + domain
	}

	// Parse and validate URL
	_, err := url.Parse(fullURL)
	if err != nil {
		return "", fmt.Errorf("invalid website URL")
	}

	// Extract clean domain from URL
	parsedDomain := strings.TrimPrefix(strings.TrimPrefix(fullURL, "https://"), "http://")

	// Remove any path, query params, or fragments
	if idx := strings.Index(parsedDomain, "/"); idx != -1 {
		parsedDomain = parsedDomain[:idx]
	}
	if idx := strings.Index(parsedDomain, "?"); idx != -1 {
		parsedDomain = parsedDomain[:idx]
	}
	if idx := strings.Index(parsedDomain, "#"); idx != -1 {
		parsedDomain = parsedDomain[:idx]
	}

	// Remove port if present
	if idx := strings.Index(parsedDomain, ":"); idx != -1 {
		parsedDomain = parsedDomain[:idx]
	}

	// Convert to lowercase for consistency
	parsedDomain = strings.ToLower(parsedDomain)

	// Ensure domain has at least one dot and reasonable length
	if !strings.Contains(parsedDomain, ".") || len(parsedDomain) < 4 {
		return "", fmt.Errorf("invalid website URL format")
	}

	// Basic domain format validation
	if strings.HasPrefix(parsedDomain, ".") || strings.HasSuffix(parsedDomain, ".") {
		return "", fmt.Errorf("invalid domain format")
	}

	return parsedDomain, nil
}

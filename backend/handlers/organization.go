package handlers

import (
	"errors"
	"log"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"

	"enquiry-management-system/models"
)

type OrganizationHandler struct {
	db *gorm.DB
}

func NewOrganizationHandler(db *gorm.DB) *OrganizationHandler {
	return &OrganizationHandler{db: db}
}

// GetCurrentOrganization gets the current user's organization details
func (h *OrganizationHandler) GetCurrentOrganization(c echo.Context) error {
	// Get authenticated user from context
	authUser := c.Get("user").(*models.AuthUser)

	var org models.Organization
	if err := h.db.First(&org, "id = ? AND status = ?", authUser.OrganizationID, "active").Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Organization not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch organization")
	}

	response := models.OrganizationResponse{
		ID:        org.ID.String(),
		Name:      org.Name,
		Slug:      org.Slug,
		Domain:    org.Domain,
		Status:    org.Status,
		Plan:      org.Plan,
		Settings:  org.Settings,
		MaxUsers:  org.MaxUsers,
		CreatedAt: org.CreatedAt.Format("2006-01-02T15:04:05Z"),
		CreatedBy: org.CreatedBy.String(),
	}

	return c.JSON(http.StatusOK, response)
}

// UpdateCurrentOrganization updates the current user's organization
func (h *OrganizationHandler) UpdateCurrentOrganization(c echo.Context) error {
	// Get authenticated user from context
	authUser := c.Get("user").(*models.AuthUser)

	// Only org admins can update organization
	if authUser.Role != "admin" {
		return echo.NewHTTPError(http.StatusForbidden, "Only organization administrators can update organization settings")
	}

	var req models.UpdateOrganizationRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// Validate request
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// Get current organization
	var org models.Organization
	if err := h.db.First(&org, "id = ? AND status = ?", authUser.OrganizationID, "active").Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Organization not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch organization")
	}

	// Update fields if provided
	if req.Name != "" {
		org.Name = req.Name
	}
	if req.Domain != "" {
		// Check if domain is already taken by another organization
		var existingOrg models.Organization
		if err := h.db.Where("domain = ? AND id != ?", req.Domain, org.ID).First(&existingOrg).Error; err == nil {
			return echo.NewHTTPError(http.StatusConflict, "Domain is already taken by another organization")
		}
		org.Domain = req.Domain
	}
	if req.Plan != "" {
		org.Plan = req.Plan
		// Update max users based on plan
		limits := org.GetPlanLimits()
		org.MaxUsers = limits.MaxUsers
	}
	if req.MaxUsers > 0 && req.Plan == "enterprise" {
		// Only enterprise plans can have custom user limits
		org.MaxUsers = req.MaxUsers
	}
	if req.Settings != nil {
		org.Settings = req.Settings
	}

	// Save changes
	if err := h.db.Save(&org).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update organization")
	}

	response := models.OrganizationResponse{
		ID:        org.ID.String(),
		Name:      org.Name,
		Slug:      org.Slug,
		Domain:    org.Domain,
		Status:    org.Status,
		Plan:      org.Plan,
		Settings:  org.Settings,
		MaxUsers:  org.MaxUsers,
		CreatedAt: org.CreatedAt.Format("2006-01-02T15:04:05Z"),
		CreatedBy: org.CreatedBy.String(),
	}

	return c.JSON(http.StatusOK, response)
}

// GetOrganizationStats gets statistics for the current organization
func (h *OrganizationHandler) GetOrganizationStats(c echo.Context) error {
	// Get authenticated user from context
	authUser := c.Get("user").(*models.AuthUser)

	// Only admins and managers can view stats
	if authUser.Role != "admin" && authUser.Role != "manager" {
		return echo.NewHTTPError(http.StatusForbidden, "Insufficient permissions to view organization statistics")
	}

	var stats models.OrganizationStatsResponse

	// Get organization
	var org models.Organization
	if err := h.db.First(&org, "id = ?", authUser.OrganizationID).Error; err != nil {
		return echo.NewHTTPError(http.StatusNotFound, "Organization not found")
	}

	// Count total users
	h.db.Model(&models.User{}).Where("organization_id = ?", authUser.OrganizationID).Count(&stats.TotalUsers)

	// Count active users
	h.db.Model(&models.User{}).Where("organization_id = ? AND status = ?", authUser.OrganizationID, "active").Count(&stats.ActiveUsers)

	// Count total enquiries
	h.db.Model(&models.Enquiry{}).Where("organization_id = ?", authUser.OrganizationID).Count(&stats.TotalEnquiries)

	// Set plan limits
	stats.MaxUsers = org.MaxUsers
	limits := org.GetPlanLimits()
	stats.PlanLimits.Users = limits.MaxUsers
	stats.PlanLimits.Storage = limits.StorageGB
	stats.PlanLimits.Features = limits.Features

	return c.JSON(http.StatusOK, stats)
}

// CreateOrganization creates a new organization (super admin only)
func (h *OrganizationHandler) CreateOrganization(c echo.Context) error {
	var req models.CreateOrganizationRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// Validate request
	if err := c.Validate(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, err.Error())
	}

	// Check if slug is already taken
	var existingOrg models.Organization
	if err := h.db.Where("slug = ?", req.Slug).First(&existingOrg).Error; err == nil {
		return echo.NewHTTPError(http.StatusConflict, "Organization slug is already taken")
	}

	// Check if domain is already taken (if provided)
	if req.Domain != "" {
		if err := h.db.Where("domain = ?", req.Domain).First(&existingOrg).Error; err == nil {
			return echo.NewHTTPError(http.StatusConflict, "Domain is already taken")
		}
	}

	// Get authenticated user (should be super admin)
	authUser := c.Get("user").(*models.AuthUser)

	// Set default plan if not provided
	if req.Plan == "" {
		req.Plan = "starter"
	}

	// Create organization
	org := models.Organization{
		Name:      req.Name,
		Slug:      strings.ToLower(req.Slug),
		Domain:    req.Domain,
		Status:    "active",
		Plan:      req.Plan,
		CreatedBy: authUser.ID,
		MaxUsers:  models.PlanLimits[req.Plan].MaxUsers,
	}

	// Generate unique API key for the organization
	if err := org.GenerateUniqueAPIKey(h.db); err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to generate unique API key: "+err.Error())
	}

	// Generate unique form ID for the organization
	if err := org.GenerateUniqueFormID(h.db); err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to generate unique form ID: "+err.Error())
	}

	if err := h.db.Create(&org).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create organization")
	}

	response := models.OrganizationResponse{
		ID:        org.ID.String(),
		Name:      org.Name,
		Slug:      org.Slug,
		Domain:    org.Domain,
		Status:    org.Status,
		Plan:      org.Plan,
		Settings:  org.Settings,
		MaxUsers:  org.MaxUsers,
		CreatedAt: org.CreatedAt.Format("2006-01-02T15:04:05Z"),
		CreatedBy: org.CreatedBy.String(),
	}

	return c.JSON(http.StatusCreated, response)
}

// GetAPIKey returns the organization's API key (admin only)
func (h *OrganizationHandler) GetAPIKey(c echo.Context) error {
	// Get authenticated user
	authUser := c.Get("user").(*models.AuthUser)

	// Only admin can view API key
	if authUser.Role != "admin" {
		return echo.NewHTTPError(http.StatusForbidden, "Only administrators can view API keys")
	}

	// Get organization
	var org models.Organization
	if err := h.db.First(&org, "id = ?", authUser.OrganizationID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Organization not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch organization")
	}

	// Generate API key if it doesn't exist
	if org.APIKey == "" {
		if err := org.GenerateUniqueAPIKey(h.db); err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to generate unique API key: "+err.Error())
		}
		if err := h.db.Save(&org).Error; err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to save API key")
		}
	}

	// Generate form ID if it doesn't exist
	if org.FormID == "" {
		if err := org.GenerateUniqueFormID(h.db); err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to generate unique form ID: "+err.Error())
		}
		if err := h.db.Save(&org).Error; err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to save form ID")
		}
	}

	response := map[string]string{
		"api_key": org.APIKey,
		"form_id": org.FormID,
	}

	return c.JSON(http.StatusOK, response)
}

// RegenerateAPIKey generates a new API key for the organization (admin only)
func (h *OrganizationHandler) RegenerateAPIKey(c echo.Context) error {
	// Get authenticated user
	authUser := c.Get("user").(*models.AuthUser)

	// Only admin can regenerate API key
	if authUser.Role != "admin" {
		return echo.NewHTTPError(http.StatusForbidden, "Only administrators can regenerate API keys")
	}

	// Start database transaction for atomic operation
	tx := h.db.Begin()
	if tx.Error != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to start transaction")
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get organization within transaction
	var org models.Organization
	if err := tx.First(&org, "id = ?", authUser.OrganizationID).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Organization not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch organization")
	}

	// Store old API key for logging
	oldAPIKey := org.APIKey

	// Generate new unique API key
	if err := org.GenerateUniqueAPIKey(tx); err != nil {
		tx.Rollback()
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to generate unique API key: "+err.Error())
	}

	// Save to database within transaction
	if err := tx.Save(&org).Error; err != nil {
		tx.Rollback()
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to save new API key")
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to commit transaction")
	}

	// Log the API key regeneration (mask the keys for security)
	log.Printf("API key regenerated for organization %s (ID: %s). Old key: %s, New key: %s",
		org.Name, org.ID, maskAPIKey(oldAPIKey), maskAPIKey(org.APIKey))

	response := map[string]string{
		"api_key": org.APIKey,
		"message": "API key regenerated successfully",
	}

	return c.JSON(http.StatusOK, response)
}

// RegenerateFormID generates a new form ID for the organization (admin only)
func (h *OrganizationHandler) RegenerateFormID(c echo.Context) error {
	// Get authenticated user
	authUser := c.Get("user").(*models.AuthUser)

	// Only admin can regenerate form ID
	if authUser.Role != "admin" {
		return echo.NewHTTPError(http.StatusForbidden, "Only administrators can regenerate form IDs")
	}

	// Start database transaction for atomic operation
	tx := h.db.Begin()
	if tx.Error != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to start transaction")
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get organization within transaction
	var org models.Organization
	if err := tx.First(&org, "id = ?", authUser.OrganizationID).Error; err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return echo.NewHTTPError(http.StatusNotFound, "Organization not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch organization")
	}

	// Store old form ID for logging
	oldFormID := org.FormID

	// Generate new unique form ID
	if err := org.GenerateUniqueFormID(tx); err != nil {
		tx.Rollback()
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to generate unique form ID: "+err.Error())
	}

	// Save to database within transaction
	if err := tx.Save(&org).Error; err != nil {
		tx.Rollback()
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to save new form ID")
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to commit transaction")
	}

	// Log the form ID regeneration
	log.Printf("Form ID regenerated for organization %s (ID: %s). Old ID: %s, New ID: %s",
		org.Name, org.ID, oldFormID, org.FormID)

	response := map[string]string{
		"form_id": org.FormID,
		"message": "Form ID regenerated successfully",
	}

	return c.JSON(http.StatusOK, response)
}

// maskAPIKey masks the API key for logging (shows only first 8 and last 4 characters)
func maskAPIKey(apiKey string) string {
	if apiKey == "" {
		return "[empty]"
	}
	if len(apiKey) <= 12 {
		return "[masked]"
	}
	return apiKey[:8] + "..." + apiKey[len(apiKey)-4:]
}

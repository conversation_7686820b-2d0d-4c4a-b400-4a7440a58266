package handlers

import (
	"encoding/json"
	"enquiry-management-system/models"
	"enquiry-management-system/services"
	"enquiry-management-system/services/email"
	"enquiry-management-system/utils"
	"errors"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type PublicFormHandler struct {
	db           *gorm.DB
	emailService *email.Service
	spamFilter   *services.SpamFilterService
}

func NewPublicFormHandler(db *gorm.DB, emailService *email.Service) *PublicFormHandler {
	return &PublicFormHandler{
		db:           db,
		emailService: emailService,
		spamFilter:   services.NewSpamFilterService(db),
	}
}

// SubmitForm handles form submissions via form_id (like Formspree)
func (h *PublicFormHandler) SubmitForm(c echo.Context) error {
	formID := c.Param("form_id")
	log.Printf("[PUBLIC_FORM] Starting form submission for form_id: %s", formID)

	// Find organization by form_id
	var organization models.Organization
	if err := h.db.Where("form_id = ? AND status = ?", formID, "active").First(&organization).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Printf("[PUBLIC_FORM] ERROR: Form not found for form_id: %s", formID)
			return echo.NewHTTPError(http.StatusNotFound, map[string]string{
				"error": "Form not found",
			})
		}
		log.Printf("[PUBLIC_FORM] ERROR: Database error while finding form: %v", err)
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": "Internal server error",
		})
	}
	log.Printf("[PUBLIC_FORM] Processing form submission for organization: %s (ID: %s)", organization.Name, organization.ID)

	// Parse form data based on content type
	requestData := make(map[string]interface{}) // Initialize the map to prevent nil map panic
	contentType := c.Request().Header.Get("Content-Type")
	if strings.Contains(contentType, "application/x-www-form-urlencoded") {
		// Handle HTML form submission
		if err := c.Request().ParseForm(); err != nil {
			log.Printf("[PUBLIC_FORM] ERROR: Failed to parse form data: %v", err)
			return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
				"error": "Invalid form data",
			})
		}
		// Convert form values to map
		for key, values := range c.Request().Form {
			if len(values) > 0 {
				requestData[key] = values[0] // Take first value
			}
		}
	} else {
		// Handle JSON submission (AJAX)
		if err := c.Bind(&requestData); err != nil {
			log.Printf("[PUBLIC_FORM] ERROR: Failed to parse JSON body: %v", err)
			return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
				"error": "Invalid request body",
			})
		}
	}
	log.Printf("[PUBLIC_FORM] Received form data: %+v", requestData)

	// Check for honeypot spam protection
	if honeypot, exists := requestData["_honeypot"]; exists && honeypot != "" {
		log.Printf("[PUBLIC_FORM] Honeypot triggered, likely spam submission")
		// Return success to not reveal honeypot to spammers
		return c.JSON(http.StatusOK, map[string]interface{}{
			"success": true,
			"message": "Form submitted successfully",
		})
	}

	// Extract standard fields
	req := models.ExternalEnquiryRequest{}
	if name, ok := requestData["name"].(string); ok {
		req.Name = name
	}
	if email, ok := requestData["email"].(string); ok {
		req.Email = email
	}
	if phone, ok := requestData["phone"].(string); ok {
		req.Phone = phone
	}
	if source, ok := requestData["source"].(string); ok {
		req.Source = source
	}
	if formType, ok := requestData["form_type"].(string); ok {
		req.FormType = formType
	}
	if message, ok := requestData["message"].(string); ok {
		req.Description = message
	} else if description, ok := requestData["description"].(string); ok {
		req.Description = description
	}

	// Validate required fields
	if req.Name == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Name is required",
		})
	}
	if req.Email == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Email is required",
		})
	}
	if req.Description == "" {
		return echo.NewHTTPError(http.StatusBadRequest, map[string]string{
			"error": "Message is required",
		})
	}

	// Collect extra fields for metadata (exclude standard and special fields)
	standardFields := map[string]bool{
		"name": true, "email": true, "phone": true, "source": true, "form_type": true,
		"message": true, "description": true,
		"_honeypot": true, "_redirect": true,
	}
	metadata := make(map[string]interface{})
	for key, value := range requestData {
		if !standardFields[key] {
			metadata[key] = value
		}
	}

	// Convert metadata to JSON
	var metadataJSON []byte
	if len(metadata) > 0 {
		var err error
		metadataJSON, err = json.Marshal(metadata)
		if err != nil {
			log.Printf("[PUBLIC_FORM] ERROR: Failed to marshal metadata: %v", err)
			return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
				"error": "Failed to process form data",
			})
		}
	}

	// Generate AI summary
	var summary string
	if req.Name != "" || req.Description != "" {
		aiSummary, err := utils.GenerateEnquirySummary(req.Name, req.Description, metadata)
		if err != nil {
			// Log the error but don't fail the request
			log.Printf("[PUBLIC_FORM] Failed to generate AI summary for enquiry '%s': %v", req.Name, err)
			summary = "" // Continue without summary
		} else {
			summary = aiSummary
			log.Printf("[PUBLIC_FORM] Generated AI summary: %s", summary)
		}
	}

	// Generate unique slug for the enquiry
	slug := utils.GenerateSlug(req.Name)

	// Process source field - if it's a URL, extract domain, otherwise keep as is
	processedSource := utils.ProcessSourceField(req.Source)
	if processedSource == "" {
		processedSource = "Website Form" // Default fallback
	}

	// Create enquiry
	enquiry := models.Enquiry{
		OrganizationID: organization.ID,
		Slug:           slug,
		Name:           req.Name,
		Email:          req.Email,
		Phone:          req.Phone,
		FormType:       req.FormType,
		Status:         "New",
		Priority:       "Medium",
		Source:         processedSource, // Store processed source (domain if URL, otherwise original)
		Description:    req.Description,
		Summary:        summary,
		SubmittedOn:    time.Now(),
	}

	// Set metadata if present
	if len(metadataJSON) > 0 {
		enquiry.Metadata = metadataJSON
	}

	// Save to database
	if err := h.db.Create(&enquiry).Error; err != nil {
		log.Printf("[PUBLIC_FORM] ERROR: Failed to create enquiry: %v", err)
		return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
			"error": "Failed to create enquiry",
		})
	}

	log.Printf("[PUBLIC_FORM] Enquiry created successfully - ID: %s, Slug: %s, Summary: %s", enquiry.ID, enquiry.Slug, enquiry.Summary)

	log.Printf("[PUBLIC_FORM] Starting spam analysis for enquiry %s", enquiry.ID)

	// Perform spam analysis asynchronously (don't block enquiry creation)
	go func() {
		if err := h.spamFilter.AnalyzeEnquiry(&enquiry); err != nil {
			log.Printf("Failed to analyze spam for enquiry %s: %v", enquiry.ID, err)
		}
	}()

	// Create activity for external enquiry creation
	h.trackExternalEnquiryCreated(organization.ID, enquiry.ID, enquiry.Name)

	// Send notification emails to organization admins
	go h.sendNotificationToAdmins(organization, enquiry)

	// Handle redirect for HTML form submissions
	if redirectURL, exists := requestData["_redirect"]; exists {
		if redirectStr, ok := redirectURL.(string); ok && redirectStr != "" {
			// Validate redirect URL (basic security check)
			if parsedURL, err := url.Parse(redirectStr); err == nil && (parsedURL.Scheme == "http" || parsedURL.Scheme == "https") {
				log.Printf("[PUBLIC_FORM] Redirecting to: %s", redirectStr)
				return c.Redirect(http.StatusSeeOther, redirectStr)
			}
		}
	}

	// Return JSON response (for AJAX or when no redirect specified)
	response := models.ExternalEnquiryResponse{
		Success:   true,
		Message:   "Form submitted successfully",
		EnquiryID: enquiry.ID.String(),
		Slug:      enquiry.Slug,
	}
	return c.JSON(http.StatusCreated, response)
}

// sendNotificationToAdmins sends email notifications to all admins of the organization
func (h *PublicFormHandler) sendNotificationToAdmins(organization models.Organization, enquiry models.Enquiry) {
	if h.emailService == nil || !h.emailService.IsConfigured() {
		log.Printf("[PUBLIC_FORM] Email service not configured, skipping admin notifications")
		return
	}

	// Get all admin users for the organization
	var adminUsers []models.User
	err := h.db.Where("organization_id = ? AND role = ? AND status = ?",
		organization.ID, "admin", "active").Find(&adminUsers).Error

	if err != nil {
		log.Printf("[PUBLIC_FORM] ERROR: Failed to fetch admin users: %v", err)
		return
	}

	if len(adminUsers) == 0 {
		log.Printf("[PUBLIC_FORM] No active admin users found for organization %s", organization.Name)
		return
	}

	// Prepare email data
	emailData := email.EnquiryData{
		EnquiryID:     enquiry.ID.String(),
		Message:       enquiry.Summary,
		CustomerName:  enquiry.Name,
		CustomerEmail: enquiry.Email,
		OrgSlug:       organization.Slug,
		EnquirySlug:   enquiry.Slug,
	}

	// Send notification to each admin
	for _, admin := range adminUsers {
		go func(adminEmail string, adminName string) {
			log.Printf("[PUBLIC_FORM] Sending enquiry notification to admin: %s (%s)", adminName, adminEmail)

			if err := h.emailService.SendNotification(adminEmail, emailData); err != nil {
				log.Printf("[PUBLIC_FORM] ERROR: Failed to send notification to %s: %v", adminEmail, err)
			} else {
				log.Printf("[PUBLIC_FORM] Notification sent successfully to %s", adminEmail)
			}
		}(admin.Email, admin.Name)
	}

	log.Printf("[PUBLIC_FORM] Initiated notification emails to %d admin(s) for enquiry %s",
		len(adminUsers), enquiry.ID)
}

// trackExternalEnquiryCreated creates an activity record for external form submissions
func (h *PublicFormHandler) trackExternalEnquiryCreated(organizationID, enquiryID uuid.UUID, enquiryName string) {
	// Find the system user for this organization
	var systemUser models.User
	if err := h.db.Where("organization_id = ? AND role = ?", organizationID, "system").First(&systemUser).Error; err != nil {
		log.Printf("[PUBLIC_FORM] ERROR: Failed to find system user for organization %s: %v", organizationID, err)
		return // Don't fail the form submission if activity tracking fails
	}

	// Create activity for external enquiry creation using system user
	activityHandler := NewActivityHandler(h.db)
	if err := activityHandler.TrackEnquiryCreated(organizationID, enquiryID, systemUser.ID, enquiryName); err != nil {
		log.Printf("[PUBLIC_FORM] ERROR: Failed to create activity for enquiry %s: %v", enquiryID, err)
		// Don't fail the form submission if activity tracking fails
	}
}

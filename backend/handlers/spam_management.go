package handlers

import (
	"net/http"

	"enquiry-management-system/middleware"
	"enquiry-management-system/models"
	"enquiry-management-system/services"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type SpamManagementHandler struct {
	db         *gorm.DB
	spamFilter *services.SpamFilterService
}

func NewSpamManagementHandler(db *gorm.DB) *SpamManagementHandler {
	return &SpamManagementHandler{
		db:         db,
		spamFilter: services.NewSpamFilterService(db),
	}
}

// MarkAsSpam manually marks an enquiry as spam
func (h *SpamManagementHandler) MarkAsSpam(c echo.Context) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Only admin and manager can mark as spam
	if !user.IsAdmin() && !user.IsManager() {
		return echo.NewHTTPError(http.StatusForbidden, "Insufficient permissions")
	}

	idParam := c.Param("id")
	enquiryID, err := uuid.Parse(idParam)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid enquiry ID")
	}

	// Verify enquiry exists and belongs to user's organization
	db := middleware.GetOrganizationScopedDB(c, h.db)
	enquiry := models.Enquiry{}
	if err := db.Model(&enquiry).Where("id = ?", enquiryID).First(&enquiry).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to verify enquiry")
	}

	// Mark as spam
	if err := h.spamFilter.MarkAsSpam(enquiryID.String()); err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to mark enquiry as spam")
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "Enquiry marked as spam successfully",
		"id":      enquiryID,
	})
}

// MarkAsNotSpam manually marks an enquiry as not spam
func (h *SpamManagementHandler) MarkAsNotSpam(c echo.Context) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Only admin and manager can mark as not spam
	if !user.IsAdmin() && !user.IsManager() {
		return echo.NewHTTPError(http.StatusForbidden, "Insufficient permissions")
	}

	idParam := c.Param("id")
	enquiryID, err := uuid.Parse(idParam)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid enquiry ID")
	}

	// Verify enquiry exists and belongs to user's organization
	db := middleware.GetOrganizationScopedDB(c, h.db)
	enquiry := models.Enquiry{}
	if err := db.Model(&enquiry).Where("id = ?", enquiryID).First(&enquiry).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Enquiry not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to verify enquiry")
	}

	// Mark as not spam
	if err := h.spamFilter.MarkAsNotSpam(enquiryID.String()); err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to mark enquiry as not spam")
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "Enquiry marked as not spam successfully",
		"id":      enquiryID,
	})
}

// GetSpamStats returns spam statistics for the organization
func (h *SpamManagementHandler) GetSpamStats(c echo.Context) error {
	// Get authenticated user
	user := middleware.GetAuthUser(c)
	if user == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Only admin and manager can view spam stats
	if !user.IsAdmin() && !user.IsManager() {
		return echo.NewHTTPError(http.StatusForbidden, "Insufficient permissions")
	}

	stats, err := h.spamFilter.GetSpamStats(user.OrganizationID.String())
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to get spam statistics")
	}

	return c.JSON(http.StatusOK, stats)
}

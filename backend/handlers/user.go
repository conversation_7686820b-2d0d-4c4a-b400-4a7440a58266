package handlers

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"enquiry-management-system/config"
	"enquiry-management-system/middleware"
	"enquiry-management-system/models"
	"enquiry-management-system/services/email"
	"enquiry-management-system/utils"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

type UserHandler struct {
	db           *gorm.DB
	cfg          *config.Config
	emailService *email.Service
}

func NewUserHandler(db *gorm.DB, cfg *config.Config, emailService *email.Service) *UserHandler {
	return &UserHandler{
		db:           db,
		cfg:          cfg,
		emailService: emailService,
	}
}

func (h *UserHandler) GetMe(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	var user models.User
	if err := h.db.First(&user, "id = ?", authUser.ID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "User not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch user details")
	}

	// Fetch organization separately
	var organization models.Organization
	if err := h.db.First(&organization, "id = ?", user.OrganizationID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Organization not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch organization details")
	}

	// Set organization in user for response
	user.Organization = organization

	return c.JSON(http.StatusOK, user)
}

func (h *UserHandler) GetAllUsers(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Use organization-scoped database
	db := middleware.GetOrganizationScopedDB(c, h.db)

	// Get all active users for assignment dropdown (exclude system users)
	var users []models.User
	if err := db.Where("status = ? AND role != ?", "active", "system").Order("name ASC").Find(&users).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch users")
	}

	// Convert to simple response format for assignment dropdown
	type UserResponse struct {
		ID   string `json:"id"`
		Name string `json:"name"`
		Role string `json:"role"`
	}

	response := make([]UserResponse, len(users))
	for i, user := range users {
		response[i] = UserResponse{
			ID:   user.ID.String(),
			Name: user.Name,
			Role: user.Role,
		}
	}

	return c.JSON(http.StatusOK, response)
}

func (h *UserHandler) GetProfile(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	var user models.User
	if err := h.db.First(&user, "id = ?", authUser.ID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "User not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch user profile")
	}

	return c.JSON(http.StatusOK, user)
}

func (h *UserHandler) UpdateProfile(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Check if user exists
	var user models.User
	if err := h.db.First(&user, "id = ?", authUser.ID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "User not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch user")
	}

	// Bind request
	var req models.UpdateProfileRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// Update only provided fields
	updates := make(map[string]interface{})
	if req.Phone != "" {
		updates["phone"] = req.Phone
	}
	if req.JobTitle != "" {
		updates["job_title"] = req.JobTitle
	}
	if req.Department != "" {
		updates["department"] = req.Department
	}
	if req.Timezone != "" {
		updates["timezone"] = req.Timezone
	}
	if req.Bio != "" {
		updates["bio"] = req.Bio
	}

	// Update user
	if len(updates) > 0 {
		if err := h.db.Model(&user).Updates(updates).Error; err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update profile")
		}
	}

	// Fetch updated user
	if err := h.db.First(&user, "id = ?", authUser.ID).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch updated profile")
	}

	return c.JSON(http.StatusOK, user)
}

func (h *UserHandler) GetTeamMembers(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Check if requesting user is admin or manager
	if authUser.Role != "admin" && authUser.Role != "manager" {
		return echo.NewHTTPError(http.StatusForbidden, "Only admins and managers can view team members")
	}

	// Parse filters
	var filters models.TeamMemberFilters
	if err := c.Bind(&filters); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid query parameters")
	}

	// Set defaults
	if filters.Page <= 0 {
		filters.Page = 1
	}
	if filters.Limit <= 0 {
		filters.Limit = 10
	}

	// Use organization-scoped database
	db := middleware.GetOrganizationScopedDB(c, h.db)

	// Build query (exclude system users from team management)
	query := db.Model(&models.User{}).Where("role != ?", "system")

	// Add filters
	if filters.Search != "" {
		searchTerm := "%" + filters.Search + "%"
		query = query.Where("name ILIKE ? OR email ILIKE ?", searchTerm, searchTerm)
	}

	if filters.Role != "" && filters.Role != "all" {
		query = query.Where("role = ?", filters.Role)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to count team members")
	}

	// Get paginated results
	var users []models.User
	offset := (filters.Page - 1) * filters.Limit
	if err := query.Offset(offset).Limit(filters.Limit).Order("name ASC").Find(&users).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch team members")
	}

	// Convert to response format
	members := make([]models.TeamMemberResponse, len(users))
	for i, user := range users {
		members[i] = models.TeamMemberResponse{
			ID:         user.ID.String(),
			Name:       user.Name,
			Email:      user.Email,
			Role:       user.Role,
			Status:     user.Status,
			LastLogin:  formatLastLogin(user.LastLogin),
			JoinedDate: user.JoinedDate.Format("2006-01-02"),
			Avatar:     generateAvatar(user.Name),
		}
	}

	response := models.TeamMembersListResponse{
		Members: members,
		Total:   total,
		Page:    filters.Page,
		Limit:   filters.Limit,
	}

	return c.JSON(http.StatusOK, response)
}

func (h *UserHandler) CreateTeamMember(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Check if requesting user is admin
	if authUser.Role != "admin" {
		return echo.NewHTTPError(http.StatusForbidden, "Only admins can add team members")
	}

	// Bind request
	var req models.CreateTeamMemberRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// Validate required fields
	if req.Name == "" || req.Email == "" || req.Role == "" {
		return echo.NewHTTPError(http.StatusBadRequest, "Name, email, and role are required")
	}

	// Validate role (exclude system role from user management)
	if !isValidManageableRole(req.Role) {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid role. Must be one of: admin, manager, agent")
	}

	// Use organization-scoped database
	db := middleware.GetOrganizationScopedDB(c, h.db)

	// restrictions: Only 2 members allowed per organization (exclude system users)
	var totalMembers int64
	if err := db.Model(&models.User{}).
		Where("organization_id = ? AND role != ?", authUser.OrganizationID, "system").
		Count(&totalMembers).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to check member count")
	}

	if totalMembers >= 2 {
		return echo.NewHTTPError(http.StatusBadRequest, "limit: Maximum 2 members allowed per organization")
	}

	// restrictions: Only one admin allowed (system users don't count)
	if req.Role == "admin" {
		var adminCount int64
		if err := db.Model(&models.User{}).
			Where("organization_id = ? AND role = ?", authUser.OrganizationID, "admin").
			Count(&adminCount).Error; err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to check admin count")
		}

		if adminCount >= 1 {
			return echo.NewHTTPError(http.StatusBadRequest, "limit: Only one admin allowed per organization")
		}
	}

	// Check if email already exists
	var existingUser models.User
	var cnt int64
	if err := db.Model(&existingUser).
		Where("email = ?", req.Email).
		Count(&cnt).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to check for existing user")
	}

	if cnt > 0 {
		return echo.NewHTTPError(http.StatusConflict, "User with this email already exists")
	}

	// Check if there's already a pending invitation for this email
	var existingInvitation models.Invitation
	if err := db.Model(&existingInvitation).
		Where("email = ? AND status = ? AND organization_id = ?", req.Email, "pending", authUser.OrganizationID).
		Count(&cnt).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to check for existing invitation")
	}

	if cnt > 0 {
		return echo.NewHTTPError(http.StatusConflict, "There is already a pending invitation for this email")
	}

	// Start transaction using the original database connection
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create new user with pending status
	user := models.User{
		OrganizationID: authUser.OrganizationID,
		Name:           "", // Will be set when invitation is accepted
		Email:          req.Email,
		Role:           req.Role,
		Status:         "pending",
		JoinedDate:     time.Now(),
	}

	if err := tx.Create(&user).Error; err != nil {
		fmt.Println("Error creating user:", err)
		tx.Rollback()
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create team member")
	}

	// Generate invitation token
	token, err := utils.GenerateInvitationToken()
	if err != nil {
		tx.Rollback()
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to generate invitation token")
	}

	// Create invitation record
	invitation := models.Invitation{
		UserID:         user.ID,
		OrganizationID: authUser.OrganizationID,
		Token:          token,
		Email:          req.Email,
		Role:           req.Role,
		Status:         "pending",
		ExpiresAt:      time.Now().Add(7 * 24 * time.Hour), // 7 days expiry
		InvitedBy:      authUser.ID,
	}

	if err := tx.Create(&invitation).Error; err != nil {
		tx.Rollback()
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to create invitation")
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to complete team member creation")
	}

	// Log invitation URL for testing
	invitationURL := fmt.Sprintf("%s/invite/accept?token=%s", h.cfg.App.BaseURL, token)
	fmt.Printf("\n🔗 INVITATION URL FOR TESTING:\n%s\n\n", invitationURL)

	// Send invitation email (async, don't fail if email fails)
	go h.sendInvitationEmail(req.Email, req.Name, authUser.Name, authUser.OrganizationID, req.Role, token)

	// Return created user
	response := models.TeamMemberResponse{
		ID:         user.ID.String(),
		Name:       req.Name, // Return the provided name for UI purposes
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		LastLogin:  formatLastLogin(user.LastLogin),
		JoinedDate: user.JoinedDate.Format("2006-01-02"),
		Avatar:     generateAvatar(req.Name),
	}

	return c.JSON(http.StatusCreated, response)
}

func (h *UserHandler) UpdateTeamMember(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Check if requesting user is admin
	if authUser.Role != "admin" {
		return echo.NewHTTPError(http.StatusForbidden, "Only admins can update team members")
	}

	// Get member ID from URL
	memberID := c.Param("id")
	memberUUID, err := uuid.Parse(memberID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid member ID format")
	}

	// Use organization-scoped database
	db := middleware.GetOrganizationScopedDB(c, h.db)

	// Check if member exists and is not a system user
	var member models.User
	if err := db.Where("id = ? AND role != ?", memberUUID, "system").First(&member).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Team member not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch team member")
	}

	// Bind request
	var req models.UpdateTeamMemberRequest
	if err := c.Bind(&req); err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid request body")
	}

	// Update only provided fields
	updates := make(map[string]interface{})
	if req.Role != "" {
		if !isValidManageableRole(req.Role) {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid role. Must be one of: admin, manager, agent")
		}
		updates["role"] = req.Role
	}
	if req.Status != "" {
		if !isValidUserStatus(req.Status) {
			return echo.NewHTTPError(http.StatusBadRequest, "Invalid status. Must be one of: active, inactive")
		}
		updates["status"] = req.Status
	}

	// Update member
	if len(updates) > 0 {
		if err := db.Model(&member).Updates(updates).Error; err != nil {
			return echo.NewHTTPError(http.StatusInternalServerError, "Failed to update team member")
		}
	}

	// Fetch updated member
	if err := db.First(&member, "id = ?", memberUUID).Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch updated team member")
	}

	// Return updated member
	response := models.TeamMemberResponse{
		ID:         member.ID.String(),
		Name:       member.Name,
		Email:      member.Email,
		Role:       member.Role,
		Status:     member.Status,
		LastLogin:  formatLastLogin(member.LastLogin),
		JoinedDate: member.JoinedDate.Format("2006-01-02"),
		Avatar:     generateAvatar(member.Name),
	}

	return c.JSON(http.StatusOK, response)
}

func (h *UserHandler) DeleteTeamMember(c echo.Context) error {
	// Get authenticated user from JWT token
	authUser := middleware.GetAuthUser(c)
	if authUser == nil {
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Check if requesting user is admin
	if authUser.Role != "admin" {
		return echo.NewHTTPError(http.StatusForbidden, "Only admins can delete team members")
	}

	// Get member ID from URL
	memberID := c.Param("id")
	memberUUID, err := uuid.Parse(memberID)
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "Invalid member ID format")
	}

	// Use organization-scoped database
	db := middleware.GetOrganizationScopedDB(c, h.db)

	// Check if member exists and is not a system user
	var member models.User
	if err := db.Where("id = ? AND role != ?", memberUUID, "system").First(&member).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return echo.NewHTTPError(http.StatusNotFound, "Team member not found")
		}
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to fetch team member")
	}

	// Soft delete (set status to inactive)
	if err := db.Model(&member).Update("status", "inactive").Error; err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "Failed to delete team member")
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Team member deleted successfully"})
}

// Helper functions
func formatLastLogin(lastLogin *time.Time) string {
	if lastLogin == nil {
		return "Never"
	}
	return lastLogin.Format("2006-01-02 15:04 PM")
}

func generateAvatar(name string) string {
	if name == "" {
		return "??"
	}
	parts := strings.Fields(name)
	if len(parts) == 1 {
		return strings.ToUpper(name[:2])
	}
	return strings.ToUpper(string(parts[0][0]) + string(parts[1][0]))
}

func isValidRole(role string) bool {
	for _, validRole := range models.UserRoles {
		if role == validRole {
			return true
		}
	}
	return false
}

// isValidManageableRole checks if role is valid for user management (excludes system)
func isValidManageableRole(role string) bool {
	manageableRoles := []string{"admin", "manager", "agent"}
	for _, validRole := range manageableRoles {
		if role == validRole {
			return true
		}
	}
	return false
}

func isValidUserStatus(status string) bool {
	for _, validStatus := range models.UserStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// sendInvitationEmail sends an invitation email to the new team member
func (h *UserHandler) sendInvitationEmail(email_address, inviteeName, inviterName string, orgID uuid.UUID, role, token string) {
	fmt.Printf("📧 Preparing to send invitation email to: %s\n", email_address)

	if h.emailService == nil {
		fmt.Printf("Email service not configured, skipping invitation email for %s\n", email_address)
		fmt.Printf("Use the invitation URL above to test the invitation flow\n")
		return
	}

	// Get organization details
	var org models.Organization
	if err := h.db.First(&org, "id = ?", orgID).Error; err != nil {
		fmt.Printf("Failed to fetch organization details for invitation email: %v\n", err)
		return
	}

	// Prepare email data
	emailData := email.InvitationData{
		InviteeName:      inviteeName,
		InviterName:      inviterName,
		OrganizationName: org.Name,
		Role:             role,
		InviteToken:      token,
		ExpiresIn:        "7 days",
	}

	// Send email
	if err := h.emailService.SendTeamInvitation(email_address, emailData); err != nil {
		fmt.Printf("Failed to send invitation email to %s: %v\n", email_address, err)
		fmt.Printf("Use the invitation URL above to test the invitation flow\n")
	} else {
		fmt.Printf("Invitation email sent successfully to %s\n", email_address)
		fmt.Printf("You can also use the invitation URL above for direct testing\n")
	}
}

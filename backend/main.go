package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"enquiry-management-system/config"
	"enquiry-management-system/database"
	"enquiry-management-system/routes"
	"enquiry-management-system/services/email"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Initialize database with connection pooling
	dbConn, err := database.NewConnection(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer dbConn.Close() // Ensure connections are closed on shutdown

	// Get the underlying GORM DB for handlers
	db := dbConn.DB

	// Initialize email service
	emailService := email.NewService(cfg)

	// Initialize Echo
	e := echo.New()

	// Middleware
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())

	// Configure CORS
	e.Use(middleware.CORSWithConfig(middleware.CORSConfig{
		AllowOrigins: []string{"*"},
		AllowHeaders: []string{"*"},
	}))

	// Health check endpoint
	e.GET("/health", func(c echo.Context) error {
		log.Println("Health check endpoint called")
		return c.JSON(http.StatusOK, map[string]string{
			"status":  "ok",
			"service": "enquiry-management-api",
		})
	})

	// Root endpoint for basic connectivity test
	e.GET("/", func(c echo.Context) error {
		return c.JSON(http.StatusOK, map[string]string{
			"message": "Enquiry Management API is running",
			"status":  "ok",
		})
	})

	// Store database in context for handlers
	e.Use(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			c.Set("db", db)
			return next(c)
		}
	})

	// Setup routes with authentication
	routes.SetupEnquiryRoutes(e, db, cfg)
	routes.SetupDashboardRoutes(e, db, cfg)
	routes.SetupUserRoutes(e, db, cfg, emailService)
	routes.SetupOrganizationRoutes(e, db, cfg)
	routes.SetupOnboardRoutes(e, db, cfg)
	routes.SetupInvitationRoutes(e, db, cfg, emailService)
	routes.SetupSpamRoutes(e, db, cfg)

	// Setup public routes (no JWT authentication required)
	routes.SetupPublicRoutes(e, db, emailService)

	// Start server with graceful shutdown
	log.Printf("Server starting on 0.0.0.0:%s", cfg.Port)

	// Start server in a goroutine
	go func() {
		if err := e.Start("0.0.0.0:" + cfg.Port); err != nil && err != http.ErrServerClosed {
			log.Fatal("Failed to start server:", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// Give outstanding requests a deadline for completion
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := e.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("Server exited")
}

package middleware

import (
	"enquiry-management-system/models"
	"errors"
	"log"
	"net/http"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// APIKeyAuthMiddleware handles API key authentication for public endpoints
func APIKeyAuthMiddleware(db *gorm.DB) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			log.Printf("[API_KEY_AUTH] Starting API key authentication for %s %s", c.Request().Method, c.Request().URL.Path)

			// Extract API key from X-API-Key header
			apiKey := c.Request().Header.Get("X-API-Key")
			log.Printf("[API_KEY_AUTH] API key header: %s", maskAPIKey(apiKey))

			if apiKey == "" {
				log.Printf("[API_KEY_AUTH] ERROR: Missing X-API-Key header")
				return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
					"error": "API key is required",
				})
			}

			// Find organization by API key
			log.Printf("[API_KEY_AUTH] Looking up organization by API key...")
			var organization models.Organization
			if err := db.Where("api_key = ? AND status = ?", apiKey, "active").First(&organization).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					log.Printf("[API_KEY_AUTH] ERROR: Invalid API key or inactive organization")
					return echo.NewHTTPError(http.StatusUnauthorized, map[string]string{
						"error": "Invalid API key",
					})
				}
				log.Printf("[API_KEY_AUTH] ERROR: Database error while validating API key: %v", err)
				return echo.NewHTTPError(http.StatusInternalServerError, map[string]string{
					"error": "Internal server error",
				})
			}

			log.Printf("[API_KEY_AUTH] Organization found - ID: %s, Name: %s, Status: %s",
				organization.ID, organization.Name, organization.Status)

			// Store organization in context for use in handlers
			c.Set("organization", &organization)
			c.Set("organization_id", organization.ID)

			log.Printf("[API_KEY_AUTH] API key authentication successful for organization: %s", organization.Name)

			return next(c)
		}
	}
}

// GetAPIKeyOrganization extracts the organization from context (set by API key auth middleware)
func GetAPIKeyOrganization(c echo.Context) *models.Organization {
	if org := c.Get("organization"); org != nil {
		return org.(*models.Organization)
	}
	return nil
}

// maskAPIKey masks the API key for logging (shows only first 8 and last 4 characters)
func maskAPIKey(apiKey string) string {
	if apiKey == "" {
		return "[empty]"
	}
	if len(apiKey) <= 12 {
		return "[masked]"
	}
	return apiKey[:8] + "..." + apiKey[len(apiKey)-4:]
}

package middleware

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	"strings"

	"enquiry-management-system/config"
	"enquiry-management-system/models"

	"github.com/golang-jwt/jwt"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// AuthMiddleware handles JWT authentication
func AuthMiddleware(cfg *config.Config, db *gorm.DB) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			log.Printf("[AUTH] Starting authentication for %s %s", c.Request().Method, c.Request().URL.Path)

			// Extract token from Authorization header
			authHeader := c.Request().Header.Get("Authorization")
			log.Printf("[AUTH] Authorization header: %s", maskToken(authHeader))

			if authHeader == "" {
				log.Printf("[AUTH] ERROR: Missing Authorization header")
				return echo.NewHTTPError(http.StatusUnauthorized, models.ErrMissingToken)
			}

			// Check Bearer prefix
			if !strings.HasPrefix(authHeader, "Bearer ") {
				log.Printf("[AUTH] ERROR: Authorization header doesn't start with 'Bearer '")
				return echo.NewHTTPError(http.StatusUnauthorized, models.ErrInvalidToken)
			}

			// Extract token
			tokenString := strings.TrimPrefix(authHeader, "Bearer ")
			if tokenString == "" {
				log.Printf("[AUTH] ERROR: Empty token after Bearer prefix")
				return echo.NewHTTPError(http.StatusUnauthorized, models.ErrMissingToken)
			}

			log.Printf("[AUTH] Token extracted successfully (length: %d)", len(tokenString))

			// Log JWT configuration being used
			log.Printf("[AUTH] JWT Config - Algorithm: %s, Issuer: %s, Audience: %s",
				cfg.JWT.Algorithm, cfg.JWT.Issuer, cfg.JWT.Audience)

			// Parse and validate token
			log.Printf("[AUTH] Starting token parsing...")
			token, err := jwt.ParseWithClaims(tokenString, &models.JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
				log.Printf("[AUTH] Token header: %+v", token.Header)

				// Validate signing method
				tokenAlg := token.Method.Alg()
				log.Printf("[AUTH] Token algorithm: %s, Expected: %s", tokenAlg, cfg.JWT.Algorithm)

				if tokenAlg != cfg.JWT.Algorithm {
					log.Printf("[AUTH] ERROR: Algorithm mismatch - token: %s, expected: %s", tokenAlg, cfg.JWT.Algorithm)
					return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
				}

				log.Printf("[AUTH] Algorithm validation passed")
				return []byte(cfg.JWT.Secret), nil
			})

			if err != nil {
				log.Printf("[AUTH] ERROR: Token parsing failed: %v", err)
				if ve, ok := err.(*jwt.ValidationError); ok {
					log.Printf("[AUTH] ValidationError details - Errors: %d", ve.Errors)
					if ve.Errors&jwt.ValidationErrorExpired != 0 {
						log.Printf("[AUTH] ERROR: Token expired")
						return echo.NewHTTPError(http.StatusUnauthorized, models.ErrExpiredToken)
					}
					if ve.Errors&jwt.ValidationErrorSignatureInvalid != 0 {
						log.Printf("[AUTH] ERROR: Invalid signature")
						return echo.NewHTTPError(http.StatusUnauthorized, models.ErrInvalidSignature)
					}
					if ve.Errors&jwt.ValidationErrorMalformed != 0 {
						log.Printf("[AUTH] ERROR: Malformed token")
						return echo.NewHTTPError(http.StatusUnauthorized, models.ErrInvalidToken)
					}
				}
				log.Printf("[AUTH] ERROR: Generic token validation error")
				return echo.NewHTTPError(http.StatusUnauthorized, models.ErrInvalidToken)
			}

			log.Printf("[AUTH] Token parsing successful")

			// Extract claims
			log.Printf("[AUTH] Extracting claims...")
			claims, ok := token.Claims.(*models.JWTClaims)
			if !ok {
				log.Printf("[AUTH] ERROR: Failed to cast claims to JWTClaims")
				return echo.NewHTTPError(http.StatusUnauthorized, models.ErrInvalidToken)
			}
			if !token.Valid {
				log.Printf("[AUTH] ERROR: Token is not valid")
				return echo.NewHTTPError(http.StatusUnauthorized, models.ErrInvalidToken)
			}

			log.Printf("[AUTH] Claims extracted - UserID: %s, Email: %s, Role: %s, Issuer: %s, Audience: %s",
				claims.UserID, claims.Email, claims.Role, claims.StandardClaims.Issuer, claims.StandardClaims.Audience)

			// Validate issuer if configured
			if cfg.JWT.Issuer != "" && claims.StandardClaims.Issuer != cfg.JWT.Issuer {
				log.Printf("[AUTH] ERROR: Issuer mismatch - token: %s, expected: %s", claims.StandardClaims.Issuer, cfg.JWT.Issuer)
				return echo.NewHTTPError(http.StatusUnauthorized, models.ErrInvalidIssuer)
			}

			// Validate audience if configured
			if cfg.JWT.Audience != "" && claims.StandardClaims.Audience != cfg.JWT.Audience {
				log.Printf("[AUTH] ERROR: Audience mismatch - token: %s, expected: %s", claims.StandardClaims.Audience, cfg.JWT.Audience)
				return echo.NewHTTPError(http.StatusUnauthorized, models.ErrInvalidAudience)
			}

			// Parse auth ID (Supabase user ID)
			log.Printf("[AUTH] Parsing auth ID: %s", claims.UserID)
			authID, err := uuid.Parse(claims.UserID)
			if err != nil {
				log.Printf("[AUTH] ERROR: Invalid auth ID format: %s, error: %v", claims.UserID, err)
				return echo.NewHTTPError(http.StatusUnauthorized, models.ErrInvalidToken)
			}

			// Verify user exists in database by auth_id and is active
			log.Printf("[AUTH] Checking user in database - AuthID: %s", authID)
			var user models.User
			if err := db.First(&user, "auth_id = ? AND status = ?", authID, "active").Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					log.Printf("[AUTH] ERROR: User not found or inactive - AuthID: %s", authID)
					return echo.NewHTTPError(http.StatusNotFound, models.ErrUserNotFound)
				}
				log.Printf("[AUTH] ERROR: Database error while fetching user: %v", err)
				return echo.NewHTTPError(http.StatusInternalServerError, "Database error")
			}

			// Fetch organization separately
			log.Printf("[AUTH] Fetching organization - OrgID: %s", user.OrganizationID)
			var organization models.Organization
			if err := db.First(&organization, "id = ?", user.OrganizationID).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					log.Printf("[AUTH] ERROR: Organization not found - OrgID: %s", user.OrganizationID)
					return echo.NewHTTPError(http.StatusNotFound, "Organization not found")
				}
				log.Printf("[AUTH] ERROR: Database error while fetching organization: %v", err)
				return echo.NewHTTPError(http.StatusInternalServerError, "Database error")
			}

			// Check if organization is active
			if organization.Status != "active" {
				log.Printf("[AUTH] ERROR: User's organization is not active - OrgID: %s, Status: %s",
					user.OrganizationID, organization.Status)
				return echo.NewHTTPError(http.StatusUnauthorized, "Organization is not active")
			}

			log.Printf("[AUTH] User found in database - ID: %s, Email: %s, Role: %s, Status: %s, OrgID: %s, OrgSlug: %s",
				user.ID, user.Email, user.Role, user.Status, user.OrganizationID, organization.Slug)

			// Create auth user context with organization information
			authUser := &models.AuthUser{
				ID:             user.ID,
				Email:          user.Email,
				Role:           user.Role,
				Name:           user.Name,
				OrganizationID: user.OrganizationID,
				OrgSlug:        organization.Slug,
				OrgName:        organization.Name,
			}

			// Store user in context
			c.Set("user", authUser)
			log.Printf("[AUTH] Authentication successful for user: %s (%s)", user.Name, user.Role)

			return next(c)
		}
	}
}

// RequireAuth is a convenience function for basic authentication
func RequireAuth(cfg *config.Config, db *gorm.DB) echo.MiddlewareFunc {
	return AuthMiddleware(cfg, db)
}

// RequireRole creates middleware that requires specific roles
func RequireRole(cfg *config.Config, db *gorm.DB, roles ...string) echo.MiddlewareFunc {
	return echo.MiddlewareFunc(func(next echo.HandlerFunc) echo.HandlerFunc {
		return AuthMiddleware(cfg, db)(func(c echo.Context) error {
			user := c.Get("user").(*models.AuthUser)
			if !user.HasAnyRole(roles...) {
				return echo.NewHTTPError(http.StatusForbidden, models.ErrInsufficientRole)
			}
			return next(c)
		})
	})
}

// RequireAdmin creates middleware that requires admin role
func RequireAdmin(cfg *config.Config, db *gorm.DB) echo.MiddlewareFunc {
	return RequireRole(cfg, db, "admin")
}

// RequireAdminOrManager creates middleware that requires admin or manager role
func RequireAdminOrManager(cfg *config.Config, db *gorm.DB) echo.MiddlewareFunc {
	return RequireRole(cfg, db, "admin", "manager")
}

// RequireManager creates middleware that requires manager role
func RequireManager(cfg *config.Config, db *gorm.DB) echo.MiddlewareFunc {
	return RequireRole(cfg, db, "manager")
}

// RequireAgent creates middleware that requires agent role
func RequireAgent(cfg *config.Config, db *gorm.DB) echo.MiddlewareFunc {
	return RequireRole(cfg, db, "agent")
}

// GetAuthUser extracts the authenticated user from context
func GetAuthUser(c echo.Context) *models.AuthUser {
	if user := c.Get("user"); user != nil {
		return user.(*models.AuthUser)
	}
	return nil
}

// Helper function to mask tokens in logs for security
func maskToken(authHeader string) string {
	if authHeader == "" {
		return "<empty>"
	}
	if len(authHeader) < 20 {
		return "<too_short>"
	}
	if strings.HasPrefix(authHeader, "Bearer ") {
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if len(token) < 10 {
			return "Bearer <short_token>"
		}
		return fmt.Sprintf("Bearer %s...%s", token[:6], token[len(token)-4:])
	}
	return "<invalid_format>"
}

package middleware

import (
	"enquiry-management-system/models"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// OrganizationScope returns a GORM scope that filters by organization ID
func OrganizationScope(authUser *models.AuthUser) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("organization_id = ?", authUser.OrganizationID)
	}
}

// GetOrganizationScopedDB returns a GORM DB instance scoped to the user's organization
func GetOrganizationScopedDB(c echo.Context, db *gorm.DB) *gorm.DB {
	authUser := c.Get("user").(*models.AuthUser)
	return db.Scopes(OrganizationScope(authUser))
}

// OrganizationMiddleware adds organization context to the request
// This middleware should be used after authentication middleware
func OrganizationMiddleware() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get authenticated user from context (set by auth middleware)
			authUser, ok := c.Get("user").(*models.AuthUser)
			if !ok {
				// This should not happen if auth middleware is properly configured
				return echo.NewHTTPError(401, "Authentication required")
			}

			// Add organization context to the request
			c.Set("organization_id", authUser.OrganizationID)
			c.Set("org_slug", authUser.OrgSlug)
			c.Set("org_name", authUser.OrgName)

			return next(c)
		}
	}
}

// RequireOrganizationFeature middleware checks if the organization has access to a specific feature
func RequireOrganizationFeature(feature string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			authUser := c.Get("user").(*models.AuthUser)

			// Get organization from database to check features
			// Note: In a production system, you might want to cache this
			var org models.Organization
			db := c.Get("db").(*gorm.DB) // Assuming DB is set in context
			if err := db.First(&org, "id = ?", authUser.OrganizationID).Error; err != nil {
				return echo.NewHTTPError(500, "Failed to check organization features")
			}

			if !org.HasFeature(feature) {
				return echo.NewHTTPError(403, "Organization plan does not include this feature")
			}

			return next(c)
		}
	}
}

// RequireOrganizationRole middleware checks if the user has a specific role within their organization
func RequireOrganizationRole(roles ...string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			authUser := c.Get("user").(*models.AuthUser)

			// Check if user has any of the required roles
			hasRole := false
			for _, role := range roles {
				if authUser.Role == role {
					hasRole = true
					break
				}
			}

			if !hasRole {
				return echo.NewHTTPError(403, "Insufficient permissions within organization")
			}

			return next(c)
		}
	}
}

// Helper functions for common organization-scoped queries

// GetOrganizationUsers returns all users in the same organization
func GetOrganizationUsers(c echo.Context, db *gorm.DB) ([]models.User, error) {
	var users []models.User
	err := GetOrganizationScopedDB(c, db).Find(&users).Error
	return users, err
}

// GetOrganizationEnquiries returns all enquiries in the same organization
func GetOrganizationEnquiries(c echo.Context, db *gorm.DB) ([]models.Enquiry, error) {
	var enquiries []models.Enquiry
	err := GetOrganizationScopedDB(c, db).Find(&enquiries).Error
	return enquiries, err
}

// GetOrganizationActivities returns all activities in the same organization
func GetOrganizationActivities(c echo.Context, db *gorm.DB) ([]models.Activity, error) {
	var activities []models.Activity
	err := GetOrganizationScopedDB(c, db).Find(&activities).Error
	return activities, err
}

// ValidateOrganizationAccess checks if a resource belongs to the user's organization
func ValidateOrganizationAccess(c echo.Context, db *gorm.DB, tableName string, resourceID string) error {
	authUser := c.Get("user").(*models.AuthUser)

	var count int64
	err := db.Table(tableName).
		Where("id = ? AND organization_id = ?", resourceID, authUser.OrganizationID).
		Count(&count).Error

	if err != nil {
		return err
	}

	if count == 0 {
		return echo.NewHTTPError(404, "Resource not found or access denied")
	}

	return nil
}

// ValidateOrganizationSlug validates that the org slug in the request matches the user's organization
func ValidateOrganizationSlug(c echo.Context) error {
	authUser := c.Get("user").(*models.AuthUser)

	// Get org slug from URL path parameter (if present)
	orgSlug := c.Param("orgSlug")
	if orgSlug == "" {
		// Try to get from query parameter
		orgSlug = c.QueryParam("org_slug")
	}

	// If no org slug in request, that's fine - we'll use user's org
	if orgSlug == "" {
		return nil
	}

	// Validate that the provided org slug matches user's organization
	if orgSlug != authUser.OrgSlug {
		return echo.NewHTTPError(403, "Access denied: organization mismatch")
	}

	return nil
}

// ValidateOrganizationID validates that the org ID in the request matches the user's organization
func ValidateOrganizationID(c echo.Context) error {
	authUser := c.Get("user").(*models.AuthUser)

	// Get org ID from query parameter
	orgID := c.QueryParam("org_id")

	// If no org ID in request, that's an error - we require explicit org context
	if orgID == "" {
		return echo.NewHTTPError(400, "Missing organization context: org_id parameter required")
	}

	// Validate that the provided org ID matches user's organization
	if orgID != authUser.OrganizationID.String() {
		return echo.NewHTTPError(403, "Access denied: you don't belong to the requested organization")
	}

	return nil
}

// OrganizationValidationMiddleware validates organization access for routes with org slug
func OrganizationValidationMiddleware() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Validate organization slug if present in URL
			if err := ValidateOrganizationSlug(c); err != nil {
				return err
			}

			// Validate organization ID if present in query params (required for API security)
			if err := ValidateOrganizationID(c); err != nil {
				return err
			}

			return next(c)
		}
	}
}

// ValidateRequestOrganization validates organization context from request headers/body
func ValidateRequestOrganization(c echo.Context, requestOrgID string) error {
	authUser := c.Get("user").(*models.AuthUser)

	// If no org ID provided in request, use user's org (secure default)
	if requestOrgID == "" {
		return nil
	}

	// Validate that the provided org ID matches user's organization
	if requestOrgID != authUser.OrganizationID.String() {
		return echo.NewHTTPError(403, "Access denied: organization ID mismatch")
	}

	return nil
}

// RequireOrganizationValidation middleware that validates organization context
// This should be used on routes that accept organization parameters
func RequireOrganizationValidation() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Check for org_id in query parameters
			if orgID := c.QueryParam("org_id"); orgID != "" {
				if err := ValidateRequestOrganization(c, orgID); err != nil {
					return err
				}
			}

			// Check for organization_id in request body (for POST/PUT requests)
			if c.Request().Method == "POST" || c.Request().Method == "PUT" {
				// We'll validate this in individual handlers since body parsing varies
			}

			return next(c)
		}
	}
}

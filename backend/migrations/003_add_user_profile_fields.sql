-- Migration: Add user profile fields
-- Description: Adds phone, job_title, department, timezone, and bio fields to users table
-- Date: 2024

-- Add new profile fields to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS phone VARCHAR(50),
ADD COLUMN IF NOT EXISTS job_title VARCHAR(255),
ADD COLUMN IF NOT EXISTS department VARCHAR(255),
ADD COLUMN IF NOT EXISTS timezone VARCHAR(100),
ADD COLUMN IF NOT EXISTS bio TEXT;

-- Create indexes for commonly searched fields
CREATE INDEX IF NOT EXISTS idx_users_department ON users(department);
CREATE INDEX IF NOT EXISTS idx_users_job_title ON users(job_title);

-- Migration: Add organizations table and update existing tables
-- Description: Creates organizations table and adds organization_id to existing tables for multi-tenancy
-- Date: 2024

-- Create organizations table
CREATE TABLE IF NOT EXISTS organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    domain VARCHAR(255) UNIQUE,
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    plan VARCHAR(50) NOT NULL DEFAULT 'starter',
    settings JSONB,
    created_by UUID NOT NULL,
    max_users INTEGER DEFAULT 10,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create a default organization for existing data
INSERT INTO organizations (id, name, slug, status, plan, created_by, max_users)
VALUES (
    gen_random_uuid(),
    'Default Organization',
    'default',
    'active',
    'enterprise',
    (SELECT id FROM users WHERE role = 'admin' LIMIT 1),
    1000
) ON CONFLICT (slug) DO NOTHING;

-- Add organization_id to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS organization_id UUID;

-- Update existing users to belong to the default organization
UPDATE users 
SET organization_id = (SELECT id FROM organizations WHERE slug = 'default')
WHERE organization_id IS NULL;

-- Make organization_id NOT NULL after updating existing data
ALTER TABLE users 
ALTER COLUMN organization_id SET NOT NULL;

-- Add organization_id to enquiries table
ALTER TABLE enquiries 
ADD COLUMN IF NOT EXISTS organization_id UUID;

-- Update existing enquiries to belong to the default organization
UPDATE enquiries 
SET organization_id = (SELECT id FROM organizations WHERE slug = 'default')
WHERE organization_id IS NULL;

-- Make organization_id NOT NULL after updating existing data
ALTER TABLE enquiries 
ALTER COLUMN organization_id SET NOT NULL;

-- Add organization_id to activities table (if it exists)
ALTER TABLE activities 
ADD COLUMN IF NOT EXISTS organization_id UUID;

-- Update existing activities to belong to the default organization
UPDATE activities 
SET organization_id = (SELECT id FROM organizations WHERE slug = 'default')
WHERE organization_id IS NULL;

-- Make organization_id NOT NULL after updating existing data
ALTER TABLE activities 
ALTER COLUMN organization_id SET NOT NULL;

-- Create indexes for better performance (optional for now, but good practice)
-- CREATE INDEX IF NOT EXISTS idx_users_organization_id ON users(organization_id);
-- CREATE INDEX IF NOT EXISTS idx_enquiries_organization_id ON enquiries(organization_id);
-- CREATE INDEX IF NOT EXISTS idx_activities_organization_id ON activities(organization_id);

-- Add foreign key constraints
ALTER TABLE users 
ADD CONSTRAINT fk_users_organization 
FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE RESTRICT;

ALTER TABLE enquiries 
ADD CONSTRAINT fk_enquiries_organization 
FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE RESTRICT;

ALTER TABLE activities 
ADD CONSTRAINT fk_activities_organization 
FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE RESTRICT;

-- Add check constraints for valid values
ALTER TABLE organizations 
ADD CONSTRAINT chk_organization_status 
CHECK (status IN ('active', 'inactive', 'suspended'));

ALTER TABLE organizations 
ADD CONSTRAINT chk_organization_plan 
CHECK (plan IN ('starter', 'pro', 'enterprise'));

-- Add unique constraint for organization email uniqueness
-- This ensures email is unique within an organization, not globally
-- We'll implement this later when we need it
-- ALTER TABLE users DROP CONSTRAINT IF EXISTS users_email_key;
-- CREATE UNIQUE INDEX users_email_org_unique ON users(email, organization_id);

-- Migration: Create invitations table
-- Description: Creates the invitations table for team member invitation system with email notifications
-- Date: 2024

-- Create invitations table
CREATE TABLE IF NOT EXISTS invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    organization_id UUID NOT NULL,
    token VARCHAR(128) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    accepted_at TIMESTAMP WITH TIME ZONE,
    invited_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Add foreign key constraints
    CONSTRAINT fk_invitations_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_invitations_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    CONSTRAINT fk_invitations_invited_by FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_invitations_token ON invitations(token);
CREATE INDEX IF NOT EXISTS idx_invitations_email ON invitations(email);
CREATE INDEX IF NOT EXISTS idx_invitations_organization_id ON invitations(organization_id);
CREATE INDEX IF NOT EXISTS idx_invitations_user_id ON invitations(user_id);
CREATE INDEX IF NOT EXISTS idx_invitations_status ON invitations(status);
CREATE INDEX IF NOT EXISTS idx_invitations_expires_at ON invitations(expires_at);
CREATE INDEX IF NOT EXISTS idx_invitations_invited_by ON invitations(invited_by);

-- Add check constraints for valid values
ALTER TABLE invitations 
ADD CONSTRAINT chk_invitation_role 
CHECK (role IN ('admin', 'manager', 'agent'));

ALTER TABLE invitations 
ADD CONSTRAINT chk_invitation_status 
CHECK (status IN ('pending', 'accepted', 'expired', 'cancelled'));

-- Create trigger to automatically update updated_at timestamp
CREATE TRIGGER update_invitations_updated_at 
    BEFORE UPDATE ON invitations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add pending status to users table if not already present
-- This allows users to be created with pending status until invitation is accepted
DO $$
BEGIN
    -- Check if we need to update the user status constraint
    IF EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'chk_user_status' 
        AND constraint_schema = current_schema()
    ) THEN
        -- Drop existing constraint
        ALTER TABLE users DROP CONSTRAINT chk_user_status;
    END IF;
    
    -- Add updated constraint that includes 'pending'
    ALTER TABLE users 
    ADD CONSTRAINT chk_user_status 
    CHECK (status IN ('active', 'inactive', 'pending'));
    
EXCEPTION
    WHEN OTHERS THEN
        -- If constraint doesn't exist or other error, just add the new one
        ALTER TABLE users 
        ADD CONSTRAINT chk_user_status 
        CHECK (status IN ('active', 'inactive', 'pending'));
END $$;

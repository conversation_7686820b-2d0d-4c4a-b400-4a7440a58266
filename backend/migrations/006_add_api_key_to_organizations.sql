-- Migration: Add API key to organizations table
-- Description: Adds api_key field to organizations table for external API access
-- Date: 2024

-- Add api_key column to organizations table
ALTER TABLE organizations 
ADD COLUMN IF NOT EXISTS api_key VARCHAR(128) UNIQUE;

-- Create index for api_key for faster lookups
CREATE INDEX IF NOT EXISTS idx_organizations_api_key ON organizations(api_key);

-- Generate API keys for existing organizations
-- This will be handled in the application code during startup or migration
-- to ensure proper key generation and uniqueness

-- Add constraint to ensure api_key is not null for active organizations
-- We'll handle this in application code to generate keys for existing orgs first

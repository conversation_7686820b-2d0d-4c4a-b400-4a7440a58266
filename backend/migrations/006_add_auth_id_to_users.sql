-- Migration: Add auth_id column to users table
-- Description: Adds auth_id column to separate internal user ID from Supabase authentication ID
-- Date: 2024

-- Add auth_id column to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS auth_id UUID;

-- Create unique index on auth_id for fast lookups
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_auth_id ON users(auth_id) WHERE auth_id IS NOT NULL;

-- Add comment to explain the purpose
COMMENT ON COLUMN users.auth_id IS 'Supabase authentication user ID - maps to Supabase auth.users.id';
COMMENT ON COLUMN users.id IS 'Internal user ID - independent of authentication provider';

-- Note: auth_id will be NULL for invited users until they complete signup
-- This allows us to create users during invitation process with internal ID
-- and later map them to Supabase auth ID when they sign up

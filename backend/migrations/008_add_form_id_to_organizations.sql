-- Migration: Add form_id column to organizations table
-- This enables form submission URLs like /api/public/forms/{form_id}

-- Add form_id column to organizations table
ALTER TABLE organizations ADD COLUMN form_id VARCHAR(12) UNIQUE;

-- Create index for faster lookups
CREATE INDEX idx_organizations_form_id ON organizations(form_id);

-- Add comment for documentation
COMMENT ON COLUMN organizations.form_id IS 'Short unique identifier for form submissions (e.g., a7k9m2x5)';

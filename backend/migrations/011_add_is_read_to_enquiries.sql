-- Migration: Add is_read column to enquiries table
-- Description: Adds is_read boolean column to enquiries table for tracking read/unread status
-- Date: 2024

-- Add is_read column to enquiries table
ALTER TABLE enquiries 
ADD COLUMN IF NOT EXISTS is_read BOOLEAN NOT NULL DEFAULT false;

-- Create index for is_read for better query performance
CREATE INDEX IF NOT EXISTS idx_enquiries_is_read ON enquiries (is_read);

-- Update existing enquiries to be marked as read (optional - you can skip this if you want all existing enquiries to be unread)
-- UPDATE enquiries SET is_read = true WHERE created_at < NOW();

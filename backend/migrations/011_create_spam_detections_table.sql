-- Migration: Create spam_detections table
-- This table stores the results of GPT-based spam analysis for enquiries

CREATE TABLE IF NOT EXISTS spam_detections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    enquiry_id UUID NOT NULL REFERENCES enquiries(id) ON DELETE CASCADE,
    is_spam BOOLEAN NOT NULL,
    confidence_score FLOAT NOT NULL CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
    reasons JSONB, -- Array of detection reasons from GPT
    deleted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_spam_detections_enquiry_id ON spam_detections(enquiry_id);
CREATE INDEX IF NOT EXISTS idx_spam_detections_score ON spam_detections(confidence_score);
CREATE INDEX IF NOT EXISTS idx_spam_detections_is_spam ON spam_detections(is_spam);

-- Add comment to the table
COMMENT ON TABLE spam_detections IS 'Stores GPT-based spam analysis results for enquiries';
COMMENT ON COLUMN spam_detections.confidence_score IS 'Spam confidence score from 0.0 (not spam) to 1.0 (definitely spam)';
COMMENT ON COLUMN spam_detections.reasons IS 'Array of detection reasons returned by GPT analysis';

# Database Migrations

This directory contains SQL migration files for the enquiry management system database.

## How to Run Migrations

### Prerequisites
- PostgreSQL database connection
- `psql` command-line tool or database management tool
- Database connection details from your `.env` file

### Running Migrations

#### Option 1: Using psql command line

```bash
# Connect to your database and run the migration
psql -h your_host -p your_port -U your_username -d your_database -f migrations/001_create_invitations_table.sql
```

#### Option 2: Using environment variables from .env

```bash
# Load environment variables and run migration
source .env
psql $DATABASE_URL -f migrations/001_create_invitations_table.sql
```

#### Option 3: Copy and paste SQL

1. Connect to your database using your preferred tool (pgAdmin, DBeaver, etc.)
2. Open the migration file `001_create_invitations_table.sql`
3. Copy and paste the SQL content
4. Execute the SQL

### Rolling Back Migrations

To rollback a migration, run the corresponding rollback file:

```bash
psql $DATABASE_URL -f migrations/001_create_invitations_table_rollback.sql
```

## Migration Files

### 001_create_invitations_table.sql
Creates the `invitations` table with the following features:
- UUID primary key
- Foreign key relationships to users and organizations
- Secure token for invitation acceptance
- Status tracking (pending, accepted, expired, cancelled)
- Expiration timestamp
- Automatic updated_at timestamp trigger
- Proper indexes for performance

### 001_create_invitations_table_rollback.sql
Rollback script that removes:
- The invitations table
- All related indexes
- Triggers and functions
- Foreign key constraints

## Table Structure

The invitations table includes:

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| user_id | UUID | Reference to users table |
| organization_id | UUID | Reference to organizations table |
| token | VARCHAR(128) | Secure invitation token |
| email | VARCHAR(255) | Invitee email address |
| role | VARCHAR(50) | Role to be assigned (admin/manager/agent) |
| status | VARCHAR(50) | Invitation status |
| expires_at | TIMESTAMP | When invitation expires |
| accepted_at | TIMESTAMP | When invitation was accepted |
| invited_by | UUID | User who sent the invitation |
| created_at | TIMESTAMP | Record creation time |
| updated_at | TIMESTAMP | Last update time |
| deleted_at | TIMESTAMP | Soft delete timestamp |

## Notes

- Foreign key constraints are commented out by default to avoid dependency issues
- Uncomment the foreign key constraints if you want to enforce referential integrity
- The table uses UUID for all ID fields
- Automatic timestamp updates are handled by database triggers
- Indexes are created for optimal query performance

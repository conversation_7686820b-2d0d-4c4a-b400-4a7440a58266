package models

import (
	"encoding/json"

	"github.com/google/uuid"

	"gorm.io/datatypes"
)

type Activity struct {
	BaseModel
	OrganizationID uuid.UUID      `json:"organization_id" gorm:"type:uuid;not null"`
	EnquiryID      uuid.UUID      `json:"enquiry_id" gorm:"type:uuid;not null"`
	UserID         uuid.UUID      `json:"user_id" gorm:"type:uuid;not null"`
	Type           string         `json:"type" gorm:"not null"`
	Description    string         `json:"description" gorm:"type:text;not null"`
	Metadata       datatypes.JSON `json:"metadata,omitempty" gorm:"type:jsonb"` // For storing additional data

	// Relationships
	Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
}

// ActivityMetadata provides structured metadata for activities
type ActivityMetadata struct {
	OldStatus   string `json:"old_status,omitempty"`
	NewStatus   string `json:"new_status,omitempty"`
	OldPriority string `json:"old_priority,omitempty"`
	NewPriority string `json:"new_priority,omitempty"`
	OldAssignee string `json:"old_assignee,omitempty"`
	NewAssignee string `json:"new_assignee,omitempty"`
	NoteContent string `json:"note_content,omitempty"`
	Changes     string `json:"changes,omitempty"`
}

type ActivityResponse struct {
	ID          uuid.UUID `json:"id"`
	EnquiryID   uuid.UUID `json:"enquiry_id"`
	UserID      uuid.UUID `json:"user_id"`
	Type        string    `json:"type"`
	Description string    `json:"description"`
	Metadata    string    `json:"metadata,omitempty"`
	Timestamp   string    `json:"timestamp"`  // Frontend expects 'timestamp', not 'created_at'
	CreatedAt   string    `json:"created_at"` // Keep for API consistency
	UpdatedAt   string    `json:"updated_at"`
	User        string    `json:"user"` // Frontend expects user as string, not object
}

// TableName specifies the table name for the Activity model
func (Activity) TableName() string {
	return "activities"
}

// Dashboard related models
type DashboardStats struct {
	TotalEnquiries int64 `json:"total_enquiries"`
	NewToday       int64 `json:"new_today"`
	InProgress     int64 `json:"in_progress"`
	Closed         int64 `json:"closed"`
}

type RecentEnquiry struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	AssignedTo  string    `json:"assigned_to"`
	Status      string    `json:"status"`
	SubmittedOn string    `json:"submitted_on"`
	Slug        string    `json:"slug"`
}

type RecentActivity struct {
	ID              uuid.UUID `json:"id"`
	Type            string    `json:"type"`
	Description     string    `json:"description"`
	Timestamp       string    `json:"timestamp"`
	EnquiryName     string    `json:"enquiry_name"`
	EnquirySlug     string    `json:"enquiry_slug"`
	EnquiryEmail    string    `json:"enquiry_email"`
	EnquiryMessage  string    `json:"enquiry_message"`
	EnquiryStatus   string    `json:"enquiry_status"`
	EnquiryPriority string    `json:"enquiry_priority"`
	EnquirySource   string    `json:"enquiry_source"`
	UserName        string    `json:"user_name"`
	UserRole        string    `json:"user_role"`
	Metadata        string    `json:"metadata,omitempty"`
}

type DashboardOverview struct {
	Stats            DashboardStats   `json:"stats"`
	RecentEnquiries  []RecentEnquiry  `json:"recent_enquiries"`
	RecentActivities []RecentActivity `json:"recent_activities"`
}

// Activity types constants
const (
	ActivityEnquiryCreated    = "Enquiry Created"
	ActivityStatusChanged     = "Status Changed"
	ActivityPriorityChanged   = "Priority Changed"
	ActivityAssignmentChanged = "Assignment Changed"
	ActivityNoteAdded         = "Note Added"
	ActivityEnquiryUpdated    = "Enquiry Updated"
	ActivityNoteDeleted       = "Note Deleted"
)

// Helper function to create activity with structured metadata
func CreateActivity(organizationID, enquiryID, userID uuid.UUID, activityType, description string, metadata *ActivityMetadata) Activity {
	activity := Activity{
		OrganizationID: organizationID,
		EnquiryID:      enquiryID,
		UserID:         userID,
		Type:           activityType,
		Description:    description,
	}

	// Convert structured metadata to JSON if provided
	if metadata != nil {
		if jsonData, err := json.Marshal(metadata); err == nil {
			activity.Metadata = jsonData
		}
		// If JSON marshaling fails, metadata stays empty (no error thrown)
	}

	return activity
}

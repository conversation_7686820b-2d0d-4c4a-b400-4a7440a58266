package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
)

type Enquiry struct {
	BaseModel
	OrganizationID uuid.UUID      `json:"organization_id" gorm:"type:uuid;not null"`
	Slug           string         `json:"slug" gorm:"uniqueIndex;not null"`
	Name           string         `json:"name" gorm:"not null"`
	Email          string         `json:"email" gorm:"not null"`
	Phone          string         `json:"phone"`
	FormType       string         `json:"form_type"`
	AssignedToID   *uuid.UUID     `json:"assigned_to_id" gorm:"type:uuid"`
	Status         string         `json:"status" gorm:"not null;default:New"`
	Priority       string         `json:"priority" gorm:"not null;default:Medium"`
	Source         string         `json:"source"`
	Description    string         `json:"description" gorm:"type:text"`
	IsSpam         bool           `json:"is_spam" gorm:"default:false"`
	IsRead         bool           `json:"is_read" gorm:"default:false"`
	Metadata       datatypes.JSON `json:"metadata,omitempty" gorm:"type:jsonb"`
	Summary        string         `json:"summary" gorm:"type:text"`
	SubmittedOn    time.Time      `json:"submitted_on" gorm:"not null;default:CURRENT_TIMESTAMP"`
	// Relationships
	Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
}

type CreateEnquiryRequest struct {
	Name        string         `json:"name" validate:"required"`
	Email       string         `json:"email" validate:"required,email"`
	Phone       string         `json:"phone"`
	FormType    string         `json:"form_type"`
	Priority    string         `json:"priority"`
	Source      string         `json:"source"`
	Description string         `json:"description"`
	Metadata    datatypes.JSON `json:"metadata,omitempty"`
}

type UpdateEnquiryRequest struct {
	AssignedToID *uuid.UUID `json:"assigned_to_id"`
	Status       string     `json:"status"`
	Priority     string     `json:"priority"`
	Source       string     `json:"source"`
	IsSpam       *bool      `json:"is_spam"`
}

type EnquiryFilters struct {
	Search       string `query:"search"`
	Status       string `query:"status"`
	Priority     string `query:"priority"`
	AssignedToID string `query:"assigned_to_id"`
	Page         int    `query:"page"`
	Limit        int    `query:"limit"`
}

// ExternalEnquiryRequest for public API submissions
type ExternalEnquiryRequest struct {
	Name        string                 `json:"name" validate:"required"`
	Email       string                 `json:"email" validate:"required,email"`
	Phone       string                 `json:"phone"`
	FormType    string                 `json:"form_type"`
	Source      string                 `json:"source"`
	Description string                 `json:"description" validate:"required"`
	Metadata    map[string]interface{} `json:"-"` // Will be populated from extra fields
}

// ExternalEnquiryResponse for public API responses
type ExternalEnquiryResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	EnquiryID string `json:"enquiry_id"`
	Slug      string `json:"slug"`
}

// TableName specifies the table name for the Enquiry model
func (Enquiry) TableName() string {
	return "enquiries"
}

// Predefined values
var (
	EnquiryStatuses = []string{"New", "In Progress", "Closed"}
	Priorities      = []string{"High", "Medium", "Low"}
	Sources         = []string{"Website Form", "Phone Call", "Email", "Referral", "Social Media"}
)

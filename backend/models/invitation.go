package models

import (
	"time"

	"github.com/google/uuid"
)

type Invitation struct {
	BaseModel
	UserID         uuid.UUID  `json:"user_id" gorm:"type:uuid;not null"`
	OrganizationID uuid.UUID  `json:"organization_id" gorm:"type:uuid;not null"`
	Token          string     `json:"token" gorm:"uniqueIndex;not null"`
	Email          string     `json:"email" gorm:"not null"`
	Role           string     `json:"role" gorm:"not null"`
	Status         string     `json:"status" gorm:"not null;default:pending"`
	ExpiresAt      time.Time  `json:"expires_at" gorm:"not null"`
	AcceptedAt     *time.Time `json:"accepted_at"`
	InvitedBy      uuid.UUID  `json:"invited_by" gorm:"type:uuid;not null"`

	// Relationships
	User         User         `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	Inviter      User         `json:"inviter,omitempty" gorm:"foreignKey:InvitedBy"`
}

type CreateInvitationRequest struct {
	Email string `json:"email" validate:"required,email"`
	Role  string `json:"role" validate:"required"`
}

type AcceptInvitationRequest struct {
	Token    string `json:"token" validate:"required"`
	Password string `json:"password,omitempty"` // Optional for Google signin
	Name     string `json:"name" validate:"required"`
}

type OnboardInvitedUserRequest struct {
	InvitationToken string `json:"invitation_token" validate:"required"`
	AuthToken       string `json:"supabase_token" validate:"required"`
	Name            string `json:"name" validate:"required"`
}

type InvitationResponse struct {
	ID             string `json:"id"`
	Email          string `json:"email"`
	Role           string `json:"role"`
	Status         string `json:"status"`
	ExpiresAt      string `json:"expires_at"`
	AcceptedAt     string `json:"accepted_at,omitempty"`
	InvitedBy      string `json:"invited_by"`
	InviterName    string `json:"inviter_name"`
	OrganizationID string `json:"organization_id"`
	CreatedAt      string `json:"created_at"`
}

// TableName specifies the table name for the Invitation model
func (Invitation) TableName() string {
	return "invitations"
}

// Predefined values
var (
	InvitationStatuses = []string{"pending", "accepted", "expired", "cancelled"}
)

// Helper methods
func (i *Invitation) IsExpired() bool {
	return time.Now().After(i.ExpiresAt)
}

func (i *Invitation) IsAccepted() bool {
	return i.Status == "accepted"
}

func (i *Invitation) IsPending() bool {
	return i.Status == "pending"
}

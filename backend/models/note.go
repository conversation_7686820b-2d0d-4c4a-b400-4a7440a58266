package models

import (
	"github.com/google/uuid"
)

type Note struct {
	BaseModel
	EnquiryID uuid.UUID `json:"enquiry_id" gorm:"type:uuid;not null;index"`
	UserID    uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	Content   string    `json:"content" gorm:"type:text;not null"`
}

type CreateNoteRequest struct {
	Content string `json:"content" validate:"required"`
}

type NoteResponse struct {
	ID        uuid.UUID `json:"id"`
	EnquiryID uuid.UUID `json:"enquiry_id"`
	UserID    uuid.UUID `json:"user_id"`
	Content   string    `json:"content"`
	CreatedAt string    `json:"created_at"`
	UpdatedAt string    `json:"updated_at"`
	User      *User     `json:"user,omitempty"`
}

// TableName specifies the table name for the Note model
func (Note) TableName() string {
	return "notes"
}

package models

// OnboardRequest represents the request payload for user onboarding
type OnboardRequest struct {
	OrganizationName string `json:"organization_name" validate:"required,min=2,max=100"`
	Domain           string `json:"domain" validate:"required"`
}

// OnboardResponse represents the response after successful onboarding
type OnboardResponse struct {
	User         User         `json:"user"`
	Organization Organization `json:"organization"`
	Message      string       `json:"message"`
}

// OnboardingData contains the data extracted from Supabase JWT for onboarding
type OnboardingData struct {
	UserID string `json:"user_id"`
	Email  string `json:"email"`
	Name   string `json:"name"`
}

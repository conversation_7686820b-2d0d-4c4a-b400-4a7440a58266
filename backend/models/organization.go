package models

import (
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type Organization struct {
	BaseModel
	Name      string         `json:"name" gorm:"not null"`
	Slug      string         `json:"slug" gorm:"uniqueIndex;not null"`
	Domain    string         `json:"domain" gorm:"uniqueIndex"`
	Status    string         `json:"status" gorm:"not null;default:active"`
	Plan      string         `json:"plan" gorm:"not null;default:starter"`
	Settings  datatypes.JSON `json:"settings" gorm:"type:jsonb"`
	CreatedBy uuid.UUID      `json:"created_by" gorm:"type:uuid;not null"`
	MaxUsers  int            `json:"max_users" gorm:"default:10"`
	APIKey    string         `json:"api_key,omitempty" gorm:"uniqueIndex"`
	FormID    string         `json:"form_id,omitempty" gorm:"uniqueIndex;size:12"`
}

// Organization request/response models
type CreateOrganizationRequest struct {
	Name   string `json:"name" validate:"required,min=2,max=100"`
	Slug   string `json:"slug" validate:"required,min=2,max=50,alphanum"`
	Domain string `json:"domain" validate:"omitempty,fqdn"`
	Plan   string `json:"plan" validate:"omitempty,oneof=starter pro enterprise"`
}

type UpdateOrganizationRequest struct {
	Name     string         `json:"name" validate:"omitempty,min=2,max=100"`
	Domain   string         `json:"domain" validate:"omitempty,fqdn"`
	Plan     string         `json:"plan" validate:"omitempty,oneof=starter pro enterprise"`
	Settings datatypes.JSON `json:"settings"`
	MaxUsers int            `json:"max_users" validate:"omitempty,min=1,max=1000"`
}

type OrganizationResponse struct {
	ID        string         `json:"id"`
	Name      string         `json:"name"`
	Slug      string         `json:"slug"`
	Domain    string         `json:"domain"`
	Status    string         `json:"status"`
	Plan      string         `json:"plan"`
	Settings  datatypes.JSON `json:"settings"`
	MaxUsers  int            `json:"max_users"`
	CreatedAt string         `json:"created_at"`
	CreatedBy string         `json:"created_by"`
}

type OrganizationStatsResponse struct {
	TotalUsers     int64 `json:"total_users"`
	ActiveUsers    int64 `json:"active_users"`
	TotalEnquiries int64 `json:"total_enquiries"`
	MaxUsers       int   `json:"max_users"`
	PlanLimits     struct {
		Users    int      `json:"users"`
		Storage  int      `json:"storage_gb"`
		Features []string `json:"features"`
	} `json:"plan_limits"`
}

// TableName specifies the table name for the Organization model
func (Organization) TableName() string {
	return "organizations"
}

// Predefined values
var (
	OrganizationPlans    = []string{"starter", "pro", "enterprise"}
	OrganizationStatuses = []string{"active", "inactive", "suspended"}
)

// Plan limits configuration
var PlanLimits = map[string]struct {
	MaxUsers  int
	StorageGB int
	Features  []string
}{
	"starter": {
		MaxUsers:  10,
		StorageGB: 5,
		Features:  []string{"basic_enquiries", "basic_reports"},
	},
	"pro": {
		MaxUsers:  50,
		StorageGB: 25,
		Features:  []string{"basic_enquiries", "basic_reports", "advanced_reports", "integrations"},
	},
	"enterprise": {
		MaxUsers:  500,
		StorageGB: 100,
		Features:  []string{"basic_enquiries", "basic_reports", "advanced_reports", "integrations", "custom_branding", "sso"},
	},
}

// Helper methods
func (o *Organization) GetPlanLimits() struct {
	MaxUsers  int
	StorageGB int
	Features  []string
} {
	if limits, exists := PlanLimits[o.Plan]; exists {
		return limits
	}
	return PlanLimits["starter"] // Default to starter plan
}

func (o *Organization) CanAddUser() bool {
	// This will be implemented when we add user counting logic
	return true
}

func (o *Organization) HasFeature(feature string) bool {
	limits := o.GetPlanLimits()
	for _, f := range limits.Features {
		if f == feature {
			return true
		}
	}
	return false
}

// GenerateAPIKey generates a new API key for the organization
func (o *Organization) GenerateAPIKey() error {
	// Generate 32 random bytes
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Create API key with org_ prefix
	o.APIKey = "org_" + hex.EncodeToString(bytes)
	return nil
}

// GenerateUniqueAPIKey generates a unique API key for the organization with database validation
func (o *Organization) GenerateUniqueAPIKey(db *gorm.DB) error {
	maxAttempts := 10
	for attempt := 0; attempt < maxAttempts; attempt++ {
		// Generate a new API key
		if err := o.GenerateAPIKey(); err != nil {
			return err
		}

		// Check if this API key already exists
		var existingOrg Organization
		err := db.Where("api_key = ? AND id != ?", o.APIKey, o.ID).First(&existingOrg).Error
		if err != nil {
			// If error is "record not found", the key is unique
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil
			}
			// For other database errors, return the error
			return fmt.Errorf("database error while checking API key uniqueness: %w", err)
		}
		// If no error, the key exists, try again
	}

	return fmt.Errorf("failed to generate unique API key after %d attempts", maxAttempts)
}

// GenerateFormID generates a new form ID for the organization
func (o *Organization) GenerateFormID() error {
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	const length = 10

	// Use crypto/rand for better randomness
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Convert bytes to charset characters
	formID := make([]byte, length)
	for i := 0; i < length; i++ {
		formID[i] = charset[bytes[i]%byte(len(charset))]
	}

	o.FormID = string(formID)
	return nil
}

// GenerateUniqueFormID generates a unique form ID for the organization with database validation
func (o *Organization) GenerateUniqueFormID(db *gorm.DB) error {
	maxAttempts := 10
	for attempt := 0; attempt < maxAttempts; attempt++ {
		// Generate a new form ID
		if err := o.GenerateFormID(); err != nil {
			return err
		}

		// Check if this form ID already exists
		var existingOrg Organization
		err := db.Where("form_id = ? AND id != ?", o.FormID, o.ID).First(&existingOrg).Error
		if err != nil {
			// If error is "record not found", the ID is unique
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil
			}
			// For other database errors, return the error
			return fmt.Errorf("database error while checking form ID uniqueness: %w", err)
		}

		// If we reach here, the ID already exists, so try again
	}

	return fmt.Errorf("failed to generate unique form ID after %d attempts", maxAttempts)
}

// ValidateAPIKey checks if the provided API key matches the organization's API key
func (o *Organization) ValidateAPIKey(apiKey string) bool {
	return o.APIKey != "" && o.APIKey == apiKey
}

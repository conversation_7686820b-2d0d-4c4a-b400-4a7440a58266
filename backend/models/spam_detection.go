package models

import (
	"github.com/google/uuid"
	"gorm.io/datatypes"
)

// SpamDetection represents the result of spam analysis from GPT
type SpamDetection struct {
	BaseModel
	EnquiryID       uuid.UUID      `json:"enquiry_id" gorm:"type:uuid;not null;index"`
	IsSpam          bool           `json:"is_spam" gorm:"not null"`
	ConfidenceScore float64        `json:"confidence_score" gorm:"not null;check:confidence_score >= 0.0 AND confidence_score <= 1.0"`
	Reasons         datatypes.JSON `json:"reasons" gorm:"type:jsonb"` // Array of detection reasons

	// Relationships
	Enquiry Enquiry `json:"enquiry,omitempty" gorm:"foreignKey:EnquiryID"`
}

// TableName returns the table name for SpamDetection model
func (SpamDetection) TableName() string {
	return "spam_detections"
}

// GPTSpamResponse represents the expected response from GPT-4o-mini
type GPTSpamResponse struct {
	IsSpam  bool     `json:"is_spam"`
	Score   float64  `json:"score"`
	Reasons []string `json:"reasons"`
}

// SpamDetectionRequest represents the data sent to GPT for analysis
type SpamDetectionRequest struct {
	Name        string `json:"name"`
	Email       string `json:"email"`
	Phone       string `json:"phone"`
	Description string `json:"description"`
	Source      string `json:"source"`
}

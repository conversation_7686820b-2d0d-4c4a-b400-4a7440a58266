package models

import (
	"time"

	"github.com/google/uuid"
)

type User struct {
	BaseModel
	OrganizationID uuid.UUID  `json:"organization_id" gorm:"type:uuid;not null"`
	AuthID         *uuid.UUID `json:"auth_id,omitempty" gorm:"type:uuid;uniqueIndex"`
	Name           string     `json:"name" gorm:"not null"`
	Email          string     `json:"email" gorm:"uniqueIndex;not null"`
	Phone          string     `json:"phone"`
	JobTitle       string     `json:"job_title"`
	Department     string     `json:"department"`
	Timezone       string     `json:"timezone"`
	Bio            string     `json:"bio" gorm:"type:text"`
	Role           string     `json:"role" gorm:"not null;default:agent"`
	Status         string     `json:"status" gorm:"not null;default:active"`
	LastLogin      *time.Time `json:"last_login"`
	JoinedDate     time.Time  `json:"joined_date" gorm:"not null;default:CURRENT_TIMESTAMP"`

	// Relationships
	Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
}

type CreateUserRequest struct {
	Name  string `json:"name" validate:"required"`
	Email string `json:"email" validate:"required,email"`
	Role  string `json:"role" validate:"required"`
}

type UpdateUserRequest struct {
	Name   string `json:"name"`
	Role   string `json:"role"`
	Status string `json:"status"`
}

type UpdateProfileRequest struct {
	Phone      string `json:"phone"`
	JobTitle   string `json:"job_title"`
	Department string `json:"department"`
	Timezone   string `json:"timezone"`
	Bio        string `json:"bio"`
}

type TeamMemberFilters struct {
	Search string `query:"search"`
	Role   string `query:"role"`
	Page   int    `query:"page"`
	Limit  int    `query:"limit"`
}

type CreateTeamMemberRequest struct {
	Name  string `json:"name" validate:"required"`
	Email string `json:"email" validate:"required,email"`
	Role  string `json:"role" validate:"required"`
}

type UpdateTeamMemberRequest struct {
	Role   string `json:"role"`
	Status string `json:"status"`
}

type TeamMemberResponse struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	Email      string `json:"email"`
	Role       string `json:"role"`
	Status     string `json:"status"`
	LastLogin  string `json:"last_login"`
	JoinedDate string `json:"joined_date"`
	Avatar     string `json:"avatar"`
}

type TeamMembersListResponse struct {
	Members []TeamMemberResponse `json:"members"`
	Total   int64                `json:"total"`
	Page    int                  `json:"page"`
	Limit   int                  `json:"limit"`
}

// TableName specifies the table name for the User model
func (User) TableName() string {
	return "users"
}

// Predefined values
var (
	UserRoles    = []string{"admin", "manager", "agent", "system"}
	UserStatuses = []string{"active", "inactive", "pending"}
)

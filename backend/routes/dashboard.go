package routes

import (
	"enquiry-management-system/config"
	"enquiry-management-system/handlers"
	"enquiry-management-system/middleware"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// SetupDashboardRoutes sets up all dashboard-related routes
func SetupDashboardRoutes(e *echo.Echo, db *gorm.DB, cfg *config.Config) {
	// Initialize dashboard handler
	dashboardHandler := handlers.NewDashboardHandler(db)

	// API routes with authentication
	api := e.Group("/api/v1")

	// Dashboard endpoints (require authentication + organization validation)
	authGroup := api.Group("", middleware.RequireAuth(cfg, db), middleware.RequireOrganizationValidation())
	authGroup.GET("/dashboard/overview", dashboardHandler.GetDashboardOverview)
	authGroup.GET("/dashboard/activities", dashboardHandler.GetActivities)
}

package routes

import (
	"enquiry-management-system/config"
	"enquiry-management-system/handlers"
	"enquiry-management-system/middleware"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// SetupEnquiryRoutes sets up all enquiry-related routes
func SetupEnquiryRoutes(e *echo.Echo, db *gorm.DB, cfg *config.Config) {
	// Initialize handlers
	enquiryHandler := handlers.NewEnquiryHandler(db)
	noteHandler := handlers.NewNoteHandler(db)
	activityHandler := handlers.NewActivityHandler(db)

	// API base group
	api := e.Group("/api/v1")

	// Protected enquiry endpoints (authentication + organization validation required)
	protected := api.Group("", middleware.RequireAuth(cfg, db), middleware.RequireOrganizationValidation())
	protected.GET("/enquiries", enquiryHandler.GetEnquiries)
	protected.GET("/enquiries/counts", enquiryHandler.GetEnquiryCounts)
	protected.GET("/enquiries/:id", enquiryHandler.GetEnquiry)
	protected.GET("/enquiries/slug/:slug", enquiryHandler.GetEnquiryBySlug)
	protected.POST("/enquiries", enquiryHandler.CreateEnquiry)
	protected.PUT("/enquiries/:id", enquiryHandler.UpdateEnquiry)

	// Read/Unread endpoints
	protected.PUT("/enquiries/:id/mark-read", enquiryHandler.MarkEnquiryAsRead)
	protected.PUT("/enquiries/:id/mark-unread", enquiryHandler.MarkEnquiryAsUnread)

	// Admin-only endpoints (authentication + organization validation + admin role required)
	adminOnly := api.Group("", middleware.RequireAdmin(cfg, db), middleware.RequireOrganizationValidation())
	adminOnly.DELETE("/enquiries/:id", enquiryHandler.DeleteEnquiry)

	// Notes endpoints (authentication required)
	protected.POST("/enquiries/:id/notes", noteHandler.CreateNote)
	protected.GET("/enquiries/:id/notes", noteHandler.GetNotes)
	protected.DELETE("/enquiries/:id/notes/:note_id", noteHandler.DeleteNote)

	// Activity endpoints (authentication required)
	protected.GET("/enquiries/:id/activities", activityHandler.GetActivities)
}

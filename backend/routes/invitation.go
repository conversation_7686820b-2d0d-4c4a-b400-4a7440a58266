package routes

import (
	"enquiry-management-system/config"
	"enquiry-management-system/handlers"
	"enquiry-management-system/middleware"
	"enquiry-management-system/services/email"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// SetupInvitationRoutes sets up all invitation related routes
func SetupInvitationRoutes(e *echo.Echo, db *gorm.DB, cfg *config.Config, emailService *email.Service) {
	// Initialize invitation handler
	invitationHandler := handlers.NewInvitationHandler(db, cfg, emailService)

	// API routes
	api := e.Group("/api/v1")

	// Public invitation routes (no auth required) with rate limiting
	publicInvitationGroup := api.Group("/invitations", middleware.InvitationRateLimit())
	publicInvitationGroup.GET("/verify", invitationHandler.VerifyInvitation)
	publicInvitationGroup.POST("/accept", invitationHandler.AcceptInvitation)
	publicInvitationGroup.POST("/onboard", invitationHandler.OnboardInvitedUser)

	// Legacy endpoint (deprecated but kept for backward compatibility)
	publicInvitationGroup.GET("/details", invitationHandler.GetInvitation)

	// Protected invitation routes (require authentication + organization validation)
	protectedGroup := api.Group("", middleware.RequireAuth(cfg, db), middleware.RequireOrganizationValidation())

	// Admin only routes
	adminGroup := protectedGroup.Group("", middleware.RequireAdmin(cfg, db))
	adminGroup.GET("/invitations", invitationHandler.ListInvitations)
	adminGroup.DELETE("/invitations/:id", invitationHandler.CancelInvitation)
}

package routes

import (
	"enquiry-management-system/config"
	"enquiry-management-system/handlers"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// SetupOnboardRoutes sets up all onboarding related routes
func SetupOnboardRoutes(e *echo.Echo, db *gorm.DB, cfg *config.Config) {
	// Initialize onboard handler
	onboardHandler := handlers.NewOnboardHandler(db)

	// API routes
	api := e.Group("/api/v1")

	// Onboarding endpoint (requires Supabase JWT but not our auth middleware)
	// This is for new users who don't exist in our database yet
	api.POST("/onboard", onboardHandler.OnboardUser)
}

package routes

import (
	"enquiry-management-system/config"
	"enquiry-management-system/handlers"
	"enquiry-management-system/middleware"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// SetupOrganizationRoutes sets up all organization related routes
func SetupOrganizationRoutes(e *echo.Echo, db *gorm.DB, cfg *config.Config) {
	// Initialize organization handler
	orgHandler := handlers.NewOrganizationHandler(db)

	// API routes with authentication
	api := e.Group("/api/v1")

	// Organization endpoints (require authentication + organization validation)
	authGroup := api.Group("", middleware.RequireAuth(cfg, db), middleware.RequireOrganizationValidation())
	authGroup.GET("/organizations/current", orgHandler.GetCurrentOrganization)
	authGroup.PUT("/organizations/current", orgHandler.UpdateCurrentOrganization)
	authGroup.GET("/organizations/stats", orgHandler.GetOrganizationStats)

	// API key and form ID management endpoints (admin only)
	adminGroup := api.Group("", middleware.RequireAdmin(cfg, db), middleware.RequireOrganizationValidation())
	adminGroup.GET("/organizations/api-key", orgHandler.GetAPIKey)
	adminGroup.POST("/organizations/api-key/regenerate", orgHandler.RegenerateAPIKey)
	adminGroup.POST("/organizations/form-id/regenerate", orgHandler.RegenerateFormID)

	// Super admin endpoints (for creating organizations)
	// Note: We'll implement super admin middleware later
	// For now, these are protected by regular auth (no org validation for creating new orgs)
	authOnlyGroup := api.Group("", middleware.RequireAuth(cfg, db))
	authOnlyGroup.POST("/organizations", orgHandler.CreateOrganization)
}

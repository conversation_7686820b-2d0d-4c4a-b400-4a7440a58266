package routes

import (
	"enquiry-management-system/handlers"
	"enquiry-management-system/services/email"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// SetupPublicRoutes sets up all public API routes (no JWT authentication required)
func SetupPublicRoutes(e *echo.Echo, db *gorm.DB, emailService *email.Service) {
	// Initialize public handlers
	publicFormHandler := handlers.NewPublicFormHandler(db, emailService)

	// Public API routes
	publicAPI := e.Group("/api/public")

	// Form submission endpoint (no API key required, uses form_id)
	publicAPI.POST("/forms/:form_id", publicFormHandler.SubmitForm)
}

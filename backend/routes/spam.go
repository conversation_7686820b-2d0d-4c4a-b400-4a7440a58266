package routes

import (
	"enquiry-management-system/config"
	"enquiry-management-system/handlers"
	"enquiry-management-system/middleware"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// SetupSpamRoutes sets up spam management routes
func SetupSpamRoutes(e *echo.Echo, db *gorm.DB, cfg *config.Config) {
	spamHandler := handlers.NewSpamManagementHandler(db)

	// API base group
	api := e.Group("/api/v1")

	// Spam management routes (require admin/manager role + organization validation)
	adminGroup := api.Group("", middleware.RequireAdminOrManager(cfg, db), middleware.RequireOrganizationValidation())

	// Manual spam actions
	adminGroup.POST("/spam/enquiries/:id/mark-spam", spamHandler.MarkAsSpam)
	adminGroup.POST("/spam/enquiries/:id/mark-not-spam", spamHandler.MarkAsNotSpam)

	// Spam statistics and detection details
	adminGroup.GET("/spam/stats", spamHandler.GetSpamStats)
}

package routes

import (
	"enquiry-management-system/config"
	"enquiry-management-system/handlers"
	"enquiry-management-system/middleware"
	"enquiry-management-system/services/email"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// SetupUserRoutes sets up all user profile related routes
func SetupUserRoutes(e *echo.Echo, db *gorm.DB, cfg *config.Config, emailService *email.Service) {
	// Initialize user handler
	userHandler := handlers.NewUserHandler(db, cfg, emailService)

	// API routes with authentication
	api := e.Group("/api/v1")

	// User endpoints (require authentication + organization validation)
	authGroup := api.Group("", middleware.RequireAuth(cfg, db), middleware.RequireOrganizationValidation())
	authGroup.GET("/users/me", userHandler.GetMe)
	authGroup.GET("/users", userHandler.GetAllUsers)
	authGroup.GET("/profile", userHandler.GetProfile)
	authGroup.PUT("/profile", userHandler.UpdateProfile)

	// Team management endpoints (require admin/manager roles + organization validation)
	adminGroup := api.Group("", middleware.RequireAdminOrManager(cfg, db), middleware.RequireOrganizationValidation())
	adminGroup.GET("/team/members", userHandler.GetTeamMembers)

	// Only admins can create, update, and delete team members (+ organization validation)
	adminOnlyGroup := api.Group("", middleware.RequireAdmin(cfg, db), middleware.RequireOrganizationValidation())
	adminOnlyGroup.POST("/team/members", userHandler.CreateTeamMember)
	adminOnlyGroup.PUT("/team/members/:id", userHandler.UpdateTeamMember)
	adminOnlyGroup.DELETE("/team/members/:id", userHandler.DeleteTeamMember)
}

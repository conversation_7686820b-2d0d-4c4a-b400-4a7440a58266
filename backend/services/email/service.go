package email

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"enquiry-management-system/config"
)

type Service struct {
	httpClient *http.Client
	apiKey     string
	fromName   string
	fromEmail  string
	baseURL    string
	apiBaseURL string
}

type InvitationData struct {
	InviteeName      string
	InviterName      string
	OrganizationName string
	Role             string
	InviteToken      string
	ExpiresIn        string
}

type EnquiryData struct {
	EnquiryID     string
	Message       string
	CustomerName  string
	CustomerEmail string
	OrgSlug       string
	EnquirySlug   string
}

// ZeptoMail API request structures
type ZeptoEmailRequest struct {
	From     ZeptoFromAddress `json:"from"`
	To       []ZeptoToAddress `json:"to"`
	Subject  string           `json:"subject"`
	HtmlBody string           `json:"htmlbody"`
	TextBody string           `json:"textbody,omitempty"`
}

type ZeptoFromAddress struct {
	Address string `json:"address"`
	Name    string `json:"name,omitempty"`
}

type ZeptoToAddress struct {
	Email<PERSON>ddress ZeptoEmailAddress `json:"email_address"`
}

type <PERSON>epto<PERSON>mailAddress struct {
	Address string `json:"address"`
	Name    string `json:"name,omitempty"`
}

// ZeptoMail API response structures
type ZeptoResponse struct {
	Data    []ZeptoResponseData `json:"data"`
	Message string              `json:"message"`
	Object  string              `json:"object"`
}

type ZeptoResponseData struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"request_id"`
}

// NewService creates a new email service
func NewService(cfg *config.Config) *Service {
	if cfg.Email.ZeptoAPIKey == "" {
		log.Println("Warning: ZeptoMail API key not configured. Email functionality will be disabled.")
		return nil
	}

	return &Service{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		apiKey:     cfg.Email.ZeptoAPIKey,
		fromName:   cfg.Email.FromName,
		fromEmail:  cfg.Email.FromEmail,
		baseURL:    cfg.App.BaseURL,
		apiBaseURL: cfg.Email.APIBaseURL,
	}
}

// sendEmail sends an email using ZeptoMail API
func (s *Service) sendEmail(emailReq ZeptoEmailRequest) error {
	// Marshal request body
	jsonData, err := json.Marshal(emailReq)
	if err != nil {
		return fmt.Errorf("failed to marshal email request: %w", err)
	}

	// Create HTTP request
	req, err := http.NewRequest("POST", s.apiBaseURL+"/email", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Authorization", "Zoho-enczapikey "+s.apiKey)

	// Execute request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to execute HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	// Check status code
	if resp.StatusCode >= 400 {
		log.Printf("ZeptoMail API returned error status: %d, body: %s", resp.StatusCode, string(body))
		return fmt.Errorf("ZeptoMail API returned error status: %d", resp.StatusCode)
	}

	// Parse response for success verification
	var zeptoResp ZeptoResponse
	if err := json.Unmarshal(body, &zeptoResp); err != nil {
		log.Printf("Warning: Could not parse ZeptoMail response, but request succeeded: %v", err)
	} else {
		log.Printf("ZeptoMail response: %s", zeptoResp.Message)
	}

	return nil
}

// SendTeamInvitation sends a team invitation email
func (s *Service) SendTeamInvitation(to string, data InvitationData) error {
	if s == nil {
		log.Println("Email service not configured, skipping invitation email")
		return nil
	}

	subject := fmt.Sprintf("You're invited to join %s", data.OrganizationName)

	// Create invitation URL
	inviteURL := fmt.Sprintf("%s/invite/accept?token=%s", s.baseURL, data.InviteToken)

	// HTML content
	htmlContent := s.generateInvitationHTML(data, inviteURL)

	// Plain text content
	plainTextContent := s.generateInvitationText(data, inviteURL)

	// Create ZeptoMail request
	emailReq := ZeptoEmailRequest{
		From: ZeptoFromAddress{
			Address: s.fromEmail,
			Name:    s.fromName,
		},
		To: []ZeptoToAddress{
			{
				EmailAddress: ZeptoEmailAddress{
					Address: to,
					Name:    data.InviteeName,
				},
			},
		},
		Subject:  subject,
		HtmlBody: htmlContent,
		TextBody: plainTextContent,
	}

	// Send email
	if err := s.sendEmail(emailReq); err != nil {
		log.Printf("Failed to send invitation email: %v", err)
		return fmt.Errorf("failed to send invitation email: %w", err)
	}

	log.Printf("Invitation email sent successfully to %s", to)
	return nil
}

// generateInvitationHTML creates the HTML content for invitation email
func (s *Service) generateInvitationHTML(data InvitationData, inviteURL string) string {
	return fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team Invitation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #0077ff; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px 20px; background-color: white; }
        .button { display: inline-block; padding: 12px 24px; background-color: #0077ff; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
        .role-badge { background-color: #666; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>You're Invited!</h1>
        </div>
        <div class="content">
            <h2>Hello %s,</h2>
            <p><strong>%s</strong> has invited you to join <strong>%s</strong> as a <span class="role-badge">%s</span>.</p>
            
            <p>You'll have access to our enquiry management system where you can:</p>
            <ul>
                <li>Manage customer enquiries</li>
                <li>Collaborate with your team</li>
                <li>Track enquiry progress</li>
                <li>Generate reports and insights</li>
            </ul>
            
            <p>Click the button below to accept your invitation and set up your account:</p>
            
            <div style="text-align: center;">
                <a href="%s" class="button" style="color: white;">Accept Invitation</a>
            </div>
            
            <p><strong>Note:</strong> This invitation will expire in %s. If you don't accept it by then, you'll need to request a new invitation.</p>
            
            <p>If you have any questions, feel free to reach out to your team administrator.</p>
        </div>
        <div class="footer">
            <p>This invitation was sent by %s. If you weren't expecting this invitation, you can safely ignore this email.</p>
        </div>
    </div>
</body>
</html>`,
		data.InviteeName,
		data.InviterName,
		data.OrganizationName,
		data.Role,
		inviteURL,
		data.ExpiresIn,
		data.OrganizationName,
	)
}

// generateInvitationText creates the plain text content for invitation email
func (s *Service) generateInvitationText(data InvitationData, inviteURL string) string {
	return fmt.Sprintf(`Hello %s,

%s has invited you to join %s as a %s.

You'll have access to our enquiry management system where you can:
- Manage customer enquiries
- Collaborate with your team
- Track enquiry progress
- Generate reports and insights

To accept your invitation and set up your account, visit:
%s

Note: This invitation will expire in %s. If you don't accept it by then, you'll need to request a new invitation.

If you have any questions, feel free to reach out to your team administrator.

---
This invitation was sent by %s. If you weren't expecting this invitation, you can safely ignore this email.`,
		data.InviteeName,
		data.InviterName,
		data.OrganizationName,
		data.Role,
		inviteURL,
		data.ExpiresIn,
		data.OrganizationName,
	)
}

// SendNotification sends a notification email for newly created enquiry
func (s *Service) SendNotification(to string, data EnquiryData) error {
	if s == nil {
		log.Println("Email service not configured, skipping notification email")
		return nil
	}

	subject := fmt.Sprintf("New Enquiry from %s - ID: %s", data.CustomerName, data.EnquiryID)

	// HTML content
	htmlContent := s.generateNotificationHTML(data)

	// Plain text content
	plainTextContent := s.generateNotificationText(data)

	// Create ZeptoMail request
	emailReq := ZeptoEmailRequest{
		From: ZeptoFromAddress{
			Address: s.fromEmail,
			Name:    s.fromName,
		},
		To: []ZeptoToAddress{
			{
				EmailAddress: ZeptoEmailAddress{
					Address: to,
					Name:    "",
				},
			},
		},
		Subject:  subject,
		HtmlBody: htmlContent,
		TextBody: plainTextContent,
	}

	// Send email
	if err := s.sendEmail(emailReq); err != nil {
		log.Printf("Failed to send notification email: %v", err)
		return fmt.Errorf("failed to send notification email: %w", err)
	}

	log.Printf("Notification email sent successfully to %s", to)
	return nil
}

// generateNotificationHTML creates the HTML content for enquiry notification email
func (s *Service) generateNotificationHTML(data EnquiryData) string {
	return fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Enquiry Notification</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #0077ff; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px 20px; background-color: white; }
        .enquiry-details { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #0077ff; }
        .button { display: inline-block; padding: 12px 24px; background-color: #0077ff; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
        .enquiry-id { background-color: #f8f9fa; padding: 4px 8px; border-radius: 4px; font-family: monospace; }
        .message-box { background-color: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 New Enquiry Received</h1>
        </div>
        <div class="content">
            <h2>A new enquiry has been submitted</h2>
            
            <div class="enquiry-details">
                <h3>Enquiry Details</h3>

                <p><strong>Name:</strong> %s</p>
                <p><strong>Email:</strong> %s</p>
                
                <h4>Message:</h4>
                <div class="message-box">
                    %s
                </div>
            </div>
            
            <p>Please review this enquiry and respond to the customer promptly.</p>
            
            <div style="text-align: center;">
                <a href="%s/org/%s/enquiries/%s" class="button" style="color: white;">View Enquiry</a>
            </div>
        </div>
        <div class="footer">
            <p>This notification was sent automatically by the Enquiry Management System.</p>
        </div>
    </div>
</body>
</html>`,
		data.CustomerName,
		data.CustomerEmail,
		data.Message,
		s.baseURL,
		data.OrgSlug,
		data.EnquirySlug,
	)
}

// generateNotificationText creates the plain text content for enquiry notification email
func (s *Service) generateNotificationText(data EnquiryData) string {
	return fmt.Sprintf(`New Enquiry Received

A new enquiry has been submitted to the system.

Enquiry Details:
- Enquiry ID: %s
- Customer Name: %s
- Customer Email: %s

Message:
%s

Please review this enquiry and respond to the customer promptly.

View the enquiry at: %s/org/%s/enquiries/%s

---
This notification was sent automatically by the Enquiry Management System.`,
		data.EnquiryID,
		data.CustomerName,
		data.CustomerEmail,
		data.Message,
		s.baseURL,
		data.OrgSlug,
		data.EnquirySlug,
	)
}

// IsConfigured returns true if the email service is properly configured
func (s *Service) IsConfigured() bool {
	return s != nil && s.apiKey != ""
}

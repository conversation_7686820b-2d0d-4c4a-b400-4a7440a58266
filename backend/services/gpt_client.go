package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"

	"enquiry-management-system/models"
)

type GPTClient struct {
	apiKey     string
	baseURL    string
	model      string
	httpClient *http.Client
}

type GPTRequest struct {
	Model       string       `json:"model"`
	Messages    []GPTMessage `json:"messages"`
	Temperature float64      `json:"temperature"`
	MaxTokens   int          `json:"max_tokens"`
}

type GPTMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type GPTResponse struct {
	Choices []struct {
		Message struct {
			Content string `json:"content"`
		} `json:"message"`
	} `json:"choices"`
	Error *struct {
		Message string `json:"message"`
		Type    string `json:"type"`
	} `json:"error,omitempty"`
}

// NewGPTClient creates a new GPT client instance
func NewGPTClient() *GPTClient {
	return &GPTClient{
		apiKey:  os.Getenv("OPENAI_API_KEY"),
		baseURL: "https://api.openai.com/v1/chat/completions",
		model:   "gpt-4o-mini",
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// AnalyzeSpam sends enquiry data to GPT-4o-mini for spam analysis
func (c *GPTClient) AnalyzeSpam(request models.SpamDetectionRequest) (*models.GPTSpamResponse, error) {
	if c.apiKey == "" {
		return nil, fmt.Errorf("OpenAI API key not configured")
	}

	// Create the system prompt
	systemPrompt := `You are a spam-filtering assistant for QueryCRM. Your task is to analyze each incoming enquiry and decide whether it is spam. Produce a JSON object with these fields:

• "is_spam": true or false  
• "score": a float between 0.0 (definitely not spam) and 1.0 (definitely spam)  
• "reasons": an array of strings explaining which rules triggered  

Use the following spam-detection rules and considerations:

1. **Disposable or Free Email Domains**  
   • If the email domain is from a known disposable provider (e.g. mailinator.com, 10minutemail.com), add reason "disposable_email_domain" and increase score by 0.3.

2. **Invalid or Missing Required Fields**  
   • If "name" or "email" is empty or clearly invalid (nonsense characters, single character), add "invalid_contact_info" and increase score by 0.2.

3. **Blacklisted Keywords**  
   • Check message text for typical spam keywords (e.g. "Viagra," "loan," "credit card," "free money," "work from home"). For each keyword, add "blacklisted_keyword:<keyword>" and increase score by 0.1 per occurrence.

4. **Excessive Links or HTML**  
   • If message contains more than one URL or HTML tags, add "excessive_links" and increase score by 0.2.

5. **Repetitive or Copy-Paste Text**  
   • If the message repeats the same word or phrase more than three times, or is clearly generic ("Hello, I would like information about your services."), add "generic_message" and increase score by 0.1.

6. **Suspicious Phone Numbers**  
   • If the phone number field is present but contains non-numeric symbols or patterns of known spam numbers, add "invalid_phone_format" and increase score by 0.1.

7. **Message Length Extremes**  
   • If message is extremely short (<10 characters) or extremely long (>2000 characters), add "odd_length_message" and increase score by 0.1.

8. **Inferred Language & Tone**  
   • If message tone is overtly promotional or urgent ("Act now," "Limited time offer"), add "promo_tone" and increase score by 0.1.

9. **Website URL Consistency**  
    • If the website_url field points to a domain different from the one in source without explanation, add "inconsistent_domain" and increase score by 0.1.

**Final Decision**  
• After applying all rules, cap score between 0.0 and 1.0.  
• If score ≥ 0.5, set "is_spam" to true; otherwise false.

**Output format**  
Respond **only** with valid JSON. For example:

{
  "is_spam": true,
  "score": 0.72,
  "reasons": [
    "disposable_email_domain",
    "blacklisted_keyword:Viagra",
    "excessive_links"
  ]
}`

	// Create user message with enquiry data
	userMessage := fmt.Sprintf(`Analyze this enquiry for spam:

Name: %s
Email: %s
Phone: %s
Source: %s
Message: %s`, request.Name, request.Email, request.Phone, request.Source, request.Description)

	// Prepare GPT request
	gptRequest := GPTRequest{
		Model: c.model,
		Messages: []GPTMessage{
			{Role: "system", Content: systemPrompt},
			{Role: "user", Content: userMessage},
		},
		Temperature: 0.1, // Low temperature for consistent results
		MaxTokens:   500,
	}

	// Send request to OpenAI
	response, err := c.sendRequest(gptRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to send request to OpenAI: %w", err)
	}

	// Parse the response
	return c.parseSpamResponse(response)
}

// sendRequest sends the HTTP request to OpenAI API
func (c *GPTClient) sendRequest(request GPTRequest) (*GPTResponse, error) {
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequest("POST", c.baseURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.apiKey)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var gptResponse GPTResponse
	if err := json.Unmarshal(body, &gptResponse); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if gptResponse.Error != nil {
		return nil, fmt.Errorf("OpenAI API error: %s", gptResponse.Error.Message)
	}

	return &gptResponse, nil
}

// parseSpamResponse parses the GPT response content into SpamResponse
func (c *GPTClient) parseSpamResponse(response *GPTResponse) (*models.GPTSpamResponse, error) {
	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("no choices in GPT response")
	}

	content := response.Choices[0].Message.Content

	var spamResponse models.GPTSpamResponse
	if err := json.Unmarshal([]byte(content), &spamResponse); err != nil {
		return nil, fmt.Errorf("failed to parse spam response JSON: %w", err)
	}

	// Validate score range
	if spamResponse.Score < 0.0 {
		spamResponse.Score = 0.0
	}
	if spamResponse.Score > 1.0 {
		spamResponse.Score = 1.0
	}

	// Ensure is_spam matches score threshold
	spamResponse.IsSpam = spamResponse.Score >= 0.5

	return &spamResponse, nil
}

package services

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"

	"enquiry-management-system/models"

	"gorm.io/gorm"
)

type SpamFilterService struct {
	db        *gorm.DB
	gptClient *GPTClient
	enabled   bool
	threshold float64
}

// NewSpamFilterService creates a new spam filter service instance
func NewSpamFilterService(db *gorm.DB) *SpamFilterService {
	enabled := true
	if envEnabled := os.Getenv("SPAM_DETECTION_ENABLED"); envEnabled != "" {
		if parsed, err := strconv.ParseBool(envEnabled); err == nil {
			enabled = parsed
		}
	}

	threshold := 0.5
	if envThreshold := os.Getenv("SPAM_THRESHOLD"); envThreshold != "" {
		if parsed, err := strconv.ParseFloat(envThreshold, 64); err == nil {
			threshold = parsed
		}
	}

	return &SpamFilterService{
		db:        db,
		gptClient: NewGPTClient(),
		enabled:   enabled,
		threshold: threshold,
	}
}

// AnalyzeEnquiry performs spam analysis on an enquiry
func (s *SpamFilterService) AnalyzeEnquiry(enquiry *models.Enquiry) error {
	if !s.enabled {
		log.Println("Spam detection is disabled")
		return nil
	}

	// Prepare request for GPT analysis
	request := models.SpamDetectionRequest{
		Name:        enquiry.Name,
		Email:       enquiry.Email,
		Phone:       enquiry.Phone,
		Description: enquiry.Description,
		Source:      enquiry.Source,
	}

	// Get spam analysis from GPT
	gptResponse, err := s.gptClient.AnalyzeSpam(request)
	if err != nil {
		log.Printf("Failed to analyze spam for enquiry %s: %v", enquiry.ID, err)
		// Don't fail the enquiry creation if spam detection fails
		return nil
	}

	// Convert reasons to JSON
	reasonsJSON, err := json.Marshal(gptResponse.Reasons)
	if err != nil {
		log.Printf("Failed to marshal reasons for enquiry %s: %v", enquiry.ID, err)
		reasonsJSON = []byte("[]")
	}

	// Create spam detection record
	spamDetection := models.SpamDetection{
		EnquiryID:       enquiry.ID,
		IsSpam:          gptResponse.IsSpam,
		ConfidenceScore: gptResponse.Score,
		Reasons:         reasonsJSON,
	}

	// Save spam detection result
	if err := s.db.Create(&spamDetection).Error; err != nil {
		log.Printf("Failed to save spam detection for enquiry %s: %v", enquiry.ID, err)
		return err
	}

	// Update enquiry spam status if threshold is met
	if gptResponse.Score >= s.threshold {
		enquiry.IsSpam = true
		if err := s.db.Model(enquiry).Update("is_spam", true).Error; err != nil {
			log.Printf("Failed to update enquiry spam status for %s: %v", enquiry.ID, err)
			return err
		}
		log.Printf("Enquiry %s marked as spam (score: %.2f)", enquiry.ID, gptResponse.Score)
	} else {
		log.Printf("Enquiry %s marked as genuine (score: %.2f)", enquiry.ID, gptResponse.Score)
	}

	return nil
}

// MarkAsSpam manually marks an enquiry as spam
func (s *SpamFilterService) MarkAsSpam(enquiryID string) error {
	return s.updateSpamStatus(enquiryID, true)
}

// MarkAsNotSpam manually marks an enquiry as not spam
func (s *SpamFilterService) MarkAsNotSpam(enquiryID string) error {
	return s.updateSpamStatus(enquiryID, false)
}

// updateSpamStatus updates the spam status of an enquiry
func (s *SpamFilterService) updateSpamStatus(enquiryID string, isSpam bool) error {
	result := s.db.Model(&models.Enquiry{}).
		Where("id = ?", enquiryID).
		Update("is_spam", isSpam)

	if result.Error != nil {
		return fmt.Errorf("failed to update spam status: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("enquiry not found")
	}

	action := "not spam"
	if isSpam {
		action = "spam"
	}
	log.Printf("Enquiry %s manually marked as %s", enquiryID, action)

	return nil
}

// GetSpamStats returns spam statistics for an organization
func (s *SpamFilterService) GetSpamStats(organizationID string) (map[string]interface{}, error) {
	var totalEnquiries int64
	var spamEnquiries int64

	// Count total enquiries
	if err := s.db.Model(&models.Enquiry{}).
		Where("organization_id = ?", organizationID).
		Count(&totalEnquiries).Error; err != nil {
		return nil, err
	}

	// Count spam enquiries
	if err := s.db.Model(&models.Enquiry{}).
		Where("organization_id = ? AND is_spam = ?", organizationID, true).
		Count(&spamEnquiries).Error; err != nil {
		return nil, err
	}

	spamRate := 0.0
	if totalEnquiries > 0 {
		spamRate = float64(spamEnquiries) / float64(totalEnquiries) * 100
	}

	return map[string]interface{}{
		"total_enquiries":   totalEnquiries,
		"spam_enquiries":    spamEnquiries,
		"genuine_enquiries": totalEnquiries - spamEnquiries,
		"spam_rate":         spamRate,
	}, nil
}

// IsEnabled returns whether spam detection is enabled
func (s *SpamFilterService) IsEnabled() bool {
	return s.enabled
}

// GetThreshold returns the current spam detection threshold
func (s *SpamFilterService) GetThreshold() float64 {
	return s.threshold
}

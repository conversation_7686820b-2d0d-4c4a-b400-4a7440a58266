package utils

import (
	"time"

	"enquiry-management-system/config"
	"enquiry-management-system/models"

	"github.com/golang-jwt/jwt"
	"github.com/google/uuid"
)

// JWTService handles JWT token operations
type JWTService struct {
	config *config.JWTConfig
}

// NewJWTService creates a new JWT service
func NewJWTService(cfg *config.JWTConfig) *JWTService {
	return &JWTService{config: cfg}
}

// GenerateToken generates a JWT token for a user
func (s *JWTService) GenerateToken(user *models.User, expirationTime time.Duration) (string, error) {
	// Set expiration time
	expiresAt := time.Now().Add(expirationTime)

	// Create claims
	claims := &models.JWTClaims{
		UserID: user.ID.String(),
		Email:  user.Email,
		Role:   user.Role,
		Name:   user.Name,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expiresAt.Unix(),
			IssuedAt:  time.Now().Unix(),
			NotBefore: time.Now().Unix(),
			Issuer:    s.config.Issuer,
			Subject:   user.ID.String(),
			Audience:  s.config.Audience,
		},
	}

	// Create token with claims
	token := jwt.NewWithClaims(jwt.GetSigningMethod(s.config.Algorithm), claims)

	// Sign token with secret
	tokenString, err := token.SignedString([]byte(s.config.Secret))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// ValidateToken validates a JWT token and returns claims
func (s *JWTService) ValidateToken(tokenString string) (*models.JWTClaims, error) {
	// Parse token
	token, err := jwt.ParseWithClaims(tokenString, &models.JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if token.Method.Alg() != s.config.Algorithm {
			return nil, jwt.NewValidationError("invalid signing method", jwt.ValidationErrorSignatureInvalid)
		}
		return []byte(s.config.Secret), nil
	})

	if err != nil {
		return nil, err
	}

	// Extract and validate claims
	claims, ok := token.Claims.(*models.JWTClaims)
	if !ok || !token.Valid {
		return nil, jwt.NewValidationError("invalid token", jwt.ValidationErrorClaimsInvalid)
	}

	// Validate issuer
	if s.config.Issuer != "" && claims.Issuer != s.config.Issuer {
		return nil, jwt.NewValidationError("invalid issuer", jwt.ValidationErrorIssuer)
	}

	// Validate audience
	if s.config.Audience != "" && claims.Audience != s.config.Audience {
		return nil, jwt.NewValidationError("invalid audience", jwt.ValidationErrorAudience)
	}

	return claims, nil
}

// RefreshToken generates a new token with extended expiration
func (s *JWTService) RefreshToken(tokenString string, expirationTime time.Duration) (string, error) {
	// Validate existing token
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		return "", err
	}

	// Parse user ID
	userID, err := uuid.Parse(claims.UserID)
	if err != nil {
		return "", err
	}

	// Create user object for token generation
	user := &models.User{
		BaseModel: models.BaseModel{ID: userID},
		Email:     claims.Email,
		Role:      claims.Role,
		Name:      claims.Name,
	}

	// Generate new token
	return s.GenerateToken(user, expirationTime)
}

// ExtractUserIDFromToken extracts user ID from token without full validation
func (s *JWTService) ExtractUserIDFromToken(tokenString string) (uuid.UUID, error) {
	// Parse token without validation (for expired tokens)
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, &models.JWTClaims{})
	if err != nil {
		return uuid.Nil, err
	}

	claims, ok := token.Claims.(*models.JWTClaims)
	if !ok {
		return uuid.Nil, jwt.NewValidationError("invalid claims", jwt.ValidationErrorClaimsInvalid)
	}

	return uuid.Parse(claims.UserID)
}

// GetTokenExpiration returns the expiration time of a token
func (s *JWTService) GetTokenExpiration(tokenString string) (time.Time, error) {
	claims, err := s.ValidateToken(tokenString)
	if err != nil {
		return time.Time{}, err
	}

	return time.Unix(claims.ExpiresAt, 0), nil
}

// IsTokenExpired checks if a token is expired
func (s *JWTService) IsTokenExpired(tokenString string) bool {
	expiration, err := s.GetTokenExpiration(tokenString)
	if err != nil {
		return true
	}

	return time.Now().After(expiration)
}

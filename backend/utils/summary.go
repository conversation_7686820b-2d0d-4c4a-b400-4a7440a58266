package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
)

const openAIEndpoint = "https://api.openai.com/v1/chat/completions"

type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type OpenAIRequest struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
}

type OpenAIResponse struct {
	Choices []struct {
		Message struct {
			Content string `json:"content"`
		} `json:"message"`
	} `json:"choices"`
}

// EnquiryData holds the data to be summarized
type EnquiryData struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// GenerateEnquirySummary creates a summary from enquiry name, description, and metadata
func GenerateEnquirySummary(name, description string, metadata map[string]interface{}) (string, error) {
	apiKey := os.Getenv("OPENAI_API_KEY")
	if apiKey == "" {
		return "", fmt.Errorf("OPENAI_API_KEY environment variable not set")
	}

	// Build the content string with all available information
	var contentParts []string

	if name != "" {
		contentParts = append(contentParts, fmt.Sprintf("Name: %s", name))
	}

	if description != "" {
		contentParts = append(contentParts, fmt.Sprintf("Description: %s", description))
	}

	if len(metadata) > 0 {
		metadataJSON, err := json.MarshalIndent(metadata, "", "  ")
		if err == nil {
			contentParts = append(contentParts, fmt.Sprintf("Additional Information: %s", string(metadataJSON)))
		}
	}

	if len(contentParts) == 0 {
		return "", fmt.Errorf("no content provided for summarization")
	}

	content := strings.Join(contentParts, "\n\n")

	reqBody := OpenAIRequest{
		Model: "gpt-4o-mini-2024-07-18",
		Messages: []Message{
			{
				Role:    "system",
				Content: "You are a helpful assistant that creates concise, professional summaries of customer enquiries. Focus on the key points and main purpose of the enquiry. Keep the summary to 1-2 sentences that capture the essence of the request.",
			},
			{
				Role:    "user",
				Content: fmt.Sprintf("Please create a concise summary of this customer enquiry:\n\n%s", content),
			},
		},
	}

	body, err := json.Marshal(reqBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequest("POST", openAIEndpoint, bytes.NewBuffer(body))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make API request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API request failed with status %d", resp.StatusCode)
	}

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	var aiResp OpenAIResponse
	if err := json.Unmarshal(respBody, &aiResp); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if len(aiResp.Choices) == 0 {
		return "", fmt.Errorf("no choices returned from API")
	}

	summary := strings.TrimSpace(aiResp.Choices[0].Message.Content)
	if summary == "" {
		return "", fmt.Errorf("empty summary returned")
	}

	return summary, nil
}
func ConvertJSONToMap(jsonData []byte) (map[string]interface{}, error) {
	var result map[string]interface{}
	if len(jsonData) == 0 {
		return result, nil
	}

	err := json.Unmarshal(jsonData, &result)
	return result, err
}

package utils

import (
	"time"

	"enquiry-management-system/models"

	"github.com/golang-jwt/jwt"
	"github.com/google/uuid"
)

// TestJWTGenerator helps generate JWT tokens for testing
type TestJWTGenerator struct {
	secret    string
	issuer    string
	audience  string
	algorithm string
}

// NewTestJWTGenerator creates a new test JWT generator
func NewTestJWTGenerator(secret, issuer, audience, algorithm string) *TestJWTGenerator {
	if secret == "" {
		secret = "test-secret-key-for-development-only"
	}
	if issuer == "" {
		issuer = "test-issuer"
	}
	if audience == "" {
		audience = "test-audience"
	}
	if algorithm == "" {
		algorithm = "HS256"
	}

	return &TestJWTGenerator{
		secret:    secret,
		issuer:    issuer,
		audience:  audience,
		algorithm: algorithm,
	}
}

// GenerateTestToken generates a test JWT token for a user
func (g *TestJWTGenerator) GenerateTestToken(userID, email, role, name string, duration time.Duration) (string, error) {
	// Parse user ID
	uid, err := uuid.Parse(userID)
	if err != nil {
		return "", err
	}

	// Set expiration time
	expiresAt := time.Now().Add(duration)

	// <PERSON><PERSON> claims
	claims := &models.JWTClaims{
		UserID: uid.String(),
		Email:  email,
		Role:   role,
		Name:   name,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expiresAt.Unix(),
			IssuedAt:  time.Now().Unix(),
			NotBefore: time.Now().Unix(),
			Issuer:    g.issuer,
			Subject:   uid.String(),
			Audience:  g.audience,
		},
	}

	// Create token with claims
	token := jwt.NewWithClaims(jwt.GetSigningMethod(g.algorithm), claims)

	// Sign token with secret
	tokenString, err := token.SignedString([]byte(g.secret))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// GenerateAdminToken generates a test admin token
func (g *TestJWTGenerator) GenerateAdminToken(userID, email, name string, duration time.Duration) (string, error) {
	return g.GenerateTestToken(userID, email, "admin", name, duration)
}

// GenerateManagerToken generates a test manager token
func (g *TestJWTGenerator) GenerateManagerToken(userID, email, name string, duration time.Duration) (string, error) {
	return g.GenerateTestToken(userID, email, "manager", name, duration)
}

// GenerateAgentToken generates a test agent token
func (g *TestJWTGenerator) GenerateAgentToken(userID, email, name string, duration time.Duration) (string, error) {
	return g.GenerateTestToken(userID, email, "agent", name, duration)
}

// GenerateExpiredToken generates an expired token for testing
func (g *TestJWTGenerator) GenerateExpiredToken(userID, email, role, name string) (string, error) {
	// Set expiration time in the past
	expiresAt := time.Now().Add(-1 * time.Hour)

	// Parse user ID
	uid, err := uuid.Parse(userID)
	if err != nil {
		return "", err
	}

	// Create claims
	claims := &models.JWTClaims{
		UserID: uid.String(),
		Email:  email,
		Role:   role,
		Name:   name,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expiresAt.Unix(),
			IssuedAt:  time.Now().Add(-2 * time.Hour).Unix(),
			NotBefore: time.Now().Add(-2 * time.Hour).Unix(),
			Issuer:    g.issuer,
			Subject:   uid.String(),
			Audience:  g.audience,
		},
	}

	// Create token with claims
	token := jwt.NewWithClaims(jwt.GetSigningMethod(g.algorithm), claims)

	// Sign token with secret
	tokenString, err := token.SignedString([]byte(g.secret))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// Example usage for testing
func ExampleTokenGeneration() {
	// Create generator
	generator := NewTestJWTGenerator(
		"test-secret-key",
		"test-issuer",
		"test-audience",
		"HS256",
	)

	// Generate tokens for different roles
	adminToken, _ := generator.GenerateAdminToken(
		"123e4567-e89b-12d3-a456-************",
		"<EMAIL>",
		"Admin User",
		24*time.Hour,
	)

	managerToken, _ := generator.GenerateManagerToken(
		"456e7890-e89b-12d3-a456-************",
		"<EMAIL>",
		"Manager User",
		24*time.Hour,
	)

	agentToken, _ := generator.GenerateAgentToken(
		"789e0123-e89b-12d3-a456-************",
		"<EMAIL>",
		"Agent User",
		24*time.Hour,
	)

	// Use tokens in tests
	_ = adminToken
	_ = managerToken
	_ = agentToken
}

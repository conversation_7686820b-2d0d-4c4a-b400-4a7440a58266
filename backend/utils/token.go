package utils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
)

// GenerateSecureToken generates a cryptographically secure random token
func GenerateSecureToken(length int) (string, error) {
	if length <= 0 {
		return "", fmt.<PERSON>rrorf("token length must be positive")
	}

	// Generate random bytes
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random token: %w", err)
	}

	// Convert to hex string
	return hex.EncodeToString(bytes), nil
}

// GenerateInvitationToken generates a secure token for invitations (32 bytes = 64 hex chars)
func GenerateInvitationToken() (string, error) {
	return GenerateSecureToken(32)
}

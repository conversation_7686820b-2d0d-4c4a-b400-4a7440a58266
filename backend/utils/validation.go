package utils

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"

	"github.com/google/uuid"
)

// GenerateSlug creates a URL-friendly slug from a name with UUID suffix for uniqueness
func GenerateSlug(name string) string {
	// Simple slug generation - replace spaces with hyphens and convert to lowercase
	slug := strings.ToLower(strings.ReplaceAll(name, " ", "-"))
	// Add UUID suffix to ensure uniqueness
	uniqueID := uuid.New().String()[:8]
	return fmt.Sprintf("%s-%s", slug, uniqueID)
}

// IsValidEmail validates email format using regex
func IsValidEmail(email string) bool {
	// Simple email validation regex
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// IsValidPriority checks if priority is valid
func IsValidPriority(priority string) bool {
	validPriorities := []string{"High", "Medium", "Low"}
	for _, validPriority := range validPriorities {
		if priority == validPriority {
			return true
		}
	}
	return false
}

// IsValidSource checks if source is valid
func IsValidSource(source string) bool {
	validSources := []string{"Website Form", "Phone Call", "Email", "Referral", "Social Media"}
	for _, validSource := range validSources {
		if source == validSource {
			return true
		}
	}
	return false
}

// IsValidStatus checks if status is valid
func IsValidStatus(status string) bool {
	validStatuses := []string{"New", "In Progress", "Closed"}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// ExtractDomainFromURL extracts the domain name from a URL
func ExtractDomainFromURL(websiteURL string) string {
	if websiteURL == "" {
		return ""
	}

	// Add protocol if missing
	fullURL := websiteURL
	if !strings.HasPrefix(websiteURL, "http://") && !strings.HasPrefix(websiteURL, "https://") {
		fullURL = "https://" + websiteURL
	}

	// Parse URL
	parsedURL, err := url.Parse(fullURL)
	if err != nil {
		// If URL parsing fails, try to extract domain manually
		cleanURL := strings.TrimPrefix(strings.TrimPrefix(websiteURL, "https://"), "http://")
		if idx := strings.Index(cleanURL, "/"); idx != -1 {
			cleanURL = cleanURL[:idx]
		}
		if idx := strings.Index(cleanURL, "?"); idx != -1 {
			cleanURL = cleanURL[:idx]
		}
		if idx := strings.Index(cleanURL, "#"); idx != -1 {
			cleanURL = cleanURL[:idx]
		}
		if idx := strings.Index(cleanURL, ":"); idx != -1 {
			cleanURL = cleanURL[:idx]
		}
		return strings.ToLower(cleanURL)
	}

	return strings.ToLower(parsedURL.Hostname())
}

// ProcessSourceField processes the source field - if it looks like a URL, extract domain, otherwise return as is
func ProcessSourceField(source string) string {
	if source == "" {
		return ""
	}

	// Check if the source looks like a URL (contains a dot and no spaces, or starts with http/https)
	if strings.HasPrefix(source, "http://") || strings.HasPrefix(source, "https://") ||
		(strings.Contains(source, ".") && !strings.Contains(source, " ") && len(source) > 4) {
		// Looks like a URL, extract domain
		return ExtractDomainFromURL(source)
	}

	// Not a URL, return as is
	return source
}

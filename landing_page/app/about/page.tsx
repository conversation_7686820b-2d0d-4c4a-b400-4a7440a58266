'use client';

import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Target, 
  Users, 
  Heart, 
  Shield, 
  Zap, 
  ArrowRight,
  Award,
  Globe,
  TrendingUp
} from 'lucide-react';
import Link from 'next/link';

const team = [
  {
    name: '<PERSON>',
    role: 'CEO & Co-Founder',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b647?w=400&h=400&fit=crop&crop=face',
    bio: 'Former VP of Sales at TechCorp with 15+ years in customer relationship management.',
    linkedin: '#'
  },
  {
    name: '<PERSON>',
    role: 'CTO & Co-Founder',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
    bio: 'Ex-Google engineer specializing in scalable systems and data analytics.',
    linkedin: '#'
  },
  {
    name: '<PERSON>',
    role: 'Head of Product',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
    bio: 'Product leader with a passion for user experience and customer-centric design.',
    linkedin: '#'
  },
  {
    name: 'David Kim',
    role: 'Head of Engineering',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
    bio: 'Full-stack architect with expertise in building enterprise-grade applications.',
    linkedin: '#'
  }
];

const values = [
  {
    icon: Target,
    title: 'Customer-Centric',
    description: 'Every decision begins with your needs. We focus on solving real problems, not just adding features.',
    color: 'from-blue-500 to-cyan-500'
  },
  {
    icon: Users,
    title: 'Collaboration',
    description: 'We believe the best ideas come from different perspectives working together, including yours.',
    color: 'from-green-500 to-emerald-500'
  },
  {
    icon: Heart,
    title: 'Empathy',
    description: "We understand the frustration of missed leads and poor follow-up. That's why we built QueryCRM.",
    color: 'from-red-500 to-pink-500'
  },
  {
    icon: Shield,
    title: 'Trust & Security',
    description: 'Your data is private and protected. We follow strict security practices and respect your privacy at every step.',
    color: 'from-purple-500 to-indigo-500'
  }
];

const stats = [
  { number: '500+', label: 'Businesses Served', icon: Globe },
  { number: '2M+', label: 'queries Managed', icon: TrendingUp },
  { number: '99.9%', label: 'Uptime SLA', icon: Shield },
  { number: '24/7', label: 'Support Available', icon: Heart }
];

export default function AboutPage() {
  return (
    <div className="pt-16">
      {/* Hero Section */}
      <section className="section-spacing bg-gradient-to-br from-blue-50 via-white to-cyan-50">
        <div className="max-w-7xl mx-auto container-padding">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center space-y-6"
          >
            <Badge variant="secondary" className="text-sm font-medium">
              About QueryCRM
            </Badge>
            
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 text-balance">
              Our Mission:{' '}
              <span className=" bg-clip-text text-primary">
                Empowering Businesses
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 max-w-3xl mx-auto text-balance">
              We're building QueryCRM to help teams take control of customer enquiries and turn every message into an opportunity to grow.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Our Story */}
      <section className="section-spacing bg-white">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                Our Story
              </h2>
              <div className="space-y-4 text-gray-600 leading-relaxed">
                <p>
                 QueryCRM was born from a common but costly problem — businesses miss out on growth not because of lack of leads, but because of poor enquiry follow-up.
                </p>
                <p>
                  While building tools for fast-moving teams, our founder, Suraj Sharma, noticed a pattern: form submissions went unanswered, demo requests got buried, and leads fell through the cracks — all because enquiry management was either scattered or an afterthought in bloated CRMs.
                </p>
                <p>
                  QueryCRM was built to fix this.
                </p>
                <p>
                  A clean, lightweight CRM designed solely to capture, track, and follow up on every incoming query — without the clutter or complexity of traditional tools.

                  We're now in beta, working closely with a small group of businesses to refine the product and ensure it delivers exactly what teams need: zero missed opportunities.
                </p>
              </div>
              <Button size="lg" asChild>
                <Link href="/demo">
                  Want to join the beta?
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Link>
              </Button>
            </motion.div>

            {/* <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="grid grid-cols-2 gap-4">
                {stats.map((stat, index) => (
                  <Card key={stat.label} className="text-center p-6 hover:shadow-lg transition-all duration-300">
                    <CardContent className="p-0 space-y-2">
                      <stat.icon className="w-8 h-8 text-primary mx-auto" />
                      <div className="text-2xl font-bold text-gray-900">{stat.number}</div>
                      <div className="text-sm text-gray-600">{stat.label}</div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </motion.div> */}
          </div>
        </div>
      </section>

      {/* Team Section */}
      {/* <section className="section-spacing bg-gray-50">
        <div className="max-w-7xl mx-auto container-padding">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Meet the Team
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              The passionate individuals behind QueryCRM who are dedicated to your success.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="text-center hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-6 space-y-4">
                    <Avatar className="w-20 h-20 mx-auto">
                      <AvatarImage src={member.avatar} alt={member.name} />
                      <AvatarFallback>{member.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                    </Avatar>
                    
                    <div className="space-y-2">
                      <h3 className="font-semibold text-gray-900">{member.name}</h3>
                      <p className="text-sm text-primary font-medium">{member.role}</p>
                      <p className="text-sm text-gray-600 leading-relaxed">{member.bio}</p>
                    </div>
                    
                    <Button variant="outline" size="sm" asChild>
                      <Link href={member.linkedin}>
                        Connect on LinkedIn
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section> */}

      {/* Values Section */}
      <section className="section-spacing bg-white">
        <div className="max-w-7xl mx-auto container-padding">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Core Values
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              The principles that guide how we build, listen, and grow. Everything starts with our users.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={value.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${value.color} flex items-center justify-center flex-shrink-0`}>
                        <value.icon className="w-6 h-6 text-white" />
                      </div>
                      <div className="space-y-2">
                        <h3 className="font-semibold text-gray-900 text-lg">{value.title}</h3>
                        <p className="text-gray-600 leading-relaxed">{value.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Statement */}
      <section className="section-spacing bg-gradient-to-br from-blue-50 to-cyan-50">
        <div className="max-w-4xl mx-auto container-padding text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto">
              <Award className="w-8 h-8 text-white" />
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 text-balance">
              Our Mission
            </h2>
            
            <blockquote className="text-xl text-gray-700 leading-relaxed italic">
              &quot;To empower businesses of all sizes with smart enquiry management tools that turn potential customers into loyal advocates. We're here to make sure no opportunity is missed and every interaction helps drive real growth.&quot;
            </blockquote>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
              <Button size="lg" asChild>
                <Link href="#">
                  Join Our Mission
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Link>
              </Button>
              
              <Button variant="outline" size="lg" asChild>
                <Link href="/demo">
                  Schedule a Demo
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="section-spacing bg-white">
        <div className="max-w-7xl mx-auto container-padding">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <Card className="text-center hover:shadow-lg transition-all duration-300">
                <CardContent className="p-6 space-y-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto">
                    <Zap className="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">Get in Touch</h3>
                  <p className="text-gray-600 text-sm">
                    Have questions? We&apos;d love to hear from you.
                  </p>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/demo">Contact Us</Link>
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="text-center hover:shadow-lg transition-all duration-300">
                <CardContent className="p-6 space-y-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto">
                    <Users className="w-6 h-6 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">Careers</h3>
                  <p className="text-gray-600 text-sm">
                    Join our team and help shape the future of enquiry management.
                  </p>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="#">View Openings</Link>
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="text-center hover:shadow-lg transition-all duration-300">
                <CardContent className="p-6 space-y-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto">
                    <Heart className="w-6 h-6 text-purple-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900">Support</h3>
                  <p className="text-gray-600 text-sm">
                    Need help? Our support team is here 24/7.
                  </p>
                  <Button variant="outline" size="sm" asChild>
                    <Link href="#">Get Support</Link>
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
}
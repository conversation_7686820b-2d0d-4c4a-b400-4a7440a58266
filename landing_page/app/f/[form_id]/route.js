import { NextResponse } from 'next/server';

/**
 * Form submission proxy route handler
 * Forwards requests from https://querycrm.com/f/{form_id} to backend API
 * This keeps the backend API domain hidden from client websites
 */

export async function POST(request, { params }) {
  const formId = params.form_id;
  
  // Validate form_id parameter
  if (!formId || typeof formId !== 'string') {
    return NextResponse.json(
      { error: 'Invalid form ID' },
      { status: 400 }
    );
  }

  try {
    // Get backend URL from environment or default
    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.querycrm.com';
    const targetUrl = `${backendUrl}/api/public/forms/${formId}`;

    console.log(`[FORM_PROXY] Forwarding request to: ${targetUrl}`);

    // Get request body
    let body;
    const contentType = request.headers.get('content-type') || '';

    if (contentType.includes('application/json')) {
      // Handle JSON requests (AJAX)
      body = await request.text();
    } else if (contentType.includes('application/x-www-form-urlencoded')) {
      // Handle form data requests (HTML forms)
      body = await request.text();
    } else if (contentType.includes('multipart/form-data')) {
      // Handle multipart form data (for future file uploads)
      body = await request.arrayBuffer();
    } else {
      // Default to text
      body = await request.text();
    }

    // Prepare headers for backend request
    const forwardHeaders = new Headers();
    
    // Forward essential headers
    const headersToForward = [
      'content-type',
      'user-agent',
      'accept',
      'accept-language',
      'x-forwarded-for',
      'x-real-ip'
    ];

    headersToForward.forEach(headerName => {
      const headerValue = request.headers.get(headerName);
      if (headerValue) {
        forwardHeaders.set(headerName, headerValue);
      }
    });

    // Add client IP information
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    'unknown';
    forwardHeaders.set('x-client-ip', clientIP);

    // Add origin information for backend logging
    const origin = request.headers.get('origin');
    if (origin) {
      forwardHeaders.set('x-original-origin', origin);
    }

    // Forward request to backend
    const backendResponse = await fetch(targetUrl, {
      method: 'POST',
      headers: forwardHeaders,
      body: body,
    });

    console.log(`[FORM_PROXY] Backend response status: ${backendResponse.status}`);

    // Get response data
    const responseData = await backendResponse.text();
    let parsedData;
    
    try {
      parsedData = JSON.parse(responseData);
    } catch (e) {
      // If not JSON, return as text
      parsedData = { message: responseData };
    }

    // Handle redirect responses (for HTML form submissions)
    if (backendResponse.status === 303 || backendResponse.status === 302) {
      const redirectUrl = backendResponse.headers.get('location');
      if (redirectUrl) {
        return NextResponse.redirect(redirectUrl, backendResponse.status);
      }
    }

    // Create response with same status as backend
    const response = NextResponse.json(parsedData, {
      status: backendResponse.status,
    });

    // Add CORS headers for cross-origin requests
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, X-Requested-With');

    return response;

  } catch (error) {
    console.error('[FORM_PROXY] Error forwarding request:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to process form submission',
        message: 'Please try again later'
      },
      { status: 500 }
    );
  }
}

/**
 * Handle OPTIONS requests for CORS preflight
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, X-Requested-With',
      'Access-Control-Max-Age': '86400', // 24 hours
    },
  });
}

/**
 * Handle GET requests - return form information or redirect to documentation
 */
export async function GET(_, { params }) {
  const formId = params.form_id;
  
  return NextResponse.json({
    message: 'Form submission endpoint',
    form_id: formId,
    method: 'POST',
    documentation: 'https://querycrm.com/docs/forms',
    example: {
      url: `https://querycrm.com/f/${formId}`,
      method: 'POST',
      fields: {
        name: 'required',
        email: 'required', 
        message: 'required',
        phone: 'optional'
      }
    }
  }, {
    headers: {
      'Access-Control-Allow-Origin': '*',
    }
  });
}

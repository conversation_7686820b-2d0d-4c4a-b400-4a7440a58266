'use client';

import { <PERSON> } from '@/components/sections/Features/Hero';
import { MainFeatures } from '@/components/sections/Features/MainFeatures';
import { AdditionalFeatures } from '@/components/sections/Features/AdditionalFeatures';
import { Integrations } from '@/components/sections/Features/Integrations';
import { CTA } from '@/components/sections/Features/CTA';
import {
    Inbox,
    Users,
    BarChart3,
    Zap,
    Bell,
    Shield,
    MessageSquare,
    Clock,
    Globe,
    Database,
} from 'lucide-react';

const features = [
  {
    icon: Inbox,
    title: 'Centralized Inbox',
    description:
      'View and manage all your enquiries in one simple dashboard. Connect web forms and campaign pages easily without switching tools.',
    details: [
      'Unified view of all incoming enquiries',
      'Smart categorization and tagging',
      'Bulk actions and filtering options',
      'Real-time status updates',
    ],
    color: 'from-blue-500 to-cyan-500',
    popular: false,
  },
  {
    icon: Users,
    title: 'Team Collaboration',
    description:
      'Assign enquiries, add notes, and work together with your team to make sure every lead gets the attention it deserves.',
    details: [
      'Role-based access (<PERSON><PERSON>, Manager)',
      'Internal notes for team context',
      'Assign and reassign leads easily',
      'Activity visibility for accountability',
    ],
    color: 'from-green-500 to-emerald-500',
    popular: true,
  },
  {
    icon: BarChart3,
    title: 'Activity Overview',
    description:
      'Track how enquiries are progressing, see team assignments, and stay informed without digging through spreadsheets.',
    details: [
      'Quick view of open and closed enquiries',
      'Track who is working on what',
      'Status-based filtering',
      'Simple, visual dashboards',
    ],
    color: 'from-purple-500 to-violet-500',
    popular: false,
  },
  {
    icon: Zap,
    title: 'API Integration',
    description:
    'Connect your website forms to QueryCRM by adding a single API endpoint to your form action. No SDKs or complex setup required.',
    details: [
        'Plug-and-play form action URL',
        'No backend code or SDK needed',
        'Webhook support for real-time updates',
        'Developer help available during beta',
    ],
    color: 'from-yellow-500 to-orange-500',
    popular: false,
  },
  {
    icon: Bell,
    title: 'Email Notifications',
    description:
      'Get notified when new enquiries arrive or when they are assigned. Stay updated without having to log in constantly.',
    details: [
      'Email alerts for new leads and assignments',
      'Customizable preferences',
      'Support for team notifications',
      'Low-noise, high-signal approach',
    ],
    color: 'from-red-500 to-pink-500',
    popular: false,
  },
  {
    icon: Shield,
    title: 'Secure by Design',
    description:
      'We take data protection seriously. Your customer data is encrypted and stored securely using industry best practices.',
    details: [
      'HTTPS and database-level encryption',
      'Role-based access for teams',
      'Private beta access controls',
      'Built with privacy in mind',
    ],
    color: 'from-indigo-500 to-blue-500',
    popular: false,
  },
];

const additionalFeatures = [
  {
    icon: MessageSquare,
    title: 'Auto-Response Setup',
    description:
      'Easily configure thank-you messages to acknowledge customer enquiries after form submission. No complex logic needed.',
  },
  {
    icon: Database,
    title: 'Data Export & Backups',
    description:
      'Download your enquiry data anytime in CSV format. Automated daily backups ensure nothing is lost.',
  },
];

export default function FeaturesPage() {
    return (
        <div className="pt-16">
            <Hero />
            <MainFeatures features={features} />
            <AdditionalFeatures additionalFeatures={additionalFeatures} />
            {/* <Integrations /> */}
            <CTA />
        </div>
    );
}
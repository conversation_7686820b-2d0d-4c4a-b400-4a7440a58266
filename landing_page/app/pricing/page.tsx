'use client';

import { useState } from 'react';
import { Hero } from '@/components/sections/Pricing/Hero';
import { PricingPlans } from '@/components/sections/Pricing/PricingPlans';
import { FeaturesComparison } from '@/components/sections/Pricing/FeaturesComparison';
import { FAQ } from '@/components/sections/Pricing/FAQ';
import { CTA } from '@/components/sections/Pricing/CTA';

const plans = [
	{
		name: 'Starter',
		description: 'Perfect for small businesses getting started',
		monthlyPrice: 29,
		yearlyPrice: 290,
		features: [
			'Up to 500 queries/month',
			'2 team members',
			'Basic analytics dashboard',
			'Email support',
			'Web form integration',
			'Basic automation rules',
		],
		popular: false,
		color: 'from-gray-500 to-gray-600',
	},
	{
		name: 'Professional',
		description: 'Ideal for growing teams and businesses',
		monthlyPrice: 79,
		yearlyPrice: 790,
		features: [
			'Up to 2,000 queries/month',
			'10 team members',
			'Advanced analytics & reporting',
			'Priority email & chat support',
			'API access & webhooks',
			'Custom automation workflows',
			'Team collaboration tools',
			'SLA management',
			'Custom fields & tags',
		],
		popular: true,
		color: 'from-blue-500 to-cyan-500',
	},
	{
		name: 'Enterprise',
		description: 'For large organizations with advanced needs',
		monthlyPrice: 199,
		yearlyPrice: 1990,
		features: [
			'Unlimited queries',
			'Unlimited team members',
			'Advanced analytics & custom reports',
			'24/7 phone & email support',
			'Full API access & custom integrations',
			'Advanced automation & AI features',
			'Single Sign-On (SSO)',
			'Advanced security & compliance',
			'Custom onboarding & training',
			'Dedicated account manager',
		],
		popular: false,
		color: 'from-purple-500 to-indigo-500',
	},
];

const faqs = [
  {
    question: 'Is QueryCRM free?',
    answer:
      'Yes, QueryCRM is completely free during the beta period. You’ll get full access to all features without needing a credit card.',
  },
  {
    question: 'How do I join the beta?',
    answer:
      'Click on “Request Access” and fill out the short form. Our team will review your request and get back to you with access details.',
  },
  {
    question: 'What happens after the beta?',
    answer:
      'After the beta, we’ll introduce flexible pricing plans. Early users will get extended free access and special discounts.',
  },
  {
    question: 'Can I invite my team?',
    answer:
      'Yes, you can invite team members and assign roles like Admin or Manager to collaborate on managing enquiries.',
  },
  {
    question: 'Do you support web form integrations?',
    answer:
      'Yes, you can connect your website form using a simple API endpoint. We’ll guide you through it during onboarding.',
  },
  {
    question: 'How is my data protected?',
    answer:
      'We use secure encryption and role-based access controls to keep your data safe. Privacy and security are top priorities.',
  },
];

export default function PricingPage() {
	const [isYearly, setIsYearly] = useState(false);

	return (
		<div className="pt-16">
			<Hero isYearly={isYearly} setIsYearly={setIsYearly} />
			{/* <PricingPlans isYearly={isYearly} plans={plans} /> */}
			<FeaturesComparison />
			<FAQ faqs={faqs} />
			<CTA />
		</div>
	);
}
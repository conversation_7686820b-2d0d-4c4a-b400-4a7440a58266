'use client';

import { motion } from 'framer-motion';

export function AnalyticsIllustration() {
  return (
    <div className="relative w-full h-full">
      <motion.svg
        viewBox="0 0 300 200"
        className="w-full h-full"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <defs>
          <linearGradient id="analyticsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#0ea5e9" />
            <stop offset="100%" stopColor="#3b82f6" />
          </linearGradient>
        </defs>

        {/* Chart bars */}
        <motion.rect
          x="50"
          y="120"
          width="30"
          height="60"
          rx="4"
          fill="url(#analyticsGradient)"
          initial={{ height: 0, y: 180 }}
          animate={{ height: 60, y: 120 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        />
        <motion.rect
          x="90"
          y="100"
          width="30"
          height="80"
          rx="4"
          fill="#10b981"
          initial={{ height: 0, y: 180 }}
          animate={{ height: 80, y: 100 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        />
        <motion.rect
          x="130"
          y="80"
          width="30"
          height="100"
          rx="4"
          fill="#f59e0b"
          initial={{ height: 0, y: 180 }}
          animate={{ height: 100, y: 80 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        />
        <motion.rect
          x="170"
          y="90"
          width="30"
          height="90"
          rx="4"
          fill="#8b5cf6"
          initial={{ height: 0, y: 180 }}
          animate={{ height: 90, y: 90 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        />

        {/* Trend line */}
        <motion.path
          d="M 65 130 Q 105 110 145 90 T 185 100"
          stroke="url(#analyticsGradient)"
          strokeWidth="3"
          fill="none"
          strokeDasharray="5,5"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 1.2, delay: 1.2 }}
        />

        {/* Data points */}
        <motion.circle
          cx="65"
          cy="130"
          r="4"
          fill="#3b82f6"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3, delay: 1.8 }}
        />
        <motion.circle
          cx="105"
          cy="110"
          r="4"
          fill="#3b82f6"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3, delay: 1.9 }}
        />
        <motion.circle
          cx="145"
          cy="90"
          r="4"
          fill="#3b82f6"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3, delay: 2.0 }}
        />
        <motion.circle
          cx="185"
          cy="100"
          r="4"
          fill="#3b82f6"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3, delay: 2.1 }}
        />

        {/* Percentage indicators */}
        <motion.text
          x="220"
          y="70"
          fontSize="14"
          fill="#10b981"
          fontWeight="bold"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 2.3 }}
        >
          +23%
        </motion.text>
        <motion.text
          x="220"
          y="110"
          fontSize="12"
          fill="#6b7280"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 2.4 }}
        >
          Growth
        </motion.text>
      </motion.svg>
    </div>
  );
}
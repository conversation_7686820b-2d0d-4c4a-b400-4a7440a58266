'use client';

import { motion } from 'framer-motion';

export function DashboardIllustration() {
  return (
    <div className="relative w-full h-full">
      <motion.svg
        viewBox="0 0 400 300"
        className="w-full h-full"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8, delay: 0.2 }}
      >
        {/* Background */}
        <defs>
          <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#f8fafc" />
            <stop offset="100%" stopColor="#e2e8f0" />
          </linearGradient>
          <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#ffffff" />
            <stop offset="100%" stopColor="#f1f5f9" />
          </linearGradient>
          <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#0ea5e9" />
            <stop offset="100%" stopColor="#3b82f6" />
          </linearGradient>
        </defs>

        {/* Main dashboard container */}
        <motion.rect
          x="50"
          y="50"
          width="300"
          height="200"
          rx="16"
          fill="url(#cardGradient)"
          stroke="#e2e8f0"
          strokeWidth="2"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
        />

        {/* Header bar */}
        <motion.rect
          x="50"
          y="50"
          width="300"
          height="40"
          rx="16"
          fill="url(#primaryGradient)"
          initial={{ scaleX: 0 }}
          animate={{ scaleX: 1 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        />

        {/* Navigation dots */}
        <motion.circle
          cx="70"
          cy="70"
          r="3"
          fill="rgba(255,255,255,0.8)"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3, delay: 1.2 }}
        />
        <motion.circle
          cx="85"
          cy="70"
          r="3"
          fill="rgba(255,255,255,0.8)"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3, delay: 1.3 }}
        />
        <motion.circle
          cx="100"
          cy="70"
          r="3"
          fill="rgba(255,255,255,0.8)"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3, delay: 1.4 }}
        />

        {/* Content cards */}
        <motion.rect
          x="70"
          y="110"
          width="80"
          height="60"
          rx="8"
          fill="rgba(59, 130, 246, 0.1)"
          stroke="#3b82f6"
          strokeWidth="1"
          initial={{ y: 140, opacity: 0 }}
          animate={{ y: 110, opacity: 1 }}
          transition={{ duration: 0.5, delay: 1.5 }}
        />
        <motion.rect
          x="160"
          y="110"
          width="80"
          height="60"
          rx="8"
          fill="rgba(16, 185, 129, 0.1)"
          stroke="#10b981"
          strokeWidth="1"
          initial={{ y: 140, opacity: 0 }}
          animate={{ y: 110, opacity: 1 }}
          transition={{ duration: 0.5, delay: 1.6 }}
        />
        <motion.rect
          x="250"
          y="110"
          width="80"
          height="60"
          rx="8"
          fill="rgba(245, 158, 11, 0.1)"
          stroke="#f59e0b"
          strokeWidth="1"
          initial={{ y: 140, opacity: 0 }}
          animate={{ y: 110, opacity: 1 }}
          transition={{ duration: 0.5, delay: 1.7 }}
        />

        {/* Chart representation */}
        <motion.path
          d="M 70 200 Q 110 180 150 190 T 230 170 T 330 185"
          stroke="url(#primaryGradient)"
          strokeWidth="3"
          fill="none"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 1.2, delay: 2 }}
        />

        {/* Data points */}
        <motion.circle
          cx="70"
          cy="200"
          r="4"
          fill="#3b82f6"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3, delay: 2.5 }}
        />
        <motion.circle
          cx="150"
          cy="190"
          r="4"
          fill="#3b82f6"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3, delay: 2.6 }}
        />
        <motion.circle
          cx="230"
          cy="170"
          r="4"
          fill="#3b82f6"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3, delay: 2.7 }}
        />

        {/* Floating elements */}
        <motion.circle
          cx="380"
          cy="80"
          r="6"
          fill="#10b981"
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 0.6 }}
          transition={{ duration: 0.5, delay: 3 }}
        />
        <motion.circle
          cx="20"
          cy="150"
          r="4"
          fill="#f59e0b"
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 0.6 }}
          transition={{ duration: 0.5, delay: 3.2 }}
        />
      </motion.svg>
    </div>
  );
}
'use client';

import { motion } from 'framer-motion';

export function TeamCollaborationIllustration() {
  return (
    <div className="relative w-full h-full">
      <motion.svg
        viewBox="0 0 300 200"
        className="w-full h-full"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <defs>
          <linearGradient id="teamGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#0ea5e9" />
            <stop offset="100%" stopColor="#3b82f6" />
          </linearGradient>
        </defs>

        {/* People illustrations */}
        <motion.g
          initial={{ x: -50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {/* Person 1 */}
          <circle cx="80" cy="80" r="20" fill="url(#teamGradient)" />
          <rect x="70" y="100" width="20" height="30" rx="10" fill="url(#teamGradient)" />
        </motion.g>

        <motion.g
          initial={{ x: 50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          {/* Person 2 */}
          <circle cx="150" cy="80" r="20" fill="#10b981" />
          <rect x="140" y="100" width="20" height="30" rx="10" fill="#10b981" />
        </motion.g>

        <motion.g
          initial={{ x: -50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          {/* Person 3 */}
          <circle cx="220" cy="80" r="20" fill="#f59e0b" />
          <rect x="210" y="100" width="20" height="30" rx="10" fill="#f59e0b" />
        </motion.g>

        {/* Connection lines */}
        <motion.line
          x1="100"
          y1="80"
          x2="130"
          y2="80"
          stroke="url(#teamGradient)"
          strokeWidth="2"
          strokeDasharray="5,5"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 1, delay: 1 }}
        />
        <motion.line
          x1="170"
          y1="80"
          x2="200"
          y2="80"
          stroke="url(#teamGradient)"
          strokeWidth="2"
          strokeDasharray="5,5"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: 1 }}
          transition={{ duration: 1, delay: 1.2 }}
        />

        {/* Collaboration symbols */}
        <motion.circle
          cx="115"
          cy="60"
          r="8"
          fill="#10b981"
          opacity="0.3"
          initial={{ scale: 0, rotate: 0 }}
          animate={{ scale: 1, rotate: 360 }}
          transition={{ duration: 1, delay: 1.5 }}
        />
        <motion.circle
          cx="185"
          cy="60"
          r="8"
          fill="#f59e0b"
          opacity="0.3"
          initial={{ scale: 0, rotate: 0 }}
          animate={{ scale: 1, rotate: -360 }}
          transition={{ duration: 1, delay: 1.7 }}
        />
      </motion.svg>
    </div>
  );
}
'use client';

import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Calendar, Clock, Users, Zap } from 'lucide-react';

const benefits = [
  {
    icon: Calendar,
    title: 'Personalized Walkthrough',
    description:
      'Get a live demo tailored to your business needs and how you handle enquiries today.',
  },
  {
    icon: Clock,
    title: '30-Minute Session',
    description:
      'A complete overview of QueryCRM’s features — focused and value-packed in under 30 minutes.',
  },
  {
    icon: Users,
    title: 'Chat with the Founder',
    description:
      'Get direct insights from the person who built QueryCRM and understand the thinking behind each feature.',
  },
  {
    icon: Zap,
    title: 'See Real Value Fast',
    description:
      'Discover how QueryCRM can help your team stay organized, respond faster, and convert more leads.',
  },
];

export function DemoBenefits() {
    return (
        <section className="section-spacing bg-white">
            <div className="max-w-7xl mx-auto container-padding">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                    className="text-center mb-16"
                >
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                        What to Expect from Your Demo
                    </h2>
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                        We&apos;ll walk you through how QueryCRM helps you manage enquiries more efficiently, assign tasks, and keep your team aligned — all tailored to your business needs.
                    </p>
                </motion.div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                    {benefits.map((benefit, index) => (
                        <motion.div
                            key={benefit.title}
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: index * 0.1 }}
                            viewport={{ once: true }}
                        >
                            <Card className="text-center h-full hover:shadow-lg transition-all duration-300">
                                <CardContent className="p-6 space-y-4">
                                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto">
                                        <benefit.icon className="w-6 h-6 text-blue-600" />
                                    </div>
                                    <h3 className="font-semibold text-gray-900">
                                        {benefit.title}
                                    </h3>
                                    <p className="text-gray-600 text-sm leading-relaxed">
                                        {benefit.description}
                                    </p>
                                </CardContent>
                            </Card>
                        </motion.div>
                    ))}
                </div>
            </div>
        </section>
    );
}
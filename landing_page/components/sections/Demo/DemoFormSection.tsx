'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export function DemoFormSection() {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        company: '',
        message: '',
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSubmitted, setIsSubmitted] = useState(false);
    const { toast } = useToast();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        try {
            // Submit to QueryCRM using the new clean form submission URL
            // Use localhost for development, production URL for production
            const formUrl = process.env.NODE_ENV === 'development'
                ? '/f/q3tai2y4og'  // Use relative URL in development (same domain)
                : 'https://querycrm.com/f/q3tai2y4og'; // Use absolute URL in production

            const response = await fetch(formUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: formData.name,
                    email: formData.email,
                    message: formData.message,
                    company: formData.company,
                    source: 'QueryCRM landing_page',
                    form_type: 'demo_request'
                }),
            });

            if (response.ok) {
                setIsSubmitted(true);
                toast({
                    title: 'Demo request submitted!',
                    description:
                        'Our team will contact you within 24 hours to schedule your personalized demo.',
                });
            } else {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error || 'Failed to submit demo request');
            }
        } catch (error) {
            console.error('Demo form submission error:', error);
            toast({
                title: 'Error',
                description:
                    'There was an error submitting your demo request. Please try again.',
                variant: 'destructive',
            });
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value,
        });
    };

    return (
        <section className="section-spacing bg-gray-50">
            <div className="max-w-4xl mx-auto container-padding">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
                    {/* Form */}
                    <motion.div
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.8 }}
                        viewport={{ once: true }}
                    >
                        {!isSubmitted ? (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="text-2xl">Schedule Your Demo</CardTitle>
                                    <p className="text-gray-600">
                                        Fill out the form below and we&apos;ll get back to you
                                        within 24 hours to schedule your personalized demo.
                                    </p>
                                </CardHeader>
                                <CardContent>
                                    <form onSubmit={handleSubmit} className="space-y-6">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="name">Full Name *</Label>
                                                <Input
                                                    id="name"
                                                    name="name"
                                                    type="text"
                                                    required
                                                    value={formData.name}
                                                    onChange={handleChange}
                                                    placeholder="John Doe"
                                                />
                                            </div>
                                            <div className="space-y-2">
                                                <Label htmlFor="email">Work Email *</Label>
                                                <Input
                                                    id="email"
                                                    name="email"
                                                    type="email"
                                                    required
                                                    value={formData.email}
                                                    onChange={handleChange}
                                                    placeholder="<EMAIL>"
                                                />
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="company">Company Name *</Label>
                                            <Input
                                                id="company"
                                                name="company"
                                                type="text"
                                                required
                                                value={formData.company}
                                                onChange={handleChange}
                                                placeholder="Your Company"
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="message">Tell us about your needs</Label>
                                            <Textarea
                                                id="message"
                                                name="message"
                                                rows={4}
                                                value={formData.message}
                                                onChange={handleChange}
                                                placeholder="What specific challenges are you facing with enquiry management? What would you like to see in the demo?"
                                            />
                                        </div>

                                        <Button
                                            type="submit"
                                            size="lg"
                                            className="w-full"
                                            disabled={isSubmitting}
                                        >
                                            {isSubmitting ? 'Submitting...' : 'Schedule Demo'}
                                        </Button>

                                        <p className="text-xs text-gray-500 text-center">
                                            By submitting this form, you agree to our privacy policy
                                            and terms of service.
                                        </p>
                                    </form>
                                </CardContent>
                            </Card>
                        ) : (
                            <Card>
                                <CardContent className="p-8 text-center space-y-6">
                                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                                        <CheckCircle className="w-8 h-8 text-green-600" />
                                    </div>
                                    <div className="space-y-2">
                                        <h3 className="text-2xl font-bold text-gray-900">
                                            Demo Request Submitted!
                                        </h3>
                                        <p className="text-gray-600">
                                            Thank you for your interest in QueryCRM. Our team will
                                            contact you within 24 hours to schedule your personalized
                                            demo at a time that works best for you.
                                        </p>
                                    </div>
                                    <div className="pt-4 border-t border-gray-200">
                                        <p className="text-sm text-gray-500">
                                            In the meantime, feel free to explore our{' '}
                                            <a
                                                href="/features"
                                                className="text-primary hover:underline"
                                            >
                                                features
                                            </a>{' '}
                                            or{' '}
                                            <a
                                                href="/pricing"
                                                className="text-primary hover:underline"
                                            >
                                                pricing
                                            </a>{' '}
                                            pages.
                                        </p>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </motion.div>

                    {/* Info Panel */}
                    <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.8 }}
                        viewport={{ once: true }}
                        className="space-y-8"
                    >
                        <div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-4">
                                What we&apos;ll cover in your demo:
                            </h3>
                            <ul className="space-y-3">
                                {[
                                    'Full walkthrough of the QueryCRM platform',
                                    'How to centralize enquiries from multiple sources',
                                    'Assigning and managing leads across your team',
                                    'Tracking progress with status updates and internal notes',
                                    'Integration options using our form-based API',
                                    'Tailored solutions for your specific workflow',
                                    'What to expect after the beta (pricing and onboarding)',
                                    'Open Q&A to address your specific questions',
                                ].map((item, index) => (
                                    <li key={index} className="flex items-start space-x-3">
                                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                                        <span className="text-gray-600">{item}</span>
                                    </li>
                                ))}
                            </ul>
                        </div>

                        {/* <div className="bg-blue-50 rounded-lg p-6">
                            <h4 className="font-semibold text-gray-900 mb-3">
                                Why businesses choose QueryCRM:
                            </h4>
                            <div className="space-y-2 text-sm text-gray-600">
                                <div className="flex justify-between">
                                    <span>Average response time improvement</span>
                                    <span className="font-semibold text-green-600">65%</span>
                                </div>
                                <div className="flex justify-between">
                                    <span>Increase in lead conversion</span>
                                    <span className="font-semibold text-green-600">40%</span>
                                </div>
                                <div className="flex justify-between">
                                    <span>Time saved on manual tasks</span>
                                    <span className="font-semibold text-green-600">
                                        8 hours/week
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span>Customer satisfaction rating</span>
                                    <span className="font-semibold text-green-600">4.9/5</span>
                                </div>
                            </div>
                        </div> */}

                        <div className="bg-gray-100 rounded-lg p-6">
                            <h4 className="font-semibold text-gray-900 mb-2">
                                Questions Before Your Demo?
                            </h4>
                            <p className="text-gray-600 text-sm mb-4">
                                We’re happy to help before your demo. Just drop us a message and we’ll get back to you as soon as possible.
                            </p>
                            <Button variant="outline" size="sm" className="w-full">
                                Contact Team
                            </Button>
                        </div>
                    </motion.div>
                </div>
            </div>
        </section>
    );
}
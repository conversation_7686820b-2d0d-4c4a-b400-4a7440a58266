'use client';

import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';

export function Hero() {
    return (
        <section className="section-spacing bg-gradient-to-br from-blue-50 via-white to-cyan-50">
            <div className="max-w-7xl mx-auto container-padding">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    className="text-center space-y-6"
                >
                    <Badge variant="secondary" className="text-sm font-medium">
                        Book Your Demo
                    </Badge>

                    <h1 className="text-4xl md:text-5xl font-bold text-gray-900 text-balance">
                        Request a{' '}
                        <span className=" bg-clip-text text-primary">
                            Personalized Demo
                        </span>
                    </h1>

                    <p className="text-xl text-gray-600 max-w-3xl mx-auto text-balance">
                        See how QueryCRM works for your business.
                        Get a tailored walkthrough focused on your team's enquiry management challenges and goals.
                    </p>
                </motion.div>
            </div>
        </section>
    );
}
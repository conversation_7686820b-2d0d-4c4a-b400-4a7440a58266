'use client';

import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';

interface AdditionalFeature {
    icon: LucideIcon;
    title: string;
    description: string;
}

interface AdditionalFeaturesProps {
    additionalFeatures: AdditionalFeature[];
}

export function AdditionalFeatures({
    additionalFeatures,
}: AdditionalFeaturesProps) {
    return (
        <section className="section-spacing bg-gray-50">
            <div className="max-w-7xl mx-auto container-padding">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                    className="text-center mb-16"
                >
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                        Even More Helpful Features
                    </h2>
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                        From auto-replies to data export, every feature is built to keep your team focused and your leads moving forward.
                    </p>
                </motion.div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {additionalFeatures.map((feature, index) => (
                        <motion.div
                            key={feature.title}
                            initial={{ opacity: 0, y: 20 }}
                            whileInView={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.6, delay: index * 0.1 }}
                            viewport={{ once: true }}
                        >
                            <Card className="h-full hover:shadow-md transition-all duration-300">
                                <CardContent className="p-6">
                                    <div className="flex items-start space-x-4">
                                        <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                                            <feature.icon className="w-5 h-5 text-gray-600" />
                                        </div>
                                        <div className="space-y-2">
                                            <h3 className="font-semibold text-gray-900">
                                                {feature.title}
                                            </h3>
                                            <p className="text-gray-600 text-sm leading-relaxed">
                                                {feature.description}
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </motion.div>
                    ))}
                </div>
            </div>
        </section>
    );
}
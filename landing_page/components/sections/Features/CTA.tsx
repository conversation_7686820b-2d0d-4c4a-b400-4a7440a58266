'use client';

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import Link from 'next/link';
import { RequestAccessModal } from '@/components/modals/RequestAccessModal';

export function CTA() {
    return (
        <section className="section-spacing bg-gradient-primary">
            <div className="max-w-4xl mx-auto container-padding text-center">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                    className="space-y-8"
                >
                    <div className="space-y-4">
                        <h2 className="text-3xl md:text-4xl font-bold text-white text-balance">
                            Ready to Experience QueryCRM?
                        </h2>
                        <p className="text-xl text-blue-100 max-w-2xl mx-auto text-balance">
                            Join the beta for free and discover a faster, simpler way to manage all your customer enquiries in one place.
                        </p>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <RequestAccessModal>
                            <Button
                                size="lg"
                                variant="secondary"
                                className="group font-semibold"
                            >
                                Request Access
                                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                            </Button>
                        </RequestAccessModal>
                        <Button
                            size="lg"
                            variant="outline"
                            className="border-white/20 text-primary hover:bg-white/10"
                            asChild
                        >
                            <Link href="/demo">Book a Demo</Link>
                        </Button>
                    </div>
                </motion.div>
            </div>
        </section>
    );
}
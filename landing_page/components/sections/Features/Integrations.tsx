'use client';

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';

const integrations = [
    'Salesforce',
    'HubSpot',
    'Slack',
    'Microsoft Teams',
    'Zapier',
    'Mailchimp',
    'Stripe',
    'WordPress',
];

export function Integrations() {
    return (
        <section className="section-spacing bg-white">
            <div className="max-w-7xl mx-auto container-padding">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                    className="text-center mb-16"
                >
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                        Seamless Integrations
                    </h2>
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                        Connect QueryCRM with your existing tools and workflows for a unified
                        business experience.
                    </p>
                </motion.div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
                    {integrations.map((integration, index) => (
                        <motion.div
                            key={integration}
                            initial={{ opacity: 0, scale: 0.8 }}
                            whileInView={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.5, delay: index * 0.1 }}
                            viewport={{ once: true }}
                            className="flex items-center justify-center p-6 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                        >
                            <span className="text-lg font-medium text-gray-700">
                                {integration}
                            </span>
                        </motion.div>
                    ))}
                </div>

                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                    className="text-center"
                >
                    <p className="text-gray-600 mb-6">
                        Can&apos;t find your integration? We offer custom integration
                        development and consulting services.
                    </p>
                    <Button variant="outline" size="lg">
                        Request Custom Integration
                    </Button>
                </motion.div>
            </div>
        </section>
    );
}
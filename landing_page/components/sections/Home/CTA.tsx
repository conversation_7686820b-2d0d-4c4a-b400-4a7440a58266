'use client';

import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight, Sparkles } from 'lucide-react';
import Link from 'next/link';
import { RequestAccessModal } from '@/components/modals/RequestAccessModal';

export function CTA() {
  return (
    <section className="section-spacing bg-gradient-to-br from-primary via-blue-600 to-cyan-600 relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: 'radial-gradient(circle at 20% 50%, white 1px, transparent 1px), radial-gradient(circle at 80% 50%, white 1px, transparent 1px)',
          backgroundSize: '100px 100px'
        }} />
      </div>

      {/* Floating elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute top-20 left-20 w-4 h-4 bg-white/20 rounded-full"
          animate={{
            y: [0, -40, 0],
            x: [0, 10, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute top-40 right-32 w-6 h-6 bg-white/10 rounded-full"
          animate={{
            y: [0, 20, 0],
            x: [0, -60, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 left-1/4 w-5 h-5 bg-white/25 rounded-full"
          animate={{
            y: [0, -15, 0],
            x: [0, 15, 0],
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      <div className="relative z-10 max-w-4xl mx-auto container-padding text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          <div className="space-y-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="flex justify-center"
            >
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
                <Sparkles className="w-8 h-8 text-white" />
              </div>
            </motion.div>

            <h2 className="text-4xl md:text-5xl font-bold text-white text-balance">
              Ready to Take Control of Your Enquiries?
            </h2>
            
            <p className="text-xl text-blue-100 max-w-2xl mx-auto text-balance">
              Be among the first to streamline your enquiry management with QueryCRM.
              Join our beta and help shape a tool built to convert more leads into customers.
            </p>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <RequestAccessModal>
              <Button size="lg" variant="secondary" className="group font-semibold">
                Request Access
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </Button>
            </RequestAccessModal>
            
            <Button size="lg" variant="outline" className="border-white/20 text-primary hover:bg-white/10" asChild>
              <Link href="/demo">
                Book a Demo
              </Link>
            </Button>
          </motion.div>

          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="flex justify-center items-center space-x-8 pt-8 text-blue-100"
          >
            <div className="text-center">
              <div className="text-2xl font-bold text-white">Free</div>
              <div className="text-sm">During Beta</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white">No</div>
              <div className="text-sm">Credit Card</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white">Direct</div>
              <div className="text-sm">Team Support</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
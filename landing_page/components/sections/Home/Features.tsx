'use client';

import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Inbox, Users, BarChart3, Zap, Bell, Shield } from 'lucide-react';

const features = [
	{
		icon: Inbox,
		title: 'Centralized Inbox',
		description:
			'Collect all enquiries from website forms and campaigns into one simple dashboard. No more missed messages or scattered leads.',
		color: 'from-blue-500 to-cyan-500',
	},
	{
		icon: Users,
		title: 'Team Collaboration',
		description:
			'Invite team members, assign roles like Admin or Manager, and easily delegate follow-ups. Everyone stays on the same page.',
		color: 'from-green-500 to-emerald-500',
	},
	{
		icon: BarChart3,
		title: 'Activity Tracking',
		description:
			'See the status of each enquiry at a glance. Add internal notes, update progress, and make sure every lead moves forward.',
		color: 'from-purple-500 to-violet-500',
	},
	{
		icon: Zap,
		title: 'Simple API Integration',
		description: 'Add our API URL to your form action and start collecting enquiries in minutes. No manual work, no complex setup.',
		color: 'from-yellow-500 to-orange-500',
	},
	{
		icon: Bell,
		title: '<PERSON><PERSON>',
		description:
			'Get notified when new enquiries come in or assignments change. Stay informed without having to keep checking the dashboard.',
		color: 'from-red-500 to-pink-500',
	},
	{
		icon: Shield,
		title: 'Built-in Security',
		description:
			'Your data is stored securely with industry best practices. We prioritize privacy and reliability so you can focus on your leads.',
		color: 'from-indigo-500 to-blue-500',
	},
];

export function Features() {
	return (
		<section className="section-spacing bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
			{/* Floating elements */}
			<div className="absolute inset-0 pointer-events-none overflow-hidden">
				<motion.div
					className="absolute bottom-12 right-12 w-8 h-8 bg-green-300 rounded-full opacity-40"
					animate={{
						y: [0, -18, 0],
						x: [0, 12, 0],
					}}
					transition={{
						duration: 6,
						repeat: Infinity,
						ease: 'easeInOut',
					}}
				/>
				<motion.div
					className="absolute top-40 left-20 w-6 h-6 bg-purple-700 rounded-full opacity-30"
					animate={{
						y: [0, 18, 0],
						x: [0, -12, 0],
					}}
					transition={{
						duration: 7,
						repeat: Infinity,
						ease: 'easeInOut',
					}}
				/>
				<motion.div
					className="absolute top-40 right-40 w-5 h-5 bg-orange-500 rounded-full opacity-30"
					animate={{
						y: [0, -12, 0],
						x: [0, 12, 0],
					}}
					transition={{
						duration: 8,
						repeat: Infinity,
						ease: 'easeInOut',
					}}
				/>
			</div>
			<div className="max-w-7xl mx-auto container-padding">
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8 }}
					viewport={{ once: true }}
					className="text-center mb-16"
				>
					<h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 text-balance">
						Manage Every Enquiry in One Place
					</h2>
					<p className="text-xl text-gray-600 max-w-3xl mx-auto text-balance">
						Collect, assign, and track customer queries without the complexity.
					</p>
				</motion.div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
					{features.map((feature, index) => (
						<motion.div
							key={feature.title}
							initial={{ opacity: 0, y: 20 }}
							whileInView={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.6, delay: index * 0.1 }}
							viewport={{ once: true }}
						>
							<Card className="group h-full hover:shadow-lg transition-all duration-300 border-0 bg-white/50 backdrop-blur-sm">
								<CardContent className="p-6">
									<div className="space-y-4">
										<div
											className={`w-12 h-12 rounded-lg bg-gradient-to-br ${feature.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
										>
											<feature.icon className="w-6 h-6 text-white" />
										</div>

										<div className="space-y-2">
											<h3 className="text-xl font-semibold text-gray-900 group-hover:text-primary transition-colors">
												{feature.title}
											</h3>
											<p className="text-gray-600 leading-relaxed">
												{feature.description}
											</p>
										</div>
									</div>
								</CardContent>
							</Card>
						</motion.div>
					))}
				</div>
			</div>
		</section>
	);
}
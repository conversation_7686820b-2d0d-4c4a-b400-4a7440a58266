'use client';

import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Star, Quote } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const testimonials = [
	{
		name: '<PERSON>',
		role: 'Marketing Director',
		company: 'TechFlow Solutions',
		avatar:
			'https://images.unsplash.com/photo-1494790108755-2616b612b647?w=400&h=400&fit=crop&crop=face',
		content:
			'QueryCRM has transformed how we handle customer queries. Our response time improved by 65% and we\'ve never missed a lead since implementing it.',
		rating: 5,
	},
	{
		name: '<PERSON>',
		role: 'Sales Manager',
		company: 'Growth Dynamics',
		avatar:
			'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
		content:
			'The analytics dashboard gives us incredible insights into our lead pipeline. We\'ve increased our conversion rate by 40% in just three months.',
		rating: 5,
	},
	{
		name: '<PERSON>',
		role: 'Operations Lead',
		company: 'Scale Ventures',
		avatar:
			'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
		content:
			'The team collaboration features are outstanding. Everyone stays in sync, and our enquiry management process is now completely streamlined.',
		rating: 5,
	},
];

export function Testimonials() {
	return (
		<section className="section-spacing bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
			{/* Floating elements */}
			<div className="absolute inset-0 pointer-events-none overflow-hidden">
				<motion.div
					className="absolute top-10 left-10 w-4 h-4 bg-blue-200 rounded-full opacity-40"
					animate={{
						y: [0, -20, 0],
						x: [0, 10, 0],
					}}
					transition={{
						duration: 6,
						repeat: Infinity,
						ease: 'easeInOut',
					}}
				/>
				<motion.div
					className="absolute bottom-16 right-16 w-6 h-6 bg-yellow-100 rounded-full opacity-30"
					animate={{
						y: [0, 20, 0],
						x: [0, -15, 0],
					}}
					transition={{
						duration: 7,
						repeat: Infinity,
						ease: 'easeInOut',
					}}
				/>
				<motion.div
					className="absolute top-1/2 left-1/3 w-5 h-5 bg-pink-100 rounded-full opacity-30"
					animate={{
						y: [0, -15, 0],
						x: [0, 15, 0],
					}}
					transition={{
						duration: 8,
						repeat: Infinity,
						ease: 'easeInOut',
					}}
				/>
			</div>
			<div className="max-w-7xl mx-auto container-padding">
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					whileInView={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8 }}
					viewport={{ once: true }}
					className="text-center mb-16"
				>
					<h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 text-balance">
						What Our Customers Are Saying
					</h2>
					<p className="text-xl text-gray-600 max-w-3xl mx-auto text-balance">
						Join hundreds of businesses who have transformed their enquiry
						management with QueryCRM.
					</p>
				</motion.div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
					{testimonials.map((testimonial, index) => (
						<motion.div
							key={testimonial.name}
							initial={{ opacity: 0, y: 20 }}
							whileInView={{ opacity: 1, y: 0 }}
							transition={{ duration: 0.6, delay: index * 0.2 }}
							viewport={{ once: true }}
						>
							<Card className="h-full hover:shadow-lg transition-all duration-300 border-0 bg-white">
								<CardContent className="p-6">
									<div className="space-y-4">
										{/* Quote icon */}
										<div className="flex justify-between items-start">
											<Quote className="w-8 h-8 text-primary/20" />
											<div className="flex space-x-1">
												{[...Array(testimonial.rating)].map((_, i) => (
													<Star
														key={i}
														className="w-4 h-4 fill-yellow-400 text-yellow-400"
													/>
												))}
											</div>
										</div>

										{/* Content */}
										<blockquote className="text-gray-700 leading-relaxed">
											&lt;quote&gt;{testimonial.content}&lt;/quote&gt;
										</blockquote>
										{/* Author */}
										<div className="flex items-center space-x-3 pt-4 border-t border-gray-100">
											<Avatar className="w-10 h-10">
												<AvatarImage
													src={testimonial.avatar}
													alt={testimonial.name}
												/>
												<AvatarFallback>
													{testimonial.name
														.split(' ')
														.map((n) => n[0])
														.join('')}
												</AvatarFallback>
											</Avatar>
											<div>
												<div className="font-semibold text-gray-900">
													{testimonial.name}
												</div>
												<div className="text-sm text-gray-500">
													{testimonial.role} at{' '}
													{testimonial.company}
												</div>
											</div>
										</div>
									</div>
								</CardContent>
							</Card>
						</motion.div>
					))}
				</div>
			</div>
		</section>
	);
}
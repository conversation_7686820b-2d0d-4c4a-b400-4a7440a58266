'use client';

import { motion } from 'framer-motion';

export function FeaturesComparison() {
  return (
    <section className="section-spacing bg-gray-50">
      <div className="max-w-7xl mx-auto container-padding">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Compare Features (Beta-Friendly Version)
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            All users get access to our full feature set during beta. Pricing plans will be introduced soon, based on team size and advanced needs.
          </p>
        </motion.div>

        {/* <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-medium text-gray-900">Feature</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-gray-900">Starter</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-gray-900">Professional</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-gray-900">Enterprise</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {[
                  ['Enquiry Management', '✓', '✓', '✓'],
                  ['Team Members', '2', '10', 'Unlimited'],
                  ['Analytics Dashboard', 'Basic', 'Advanced', 'Custom'],
                  ['API Access', '✗', '✓', '✓'],
                  ['Custom Integrations', '✗', 'Limited', 'Unlimited'],
                  ['SSO Support', '✗', '✗', '✓'],
                  ['24/7 Phone Support', '✗', '✗', '✓'],
                  ['Dedicated Account Manager', '✗', '✗', '✓']
                ].map((row, idx) => (
                  <tr key={idx} className={idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">{row[0]}</td>
                    <td className="px-6 py-4 text-sm text-gray-600 text-center">{row[1]}</td>
                    <td className="px-6 py-4 text-sm text-gray-600 text-center">{row[2]}</td>
                    <td className="px-6 py-4 text-sm text-gray-600 text-center">{row[3]}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div> */}
      </div>
    </section>
  );
}
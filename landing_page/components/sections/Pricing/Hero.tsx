'use client';

import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';

interface HeroProps {
  isYearly: boolean;
  setIsYearly: (value: boolean) => void;
}

export function Hero({ isYearly, setIsYearly }: HeroProps) {
  return (
    <section className="section-spacing bg-gradient-to-br from-blue-50 via-white to-cyan-50">
      <div className="max-w-7xl mx-auto container-padding">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center space-y-6"
        >
          <Badge variant="secondary" className="text-sm font-medium">
            Transparent Pricing
          </Badge>
          
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 text-balance">
            Flexible Plans{' '}
            <span className=" bg-clip-text text-primary">
              Coming Soon
            </span>
          </h1>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto text-balance">
            We're currently free during beta. Use all features at no cost and help shape the future of QueryCRM.
          </p>

          {/* Billing Toggle */}
          {/* <div className="flex items-center justify-center space-x-4 pt-4">
            <span className={`text-sm font-medium ${!isYearly ? 'text-gray-900' : 'text-gray-500'}`}>
              Monthly
            </span>
            <Switch
              checked={isYearly}
              onCheckedChange={setIsYearly}
              className="data-[state=checked]:bg-primary"
            />
            <span className={`text-sm font-medium ${isYearly ? 'text-gray-900' : 'text-gray-500'}`}>
              Yearly
            </span>
            <Badge variant="secondary" className="ml-2 text-green-700 bg-green-100">
              Save 17%
            </Badge>
          </div> */}
        </motion.div>
      </div>
    </section>
  );
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Form Submission</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #4F46E5;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #4338CA;
        }
        .note {
            background-color: #FEF3C7;
            border: 1px solid #F59E0B;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .note h3 {
            margin: 0 0 10px 0;
            color: #92400E;
        }
        .note p {
            margin: 0;
            color: #92400E;
            font-size: 14px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Form Submission</h1>
        
        <div class="note">
            <h3>⚠️ Setup Required</h3>
            <p>Replace "YOUR_FORM_ID_HERE" in the form action with your actual form ID from the CRM settings page.</p>
        </div>

        <!-- HTML Form Method -->
        <form action="https://querycrm.com/f/yzi8n5b7zv" method="POST">
            <div class="form-group">
                <label for="name">Name *</label>
                <input type="text" id="name" name="name" required placeholder="Enter your full name">
            </div>

            <div class="form-group">
                <label for="email">Email *</label>
                <input type="email" id="email" name="email" required placeholder="Enter your email address">
            </div>

            <div class="form-group">
                <label for="phone">Phone</label>
                <input type="tel" id="phone" name="phone" placeholder="Enter your phone number">
            </div>

            <div class="form-group">
                <label for="message">Message *</label>
                <textarea id="message" name="message" required placeholder="Enter your message or enquiry"></textarea>
            </div>

            <div class="form-group">
                <label for="company">Company</label>
                <input type="text" id="company" name="company" placeholder="Your company name (optional)">
            </div>

            <div class="form-group">
                <label for="source">How did you hear about us?</label>
                <input type="text" id="source" name="source" placeholder="Google, referral, etc. (optional)">
            </div>
            <input type="tel" name="source" value="Test HTML" hidden>
            <input type="tel" name="form_type" value="test_form" hidden>
            <button type="submit">Send Message</button>
        </form>

        <hr style="margin: 30px 0;">

        <!-- AJAX Method -->
        <h2>Or test with JavaScript (AJAX)</h2>
        <button onclick="testAjaxSubmission()" style="background-color: #10B981;">Test AJAX Submission</button>

        <div id="result" style="margin-top: 20px; padding: 10px; border-radius: 4px; display: none;"></div>
    </div>

    <script>
        function testAjaxSubmission() {
            const formId = 'dhieyz4i2f'; // Replace with actual form ID
            const apiUrl = `http://localhost:8080/api/public/forms/dhieyz4i2f`;
            
            const data = {
                name: 'John Doe (AJAX Test)',
                email: '<EMAIL>',
                phone: '+1234567890',
                message: 'This is a test submission via AJAX',
                company: 'Test Company',
                source: 'AJAX Test'
            };

            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Sending...';
            resultDiv.style.backgroundColor = '#FEF3C7';
            resultDiv.style.color = '#92400E';

            fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                console.log('Success:', data);
                resultDiv.innerHTML = `
                    <strong>✅ Success!</strong><br>
                    Enquiry ID: ${data.enquiry_id}<br>
                    Slug: ${data.slug}<br>
                    Message: ${data.message}
                `;
                resultDiv.style.backgroundColor = '#D1FAE5';
                resultDiv.style.color = '#065F46';
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <strong>❌ Error!</strong><br>
                    ${error.message || 'Failed to submit form'}
                `;
                resultDiv.style.backgroundColor = '#FEE2E2';
                resultDiv.style.color = '#991B1B';
            });
        }
    </script>
</body>
</html>

# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Testing
/coverage
.nyc_output/
junit.xml
test-results/
playwright-report/
test-results.xml
/tests/e2e/videos/
/tests/e2e/screenshots/
/cypress/videos/
/cypress/screenshots/
/cypress/downloads/
/playwright-report/
/playwright/.cache/

# Next.js
/.next/
/out/
.next/

# Production builds
/build
/dist
.vercel
.netlify/

# Environment files
.env*
.env.local
.env.development
.env.test
.env.production
.env.staging

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Certificates
*.pem
*.key
*.crt
*.cert

# Cache directories
.cache/
.parcel-cache/
.turbo/
.vite/
.eslintcache
.stylelintcache

# Temporary folders
tmp/
temp/
.tmp/

# Storybook
storybook-static/
.storybook/public/

# Bundle analysis
bundle-analyzer-report.html
webpack-bundle-analyzer-report.html

# PWA files
**/sw.js
**/workbox-*.js
public/mockServiceWorker.js

# Supabase
.supabase/

# Auto-generated files
src/types/generated/
src/api/generated/
generated/

# Design and documentation
docs/build/
docs/.docusaurus/
design/exports/
screenshots/
visual-regression/

# Performance and monitoring
.clinic/
.0x/
lighthouse/
a11y-reports/
performance-reports/
metrics/

# Local development
.local/
local/
dev.sh
debug.sh
local.sh

# Backup files
*.bak
*.backup
*.tmp
*.temp

# Package manager lock files (uncomment based on preference)
# package-lock.json  # Uncomment if using yarn or pnpm exclusively
# yarn.lock          # Uncomment if using npm or pnpm exclusively
# pnpm-lock.yaml     # Uncomment if using npm or yarn exclusively

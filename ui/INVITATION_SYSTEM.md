# Team Member Invitation System

This document describes the complete team member invitation system with email notifications and member onboarding.

## Overview

The invitation system allows administrators to invite new team members via email. The complete flow includes:

1. **<PERSON><PERSON> creates team member** → System sends invitation email via SendGrid
2. **User clicks email link** → Frontend automatically verifies token
3. **User completes signup** → Account activated and ready to use

## ✅ Features Implemented

### Backend Features
- ✅ **SendGrid Email Integration** - Professional HTML/text email templates
- ✅ **Secure Token System** - 64-character cryptographically secure tokens
- ✅ **Rate Limiting** - 10 requests per minute per IP to prevent abuse
- ✅ **Token Verification API** - Auto-verification when page loads
- ✅ **Enhanced Acceptance API** - Re-verification and detailed responses
- ✅ **Database Migration** - Complete invitation table with proper indexes
- ✅ **Organization Context** - Multi-tenant support with organization branding

### Frontend Features
- ✅ **Member Onboarding Page** - Separate from normal signup with organization context
- ✅ **Auto-verification** - Token verified automatically when page loads
- ✅ **Dual Authentication** - Both Google signin and email/password options
- ✅ **Organization Branding** - Shows organization name and role information
- ✅ **Comprehensive Error Handling** - Specific pages for different error states
- ✅ **Responsive Design** - Works on all devices with beautiful UI

## 🔧 Technical Implementation

### File Structure
```
Backend:
├── handlers/invitation.go          # Invitation API handlers
├── services/email/service.go       # SendGrid email service
├── models/invitation.go            # Invitation data models
├── utils/token.go                  # Secure token generation
├── middleware/rate_limit.go        # Rate limiting middleware
├── routes/invitation.go            # Invitation routes setup
├── migrations/005_create_invitations_table.sql
└── docs/INVITATION_API.md          # Complete API documentation

Frontend:
├── app/invite/accept/page.js       # Main invitation acceptance page
├── components/auth/
│   ├── MemberOnboardingForm.jsx    # Onboarding form component
│   └── InvitationErrorPage.jsx     # Error page components
├── lib/api/invitation.js           # Invitation API utilities
└── INVITATION_SYSTEM.md           # This documentation
```

### API Endpoints

#### Public Endpoints (Rate Limited)
- `GET /api/v1/invitations/verify?token=xxx` - Verify invitation token
- `POST /api/v1/invitations/onboard` - Onboard invited user after Supabase auth (NEW)
- `POST /api/v1/invitations/accept` - Accept invitation (DEPRECATED)

#### Protected Endpoints (Admin Only)
- `GET /api/v1/invitations` - List all organization invitations
- `DELETE /api/v1/invitations/:id` - Cancel pending invitation

### Frontend Routes
- `/invite/accept?token=xxx` - Member onboarding page

## 🚀 User Experience Flow

### 1. Admin Invites Team Member
```
Admin → Team Settings → Add Member → 
System creates user with "pending" status → 
Generates secure token → 
Sends professional email via SendGrid
```

### 2. User Receives Email
```
Professional HTML email with:
- Organization branding
- Role information  
- Secure invitation link
- 7-day expiration notice
```

### 3. User Clicks Link
```
/invite/accept?token=xxx loads →
Auto-verification happens →
If valid: Show onboarding form
If invalid: Show specific error page
```

### 4. User Completes Signup (NEW FLOW)
```
Choose authentication method:
- Google signin (name required)
- Email/password (name + password required)

Step 1: Complete Supabase authentication
- Google OAuth or email/password signup
- Get Supabase JWT token from session

Step 2: Backend onboarding process
- Call /api/v1/invitations/onboard with:
  - invitation_token
  - supabase_token (JWT)
  - name
- Backend links Supabase user ID to internal user
- Updates user status to "active"
- Marks invitation as "accepted"

Step 3: Redirect to organization dashboard
- Frontend redirects to /org/{slug}/dashboard
- User can now authenticate with Supabase
```

## 🔒 Security Features

### Token Security
- **Cryptographically Secure**: 64-character hex tokens using crypto/rand
- **Time-Limited**: 7-day automatic expiration
- **Single Use**: Tokens become invalid after acceptance
- **Organization Scoped**: Tokens tied to specific organizations

### Rate Limiting
- **10 requests per minute per IP** for invitation endpoints
- **Automatic cleanup** of rate limit data
- **Graceful error messages** for rate limit violations

### Validation
- **Double Verification**: Token verified in both verify and accept endpoints
- **Status Checking**: Prevents acceptance of expired/cancelled invitations
- **Organization Validation**: Ensures users can only accept invitations for their organization

## 📧 Email Configuration

### Environment Variables
```env
# SendGrid Configuration
SENDGRID_API_KEY=your-sendgrid-api-key-here
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Your Company Name

# Application URLs
APP_BASE_URL=http://localhost:3000
```

### Email Features
- **Professional HTML Template** with organization branding
- **Plain Text Fallback** for all email clients
- **Role-based Content** showing assigned role
- **Expiration Information** with clear timeline
- **Secure Links** with unique tokens

## 🎨 UI Components

### MemberOnboardingForm
- **Organization Info Display** with name and role
- **Pre-filled Email** (read-only from invitation)
- **Name Input** (required for all signup methods)
- **Google Signin Button** with Google branding
- **Email/Password Form** with validation
- **Loading States** and error handling

### Error Pages
- **Expired Invitation** - Contact admin message
- **Already Accepted** - Login link provided
- **Invalid/Not Found** - Clear error explanation
- **Cancelled** - Contact admin guidance
- **Rate Limited** - Retry guidance
- **Network/Server Errors** - Retry functionality

## 🔧 Setup Instructions

### 1. Database Migration
```bash
# Run the migration
psql $DATABASE_URL -f backend/migrations/005_create_invitations_table.sql
```

### 2. Environment Configuration
```bash
# Add to backend/.env
SENDGRID_API_KEY=your-actual-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Your Company Name
APP_BASE_URL=http://localhost:3000
```

### 3. Start Services
```bash
# Backend
cd backend && go run main.go

# Frontend  
cd ui && npm run dev
```

## 🧪 Testing the Flow

### 1. Create Team Member
```bash
# As admin, create a team member via API or UI
POST /api/v1/team/members
{
  "name": "John Doe",
  "email": "<EMAIL>", 
  "role": "agent"
}
```

### 2. Check Email
- Email sent via SendGrid
- Contains invitation link: `/invite/accept?token=xxx`

### 3. Test Invitation Page
- Visit the link from email
- Should auto-verify and show onboarding form
- Test both Google and email/password signup

### 4. Verify Account
- User status should change to "active"
- User should be able to login normally

## 🚨 Error Scenarios

The system handles all error cases gracefully:

- **Expired Token** → Contact admin page
- **Invalid Token** → Clear error message  
- **Already Accepted** → Login redirect
- **Cancelled Invitation** → Contact admin
- **Rate Limiting** → Retry guidance
- **Network Issues** → Retry functionality

## 📊 Monitoring

### Logs
- Email sending success/failure
- Token verification attempts
- Rate limiting violations
- Invitation acceptance events

### Database
- Track invitation status changes
- Monitor expiration rates
- Audit invitation usage

---

## ✅ **System Ready for Production!**

The invitation system is fully implemented with:
- ✅ Secure backend API with rate limiting
- ✅ Professional email templates via SendGrid  
- ✅ Beautiful member onboarding experience
- ✅ Comprehensive error handling
- ✅ Multi-tenant organization support
- ✅ Complete documentation and testing guides

The system provides a seamless, secure, and professional team member invitation experience! 🚀

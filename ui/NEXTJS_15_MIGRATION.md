# Next.js 15 Migration Guide

## Overview
This document outlines the migration to Next.js 15 and the breaking changes that were addressed in the enquiry management system.

## Key Changes in Next.js 15

### 1. Async Dynamic Route Parameters
**Breaking Change**: In Next.js 15, `params` in dynamic routes must be awaited.

#### Before (Next.js 14):
```javascript
export default function Page({ params }) {
  const { slug } = params; // ❌ This causes an error in Next.js 15
}
```

#### After (Next.js 15):
```javascript
import { use } from "react";

export default function Page({ params }) {
  const { slug } = use(params); // ✅ Correct for client components
}

// OR for server components:
export default async function Page({ params }) {
  const { slug } = await params; // ✅ Correct for server components
}
```

### 2. Client vs Server Components

#### Client Components (using hooks):
```javascript
"use client";
import { use } from "react";

export default function ClientPage({ params }) {
  const { slug } = use(params);
  // Use hooks here
}
```

#### Server Components (async):
```javascript
// No "use client" directive
export default async function ServerPage({ params }) {
  const { slug } = await params;
  // Server-side logic here
}
```

## Files Updated

### ✅ Fixed Files:
- `ui/app/enquiries/[slug]/page.js` - Added `use(params)` for client component

### ✅ Already Compliant:
- `ui/app/auth/callback/route.js` - Route handler (different pattern)
- All other pages use static routes

## Current Project Status

### Dependencies Updated:
- **Next.js**: Latest version (15.x)
- **React**: 19.0.0 (latest)
- **ESLint Config**: Updated to match Next.js version

### Architecture:
- **App Router**: ✅ Using latest App Router patterns
- **Client Components**: ✅ Properly marked with "use client"
- **Server Components**: ✅ Using async/await where needed
- **Dynamic Routes**: ✅ Fixed async params handling

## Best Practices for Next.js 15

### 1. Dynamic Route Parameters
```javascript
// ✅ Client Component Pattern
"use client";
import { use } from "react";

export default function Page({ params, searchParams }) {
  const { id } = use(params);
  const search = use(searchParams);
}

// ✅ Server Component Pattern
export default async function Page({ params, searchParams }) {
  const { id } = await params;
  const search = await searchParams;
}
```

### 2. Error Boundaries
```javascript
// error.js - Error boundary for route segments
"use client";

export default function Error({ error, reset }) {
  return (
    <div>
      <h2>Something went wrong!</h2>
      <button onClick={() => reset()}>Try again</button>
    </div>
  );
}
```

### 3. Loading States
```javascript
// loading.js - Loading UI for route segments
export default function Loading() {
  return <div>Loading...</div>;
}
```

### 4. Not Found Pages
```javascript
// not-found.js - 404 page for route segments
export default function NotFound() {
  return (
    <div>
      <h2>Not Found</h2>
      <p>Could not find requested resource</p>
    </div>
  );
}
```

## Migration Checklist

### ✅ Completed:
- [x] Updated Next.js to latest version
- [x] Fixed async params in dynamic routes
- [x] Verified client/server component patterns
- [x] Updated dependencies
- [x] Tested build process

### 🔄 Recommended Future Updates:
- [ ] Add error.js files for better error handling
- [ ] Add loading.js files for better loading states
- [ ] Consider migrating to Server Components where possible
- [ ] Add not-found.js files for custom 404 pages

## Common Patterns

### Dynamic Route with Data Fetching:
```javascript
// app/posts/[id]/page.js
export default async function PostPage({ params }) {
  const { id } = await params;
  
  // Server-side data fetching
  const post = await fetch(`/api/posts/${id}`).then(r => r.json());
  
  return <div>{post.title}</div>;
}
```

### Client Component with Dynamic Route:
```javascript
// app/posts/[id]/edit/page.js
"use client";
import { use } from "react";

export default function EditPostPage({ params }) {
  const { id } = use(params);
  
  // Client-side hooks and state
  const [post, setPost] = useState(null);
  
  return <form>...</form>;
}
```

## Performance Optimizations

### 1. Static Generation:
```javascript
// Generate static params for dynamic routes
export async function generateStaticParams() {
  const posts = await fetch('/api/posts').then(r => r.json());
  return posts.map(post => ({ id: post.id }));
}
```

### 2. Metadata Generation:
```javascript
// Generate metadata for SEO
export async function generateMetadata({ params }) {
  const { id } = await params;
  const post = await fetch(`/api/posts/${id}`).then(r => r.json());
  
  return {
    title: post.title,
    description: post.excerpt,
  };
}
```

## Troubleshooting

### Common Errors:

1. **"params should be awaited"**
   - Solution: Use `await params` in server components or `use(params)` in client components

2. **"Cannot use hooks in server component"**
   - Solution: Add `"use client"` directive at the top of the file

3. **"Dynamic API not supported"**
   - Solution: Ensure you're using the correct async pattern for your component type

## Resources

- [Next.js 15 Documentation](https://nextjs.org/docs)
- [App Router Migration Guide](https://nextjs.org/docs/app/building-your-application/upgrading/app-router-migration)
- [React 19 Features](https://react.dev/blog/2024/04/25/react-19)

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";
import Link from "next/link";

export default function AuthCodeErrorPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 via-background to-accent/10 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo/Brand */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-primary mb-2">QueryCRM</h1>
          <p className="text-gray-600">Authentication Error</p>
        </div>

        {/* Error Card */}
        <Card className="shadow-lg border-0">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">
              Authentication Failed
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              There was an error during the authentication process.
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <p className="text-sm text-gray-600 mb-4">
                This could be due to:
              </p>
              <ul className="text-sm text-gray-600 text-left space-y-1 mb-6">
                <li>• Invalid or expired authentication code</li>
                <li>• Network connectivity issues</li>
                <li>• Cancelled authentication process</li>
              </ul>
            </div>

            <div className="space-y-3">
              <Link href="/login" className="block">
                <Button className="w-full bg-primary hover:bg-primary/90">
                  Try Again
                </Button>
              </Link>
              
              <Link href="/" className="block">
                <Button variant="outline" className="w-full">
                  Go to Dashboard
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>Need help? Contact <NAME_EMAIL></p>
        </div>
      </div>
    </div>
  );
}

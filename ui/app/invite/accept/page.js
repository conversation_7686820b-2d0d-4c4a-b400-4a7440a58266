"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MemberOnboardingForm } from "@/components/auth/MemberOnboardingForm";
import { 
  InvitationErrorPage,
  InvitationExpiredPage,
  InvitationAlreadyAcceptedPage,
  InvitationNotFoundPage,
  InvitationCancelledPage
} from "@/components/auth/InvitationErrorPage";
import { invitationAPI, INVITATION_ERRORS, getInvitationErrorMessage } from "@/lib/api/invitation";
import { Loader2, UserPlus } from "lucide-react";

function InvitationAcceptContent() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  
  const [loading, setLoading] = useState(true);
  const [invitation, setInvitation] = useState(null);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);

  const verifyInvitation = async (invitationToken) => {
    if (!invitationToken) {
      setError({
        type: 'not_found',
        message: 'No invitation token provided in the URL.',
        additionalInfo: 'Please check that you clicked the correct link from your invitation email.'
      });
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      console.log('[InvitationPage] Verifying invitation token...');
      const result = await invitationAPI.verifyInvitation(invitationToken);
      
      console.log('[InvitationPage] Verification result:', result);

      if (result.valid) {
        setInvitation(result.invitation);
        console.log('[InvitationPage] Invitation is valid:', result.invitation);
      } else {
        // Handle different error types
        const errorType = result.error || 'server_error';
        const errorMessage = getInvitationErrorMessage(errorType, result);
        
        let additionalInfo = null;
        if (result.expired_at) {
          additionalInfo = `Expired on: ${new Date(result.expired_at).toLocaleDateString()}`;
        } else if (result.accepted_at) {
          additionalInfo = `Accepted on: ${new Date(result.accepted_at).toLocaleDateString()}`;
        }

        setError({
          type: errorType,
          message: errorMessage,
          additionalInfo,
          status: result.status
        });
      }
    } catch (err) {
      console.error('[InvitationPage] Verification error:', err);
      setError({
        type: 'network_error',
        message: err.message || 'Failed to verify invitation. Please try again.',
        additionalInfo: 'Check your internet connection and try refreshing the page.'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    verifyInvitation(token);
  };

  useEffect(() => {
    verifyInvitation(token);
  }, [token, retryCount]);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-emerald-50 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary rounded-2xl mb-4">
            <Loader2 className="w-8 h-8 text-white animate-spin" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Verifying Invitation</h2>
          <p className="text-gray-600">Please wait while we verify your invitation...</p>
        </div>
      </div>
    );
  }

  // Error states
  if (error) {
    const { type, message, additionalInfo } = error;
    
    switch (type) {
      case INVITATION_ERRORS.INVITATION_EXPIRED:
        return <InvitationExpiredPage message={message} additionalInfo={additionalInfo} />;
      
      case INVITATION_ERRORS.INVITATION_ALREADY_ACCEPTED:
        return <InvitationAlreadyAcceptedPage message={message} additionalInfo={additionalInfo} />;
      
      case INVITATION_ERRORS.INVITATION_NOT_FOUND:
      case INVITATION_ERRORS.MISSING_TOKEN:
        return <InvitationNotFoundPage message={message} additionalInfo={additionalInfo} />;
      
      case INVITATION_ERRORS.INVITATION_CANCELLED:
        return <InvitationCancelledPage message={message} additionalInfo={additionalInfo} />;
      
      case INVITATION_ERRORS.RATE_LIMIT_EXCEEDED:
      case INVITATION_ERRORS.NETWORK_ERROR:
      case INVITATION_ERRORS.SERVER_ERROR:
      default:
        return (
          <InvitationErrorPage 
            errorType={type} 
            message={message} 
            additionalInfo={additionalInfo}
            onRetry={handleRetry}
          />
        );
    }
  }

  // Success state - show onboarding form
  if (invitation) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-emerald-50 flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          {/* Logo/Brand */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-primary rounded-2xl mb-4">
              <UserPlus className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome!</h1>
            <p className="text-gray-600">Complete your account setup to get started</p>
          </div>

          {/* Onboarding Card */}
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="text-center pb-6">
              <CardTitle className="text-2xl font-bold text-gray-900">
                Join Your Team
              </CardTitle>
              <p className="text-gray-600 mt-2">
                Set up your account to start collaborating
              </p>
            </CardHeader>
            <CardContent className="px-8 pb-8">
              <MemberOnboardingForm invitation={invitation} token={token} />
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="text-center mt-8 text-sm text-gray-500">
            <p>© 2025 QueryCRM. All rights reserved.</p>
          </div>
        </div>
      </div>
    );
  }

  // Fallback (shouldn't reach here)
  return (
    <InvitationErrorPage 
      errorType="server_error" 
      message="An unexpected error occurred" 
      onRetry={handleRetry}
    />
  );
}

export default function InvitationAcceptPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-emerald-50 flex items-center justify-center p-4">
        <div className="text-center">
          <Loader2 className="w-8 h-8 text-primary animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading invitation...</p>
        </div>
      </div>
    }>
      <InvitationAcceptContent />
    </Suspense>
  );
}

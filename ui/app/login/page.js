import { LoginForm } from "@/components/auth/LoginForm";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Inbox, Users, Zap, Shield, CheckCircle } from "lucide-react";

export default function LoginPage() {
  const features = [
    {
      icon: Zap,
      title: "Dedicated Form URL",
      description: "Get a unique form URL to collect enquiries from all your websites - no complex setup required.",
    },
    {
      icon: Shield,
      title: "AI-Powered Spam Filter",
      description: "Advanced spam detection automatically filters out unwanted enquiries.",
    },
    {
      icon: Inbox,
      title: "Centralized Inbox",
      description: "Collect all enquiries from website forms into one simple dashboard.",
    },
    {
      icon: Users,
      title: "Team Collaboration",
      description: "Invite team members and easily delegate follow-ups.",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-emerald-50 flex">
      {/* Left Side - Features */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primary/10 via-primary/5 to-accent/10 p-12 flex-col justify-center">
        <div className="max-w-lg">
          {/* Logo and Brand */}
          <div className="flex items-center mb-8">
            <img
              src="/logo.svg"
              alt="QueryCRM Logo"
              className="w-12 h-12 mr-3"
            />
            <h1 className="text-3xl font-bold text-gray-900">
              <span className="text-primary">Query</span>CRM
            </h1>
          </div>

          {/* Main Heading */}
          <h2 className="text-4xl font-bold text-gray-900 mb-6 leading-tight">
            Turn Customer Enquiries Into Opportunities
          </h2>

          <p className="text-xl text-gray-600 mb-8">
            A simple, centralized platform to track and manage all your customer enquiries efficiently.
          </p>

          {/* Features List */}
          <div className="space-y-6">
            {features.map((feature, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <feature.icon className="w-5 h-5 text-primary" />
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Trust Indicators */}
          <div className="mt-8 pt-8 border-t border-gray-200">
            <div className="flex items-center space-x-6 text-sm text-gray-500">
              <div className="flex items-center">
                <Shield className="w-4 h-4 mr-2" />
                Secure & Private
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2" />
                Easy Setup
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-4 lg:p-12">
        <div className="w-full max-w-md">
          {/* Mobile Logo (visible only on small screens) */}
          <div className="text-center mb-8 lg:hidden">
            <div className="inline-flex items-center justify-center mb-4">
              <img
                src="/logo.svg"
                alt="QueryCRM Logo"
                className="w-16 h-16"
              />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              <span className="text-primary">Query</span>CRM
            </h1>
          </div>

          {/* Login Card */}
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="text-center pb-6">
              <CardTitle className="text-2xl font-bold text-gray-900">
                Welcome Back
              </CardTitle>
              <p className="text-gray-600 mt-2">
                Sign in to manage your enquiries
              </p>
            </CardHeader>
            <CardContent className="px-8 pb-8">
              <LoginForm />
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="text-center mt-8 text-sm text-gray-500">
            <p>© 2025 QueryCRM. All rights reserved.</p>
          </div>
        </div>
      </div>
    </div>
  );
}

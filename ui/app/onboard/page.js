"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { onboardAPI } from '@/lib/api/onboard';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Building2, Users, CheckCircle, Globe } from 'lucide-react';

export default function OnboardPage() {
  const [organizationName, setOrganizationName] = useState('');
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState(null);
  const { basicUser, loading, fetchingUser } = useAuth();
  const router = useRouter();

  // Helper function to extract domain from URL
  const extractDomain = (url) => {
    if (!url) return '';

    try {
      // Add protocol if missing
      let fullUrl = url;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        fullUrl = 'https://' + url;
      }

      const urlObj = new URL(fullUrl);
      return urlObj.hostname;
    } catch (error) {
      // If URL parsing fails, try to extract domain manually
      const cleanUrl = url.replace(/^https?:\/\//, '').replace(/\/.*$/, '');
      return cleanUrl;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!organizationName.trim()) {
      setError('Organization name is required');
      return;
    }

    if (organizationName.length < 2 || organizationName.length > 100) {
      setError('Organization name must be between 2 and 100 characters');
      return;
    }

    if (!websiteUrl.trim()) {
      setError('Website URL is required');
      return;
    }

    // Validate website URL format
    const domain = extractDomain(websiteUrl.trim());
    if (!domain || domain.length < 3) {
      setError('Please enter a valid website URL (e.g., example.com or https://example.com)');
      return;
    }

    try {
      setIsSaving(true);
      setError(null);

      // Call onboarding API with domain
      const response = await onboardAPI.onboardUser(organizationName.trim(), domain);

      // Refresh the auth context to get updated user data
      // This will trigger a re-fetch of user data from /api/v1/users/me
      window.location.href = `/org/${response.organization.slug}`;

    } catch (error) {
      console.error('Onboarding error:', error);
      setError(error.message || 'Failed to complete onboarding. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  // Show loading if still loading auth state or fetching user data
  if (loading || fetchingUser || !basicUser) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
              <Building2 className="h-6 w-6 text-primary" />
            </div>
            <CardTitle className="text-2xl">Welcome to QueryCRM</CardTitle>
            <CardDescription>
              Let's set up your organization to get started
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* User Info Display */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                  <Users className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">{basicUser.user_metadata?.full_name || basicUser.email}</p>
                  <p className="text-sm text-gray-500">{basicUser.email}</p>
                </div>
              </div>
            </div>

            {/* Error Alert */}
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Onboarding Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="organizationName" className="text-sm font-medium">
                  Organization Name
                </Label>
                <Input
                  id="organizationName"
                  type="text"
                  placeholder="Enter your organization name"
                  value={organizationName}
                  onChange={(e) => setOrganizationName(e.target.value)}
                  disabled={isSaving}
                  className="h-12"
                  maxLength={100}
                />
                <p className="text-xs text-gray-500">
                  This will be used to create your organization workspace
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="websiteUrl" className="text-sm font-medium">
                  Website URL
                </Label>
                <div className="relative">
                  <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="websiteUrl"
                    type="text"
                    placeholder="example.com or https://example.com"
                    value={websiteUrl}
                    onChange={(e) => setWebsiteUrl(e.target.value)}
                    disabled={isSaving}
                    className="h-12 pl-10"
                  />
                </div>
                <p className="text-xs text-gray-500">
                  Your organization's website domain
                </p>
              </div>

              <Button
                type="submit"
                className="w-full h-12"
                disabled={isSaving || !organizationName.trim() || !websiteUrl.trim()}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Setting up your organization...
                  </>
                ) : (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Complete Setup
                  </>
                )}
              </Button>
            </form>

            {/* Benefits List */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <p className="text-sm font-medium text-gray-900 mb-3">What you'll get:</p>
              <ul className="space-y-2 text-sm text-gray-600">
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  Your own organization workspace
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  Admin access to manage your team
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  Full access to enquiry management
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  Up to 2 team members on starter plan
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

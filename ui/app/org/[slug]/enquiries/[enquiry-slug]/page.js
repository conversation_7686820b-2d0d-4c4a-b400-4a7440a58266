"use client";

import { use } from "react";
import { CRMLayout } from "@/components/layout/CRMLayout";
import { EnquiryDetails } from "@/components/enquiries/EnquiryDetails";
import { useEnquiryDetails } from "@/hooks/useEnquiryDetails";
import { Loader2 } from "lucide-react";
import { notFound } from "next/navigation";

export default function EnquiryDetailsPage({ params }) {
  const { "enquiry-slug": enquirySlug } = use(params);

  const {
    enquiry,
    notes,
    activities,
    loading,
    notesLoading,
    activitiesLoading,
    error,
    addNote,
    deleteNote,
    updateEnquiry,
    deleteEnquiry,
    refresh
  } = useEnquiryDetails(enquirySlug);

  // Loading state
  if (loading) {
    return (
      <CRMLayout title="Loading...">
        <div className="p-6">
          <div className="flex items-center justify-center min-h-96">
            <div className="flex items-center gap-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="text-gray-600">Loading enquiry details...</span>
            </div>
          </div>
        </div>
      </CRMLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <CRMLayout title="Error">
        <div className="p-6">
          <div className="text-center min-h-96 flex items-center justify-center">
            <div>
              <h2 className="text-xl font-semibold text-red-600 mb-2">Failed to load enquiry</h2>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                onClick={refresh}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </CRMLayout>
    );
  }

  // Not found state
  if (!enquiry) {
    notFound();
  }

  return (
    <CRMLayout title={`Enquiry - ${enquiry.name}`}>
      <EnquiryDetails
        enquiry={enquiry}
        notes={notes}
        activities={activities}
        notesLoading={notesLoading}
        activitiesLoading={activitiesLoading}
        onAddNote={addNote}
        onDeleteNote={deleteNote}
        onUpdateEnquiry={updateEnquiry}
        onDeleteEnquiry={deleteEnquiry}
        onRefresh={refresh}
      />
    </CRMLayout>
  );
}

"use client";

import { CRMLayout } from "@/components/layout/CRMLayout";
import { EnquiriesTable } from "@/components/enquiries/EnquiriesTable";
import { EnquiriesFilters } from "@/components/enquiries/EnquiriesFilters";
import { NewEnquiryModal } from "@/components/enquiries/NewEnquiryModal";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/lib/auth/AuthContext";
import { useEnquiries } from "@/hooks/useEnquiries";
import { useUnreadCounts } from "@/hooks/useUnreadCounts";
import { markEnquiryAsSpam, markEnquiryAsNotSpam } from "@/lib/api/spam";
import toast, { Toaster } from "react-hot-toast";
import { useState, useMemo } from "react";

export default function EnquiriesPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("genuine");

  // Use the enquiries hook for data management
  const {
    enquiries,
    loading,
    error,
    pagination,
    changePage,
    changePageSize,
    filters,
    updateFilters,
    clearFilters,
    refresh,
    deleteEnquiry
  } = useEnquiries();

  // Use the unread counts hook for tab badges
  const {
    counts: unreadCounts,
    loading: countsLoading,
    refresh: refreshCounts,
  } = useUnreadCounts();

  // Debug logging for unread counts
  console.log('[EnquiriesPage] Current unread counts:', unreadCounts);

  // Separate enquiries into genuine and spam based on is_spam field
  const { genuineEnquiries, spamEnquiries } = useMemo(() => {
    const genuine = enquiries?.filter(enquiry => !enquiry.is_spam) || [];
    const spam = enquiries?.filter(enquiry => enquiry.is_spam) || [];
    return { genuineEnquiries: genuine, spamEnquiries: spam };
  }, [enquiries]);

  // Check if current user can create enquiries (admin or manager only)
  const canCreateEnquiry = user?.role === 'admin' || user?.role === 'manager';

  console.log('[EnquiriesPage] User role check:', {
    hasUser: !!user,
    userId: user?.id,
    userRole: user?.role,
    canCreateEnquiry
  });

  console.log('[EnquiriesPage] Enquiries data:', {
    count: enquiries?.length || 0,
    loading,
    error,
    pagination
  });

  const handleEnquiryCreated = (newEnquiry) => {
    console.log('[EnquiriesPage] New enquiry created:', newEnquiry);
    toast.success(`Enquiry for ${newEnquiry.name} created successfully!`);
    // Refresh the enquiries list to show the new enquiry
    refresh();
  };

  const handleEnquiryUpdated = (updatedEnquiry) => {
    console.log('[EnquiriesPage] Enquiry updated:', updatedEnquiry);
    toast.success(`Enquiry for ${updatedEnquiry.name} updated successfully!`);
    // Refresh the enquiries list to show the updated enquiry
    refresh();
  };

  const handleEnquiryDeleted = (deletedEnquiryId) => {
    console.log('[EnquiriesPage] Enquiry deleted:', deletedEnquiryId);
    // Remove from local state (optimistic update)
    deleteEnquiry(deletedEnquiryId);
  };

  const handleUnauthorizedAccess = () => {
    toast.error('Only administrators and managers can create new enquiries');
  };

  // Handle spam management actions
  const handleMarkAsSpam = async (enquiryId) => {
    try {
      console.log(`Marking enquiry ${enquiryId} as spam`);

      // Call the API to mark as spam
      await markEnquiryAsSpam(enquiryId);

      // Show success message
      toast.success('Enquiry marked as spam');

      // Refresh the enquiries list and unread counts to reflect the change
      refresh();
      refreshCounts();
    } catch (error) {
      console.error('Failed to mark as spam:', error);
      toast.error(error.message || 'Failed to mark as spam');
    }
  };

  const handleMarkAsNotSpam = async (enquiryId) => {
    try {
      console.log(`Marking enquiry ${enquiryId} as not spam`);

      // Call the API to mark as not spam
      await markEnquiryAsNotSpam(enquiryId);

      // Show success message
      toast.success('Enquiry marked as genuine');

      // Refresh the enquiries list and unread counts to reflect the change
      refresh();
      refreshCounts();
    } catch (error) {
      console.error('Failed to mark as not spam:', error);
      toast.error(error.message || 'Failed to mark as genuine');
    }
  };

  // Handle read/unread management actions
  const handleMarkAsRead = async (enquiryId) => {
    try {
      console.log(`Marking enquiry ${enquiryId} as read`);

      // Find the enquiry and update it locally first for immediate feedback
      const enquiry = enquiries.find(e => e.id === enquiryId);
      if (enquiry) {
        enquiry.is_read = true;
      }

      // Call the API to mark as read
      const { enquiryAPI } = await import('@/lib/api/enquiries');
      await enquiryAPI.markAsRead(enquiryId);
      // Refresh the enquiries list and unread counts to reflect the change
      refresh();
      refreshCounts();
    } catch (error) {
      console.error('Failed to mark as read:', error);
    }
  };

  const handleMarkAsUnread = async (enquiryId) => {
    try {
      console.log(`Marking enquiry ${enquiryId} as unread`);

      // Find the enquiry and update it locally first for immediate feedback
      const enquiry = enquiries.find(e => e.id === enquiryId);
      if (enquiry) {
        enquiry.is_read = false;
      }

      // Call the API to mark as unread
      const { enquiryAPI } = await import('@/lib/api/enquiries');
      await enquiryAPI.markAsUnread(enquiryId);

      // Refresh the enquiries list and unread counts to reflect the change
      refresh();
      refreshCounts();
    } catch (error) {
      console.error('Failed to mark as unread:', error);
    }
  };

  return (
    <CRMLayout title="Enquiries">
      <Toaster position="top-right" />
      <div className="p-6">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <p className="text-sm text-gray-600">Manage and track all customer enquiries</p>
          </div>
          {canCreateEnquiry ? (
            <NewEnquiryModal onEnquiryCreated={handleEnquiryCreated} />
          ) : (
            <div className="relative">
              <button
                onClick={handleUnauthorizedAccess}
                className="bg-gray-300 text-gray-500 cursor-not-allowed px-4 py-2 rounded-md flex items-center gap-2"
                disabled
              >
                <span className="text-sm">New Enquiry</span>
              </button>
              <span className="absolute -top-8 right-0 text-xs text-gray-500 whitespace-nowrap">
                Admin/Manager only
              </span>
            </div>
          )}
        </div>

        {/* Tabs for Genuine vs Spam Enquiries */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="genuine" className="flex items-center gap-2">
              Inbox
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                {unreadCounts.unread_genuine || 0}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="spam" className="flex items-center gap-2">
              Spam
              <Badge variant="secondary" className="bg-red-100 text-red-800">
                {unreadCounts.unread_spam || 0}
              </Badge>
            </TabsTrigger>
          </TabsList>

          {/* Genuine Enquiries Tab */}
          <TabsContent value="genuine">
            <div className="space-y-4">
              {/* Filters for Genuine Enquiries */}
              <EnquiriesFilters
                filters={filters}
                onFiltersChange={updateFilters}
                onClearFilters={clearFilters}
              />

              {/* Genuine Enquiries Table */}
              <EnquiriesTable
                enquiries={genuineEnquiries}
                loading={loading}
                error={error}
                pagination={pagination}
                onPageChange={changePage}
                onPageSizeChange={changePageSize}
                onEnquiryUpdated={handleEnquiryUpdated}
                onEnquiryDeleted={handleEnquiryDeleted}
                onMarkAsSpam={handleMarkAsSpam}
                onMarkAsRead={handleMarkAsRead}
                onMarkAsUnread={handleMarkAsUnread}
                showSpamActions={true}
                tabType="genuine"
              />
            </div>
          </TabsContent>

          {/* Spam Enquiries Tab */}
          <TabsContent value="spam">
            <div className="space-y-4">
              {/* Spam Enquiries Table */}
              <EnquiriesTable
                enquiries={spamEnquiries}
                loading={loading}
                error={error}
                pagination={pagination}
                onPageChange={changePage}
                onPageSizeChange={changePageSize}
                onEnquiryUpdated={handleEnquiryUpdated}
                onEnquiryDeleted={handleEnquiryDeleted}
                onMarkAsNotSpam={handleMarkAsNotSpam}
                onMarkAsRead={handleMarkAsRead}
                onMarkAsUnread={handleMarkAsUnread}
                showSpamActions={true}
                tabType="spam"
              />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </CRMLayout>
  );
}

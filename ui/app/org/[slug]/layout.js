"use client";

import { use } from 'react';
import { OrganizationProvider, useOrganization } from '@/lib/context/OrganizationContext';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Loader2, AlertCircle } from 'lucide-react';

// Organization validation component
function OrganizationValidator({ children }) {
  const { loading, error, organization } = useOrganization();

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-gray-600">Loading organization...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <a 
            href="/login" 
            className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
          >
            Return to Login
          </a>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-yellow-500 mx-auto mb-4" />
          <p className="text-gray-600">Organization not found</p>
        </div>
      </div>
    );
  }

  return children;
}

export default function OrganizationLayout({ children, params }) {
  const { slug } = use(params);

  return (
    <ProtectedRoute>
      <OrganizationProvider orgSlug={slug}>
        <OrganizationValidator>
          {children}
        </OrganizationValidator>
      </OrganizationProvider>
    </ProtectedRoute>
  );
}

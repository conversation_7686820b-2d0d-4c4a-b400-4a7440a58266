"use client";

import { useEffect } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

export default function Home() {
  const { basicUser, completeUser, loading, fetchingUser, authError } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (loading || fetchingUser) return;

    // If no Supabase user, redirect to login
    if (!basicUser) {
      router.replace('/login');
      return;
    }

    // If there's an authentication error (401), redirect to login
    if (authError === 'unauthorized') {
      router.replace('/login');
      return;
    }

    // If we have a Supabase user but no complete user data from our backend,
    // check the error type to determine where to redirect
    if (basicUser && !completeUser) {
      if (authError === 'not_found') {
        // User not found in backend (404), needs onboarding
        router.replace('/onboard');
      } else if (authError === 'other' || authError === null) {
        // Other errors or no specific error, assume needs onboarding
        router.replace('/onboard');
      }
      return;
    }

    // If we have complete user data, check for organization
    if (completeUser && completeUser.org_slug) {
      // Redirect to user's organization dashboard
      router.replace(`/org/${completeUser.org_slug}`);
    } else if (completeUser) {
      // User exists in our database but has no organization (shouldn't happen with new onboarding)
      router.replace('/login');
    }
  }, [basicUser, completeUser, loading, authError, router]);

  // Show loading while redirecting
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
        <p className="text-gray-600">Redirecting...</p>
      </div>
    </div>
  );
}

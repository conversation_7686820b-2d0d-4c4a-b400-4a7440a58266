import { SignupForm } from "@/components/auth/SignupForm";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Inbox, Users, Zap, Bell, Shield, CheckCircle, ArrowRight } from "lucide-react";

export default function SignupPage() {
  const features = [
    {
      icon: Zap,
      title: "Dedicated Form URL",
      description: "Get a unique form URL to collect enquiries from all your websites - no complex setup required.",
      highlight: true,
    },
    {
      icon: Shield,
      title: "AI-Powered Spam Filter",
      description: "Advanced GPT-4 based spam detection automatically filters out unwanted enquiries.",
      highlight: true,
    },
    {
      icon: Inbox,
      title: "Multi-Website Collection",
      description: "Centralize enquiries from all your websites in one organized dashboard.",
    },
    {
      icon: Users,
      title: "Team Management",
      description: "Assign roles, delegate follow-ups, and collaborate seamlessly.",
    },
  ];

  const benefits = [
    "Collect from multiple websites with one URL",
    "AI automatically blocks spam enquiries",
    "No technical setup or coding required",
    "Secure & compliant data management"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-emerald-50 flex">
      {/* Left Side - Features */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primary/10 via-primary/5 to-accent/10 p-12 flex-col justify-center">
        <div className="max-w-lg">
          {/* Logo and Brand */}
          <div className="flex items-center mb-8">
            <img
              src="/logo.svg"
              alt="QueryCRM Logo"
              className="w-12 h-12 mr-3"
            />
            <h1 className="text-3xl font-bold text-gray-900">
              <span className="text-primary">Query</span>CRM
            </h1>
          </div>

          {/* Main Heading */}
          <h2 className="text-4xl font-bold text-gray-900 mb-6 leading-tight">
            One URL. Multiple Websites. Zero Spam.
          </h2>

          <p className="text-xl text-gray-600 mb-8">
            Get a dedicated form URL and let AI handle spam filtering while you focus on real enquiries.
          </p>

          {/* Benefits List */}
          <div className="space-y-4 mb-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-accent flex-shrink-0" />
                <span className="text-gray-700 font-medium">{benefit}</span>
              </div>
            ))}
          </div>

          {/* Features List */}
          <div className="space-y-6">
            {features.map((feature, index) => (
              <div
                key={index}
                className={`flex items-start space-x-4 ${
                  feature.highlight
                    ? 'bg-gradient-to-r from-primary/5 to-accent/5 p-4 rounded-lg border border-primary/20'
                    : ''
                }`}
              >
                <div className="flex-shrink-0">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                    feature.highlight
                      ? 'bg-gradient-to-r from-primary to-accent text-white'
                      : 'bg-primary/10'
                  }`}>
                    <feature.icon className={`w-5 h-5 ${
                      feature.highlight ? 'text-white' : 'text-primary'
                    }`} />
                  </div>
                </div>
                <div>
                  <h3 className={`font-semibold mb-1 ${
                    feature.highlight ? 'text-gray-900' : 'text-gray-900'
                  }`}>
                    {feature.title}
                    {feature.highlight && (
                      <span className="ml-2 text-xs bg-accent text-white px-2 py-1 rounded-full">
                        OUT OF THE BOX
                      </span>
                    )}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* CTA */}
          <div className="mt-8 pt-8 border-t border-gray-200">
            <div className="flex items-center text-sm text-gray-600">
              <ArrowRight className="w-4 h-4 mr-2 text-primary" />
              Get started in under 2 minutes
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Signup Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-4 lg:p-12">
        <div className="w-full max-w-md">
          {/* Mobile Logo (visible only on small screens) */}
          <div className="text-center mb-8 lg:hidden">
            <div className="inline-flex items-center justify-center mb-4">
              <img
                src="/logo.svg"
                alt="QueryCRM Logo"
                className="w-16 h-16"
              />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              <span className="text-primary">Query</span>CRM
            </h1>
          </div>

          {/* Signup Card */}
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="text-center pb-6">
              <CardTitle className="text-2xl font-bold text-gray-900">
                Create Account
              </CardTitle>
              <p className="text-gray-600 mt-2">
                Sign up to start managing your enquiries
              </p>
            </CardHeader>
            <CardContent className="px-8 pb-8">
              <SignupForm />
            </CardContent>
          </Card>

          {/* Footer */}
          <div className="text-center mt-8 text-sm text-gray-500">
            <p>© 2025 QueryCRM. All rights reserved.</p>
          </div>
        </div>
      </div>
    </div>
  );
}

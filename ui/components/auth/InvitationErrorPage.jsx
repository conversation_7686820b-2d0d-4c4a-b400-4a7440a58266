"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  ArrowLeft,
  Mail
} from "lucide-react";
import Link from "next/link";

const ERROR_CONFIGS = {
  expired: {
    icon: Clock,
    iconColor: "text-orange-600",
    bgColor: "bg-orange-100",
    title: "Invitation Expired",
    description: "This invitation link has expired and is no longer valid.",
    showContactAdmin: true,
    showRetry: false
  },
  already_accepted: {
    icon: CheckCircle,
    iconColor: "text-green-600",
    bgColor: "bg-green-100",
    title: "Already Accepted",
    description: "This invitation has already been accepted.",
    showContactAdmin: false,
    showRetry: false,
    showLoginLink: true
  },
  cancelled: {
    icon: XCircle,
    iconColor: "text-red-600",
    bgColor: "bg-red-100",
    title: "Invitation Cancelled",
    description: "This invitation has been cancelled by your administrator.",
    showContactAdmin: true,
    showRetry: false
  },
  not_found: {
    icon: AlertTriangle,
    iconColor: "text-red-600",
    bgColor: "bg-red-100",
    title: "Invalid Invitation",
    description: "This invitation link is invalid or does not exist.",
    showContactAdmin: true,
    showRetry: false
  },
  rate_limited: {
    icon: RefreshCw,
    iconColor: "text-blue-600",
    bgColor: "bg-blue-100",
    title: "Too Many Requests",
    description: "Please wait a moment before trying again.",
    showContactAdmin: false,
    showRetry: true
  },
  server_error: {
    icon: AlertTriangle,
    iconColor: "text-red-600",
    bgColor: "bg-red-100",
    title: "Server Error",
    description: "A server error occurred while processing your invitation.",
    showContactAdmin: false,
    showRetry: true
  },
  network_error: {
    icon: AlertTriangle,
    iconColor: "text-red-600",
    bgColor: "bg-red-100",
    title: "Connection Error",
    description: "Unable to connect to the server. Please check your internet connection.",
    showContactAdmin: false,
    showRetry: true
  }
};

export function InvitationErrorPage({ 
  errorType = 'server_error', 
  message, 
  onRetry,
  additionalInfo 
}) {
  const config = ERROR_CONFIGS[errorType] || ERROR_CONFIGS.server_error;
  const IconComponent = config.icon;

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-emerald-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo/Brand */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-primary rounded-2xl mb-4">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">QueryCRM</h1>
          <p className="text-gray-600">Team Member Invitation</p>
        </div>

        {/* Error Card */}
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="text-center pb-4">
            <div className={`mx-auto w-12 h-12 ${config.bgColor} rounded-full flex items-center justify-center mb-4`}>
              <IconComponent className={`h-6 w-6 ${config.iconColor}`} />
            </div>
            <CardTitle className="text-xl font-semibold text-gray-900">
              {config.title}
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              {message || config.description}
            </p>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* Additional Info */}
            {additionalInfo && (
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-sm text-gray-600">
                  {additionalInfo}
                </p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-3">
              {/* Retry Button */}
              {config.showRetry && onRetry && (
                <Button 
                  onClick={onRetry}
                  className="w-full bg-primary hover:bg-primary/90"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              )}

              {/* Login Link (for already accepted) */}
              {config.showLoginLink && (
                <Link href="/login" className="block">
                  <Button className="w-full bg-primary hover:bg-primary/90">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Go to Sign In
                  </Button>
                </Link>
              )}

              {/* Contact Admin */}
              {config.showContactAdmin && (
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                  <div className="flex items-start gap-3">
                    <Mail className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-900 mb-1">Need Help?</h4>
                      <p className="text-sm text-blue-700 mb-2">
                        Contact your administrator to request a new invitation or resolve this issue.
                      </p>
                      <p className="text-xs text-blue-600">
                        They can resend your invitation or check your account status.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Back to Home */}
              <Link href="/" className="block">
                <Button variant="outline" className="w-full">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Home
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-gray-500">
          <p>© 2025 QueryCRM. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
}

// Specific error page components for common cases
export function InvitationExpiredPage({ message, additionalInfo }) {
  return (
    <InvitationErrorPage 
      errorType="expired" 
      message={message}
      additionalInfo={additionalInfo}
    />
  );
}

export function InvitationAlreadyAcceptedPage({ message, additionalInfo }) {
  return (
    <InvitationErrorPage 
      errorType="already_accepted" 
      message={message}
      additionalInfo={additionalInfo}
    />
  );
}

export function InvitationNotFoundPage({ message, additionalInfo }) {
  return (
    <InvitationErrorPage 
      errorType="not_found" 
      message={message}
      additionalInfo={additionalInfo}
    />
  );
}

export function InvitationCancelledPage({ message, additionalInfo }) {
  return (
    <InvitationErrorPage 
      errorType="cancelled" 
      message={message}
      additionalInfo={additionalInfo}
    />
  );
}

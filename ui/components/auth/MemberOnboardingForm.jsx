"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/lib/auth/AuthContext";
import { invitationAPI } from "@/lib/api/invitation";
import { Loader2, Mail, Lock, Eye, EyeOff, User, Building2, Shield } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

export function MemberOnboardingForm({ invitation, token }) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [authMethod, setAuthMethod] = useState('email'); // 'email' or 'google'
  const [showPassword, setShowPassword] = useState(false);
  const [authStep, setAuthStep] = useState('form'); // 'form', 'authenticating', 'onboarding'
  const [formData, setFormData] = useState({
    name: '',
    password: ''
  });

  const { signInWithGoogle, signUpWithEmail, supabase, accessToken } = useAuth();
  const router = useRouter();

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleGoogleSignup = async () => {
    if (!formData.name.trim()) {
      setError('Please enter your full name before continuing with Google signup');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setAuthMethod('google');
      setAuthStep('authenticating');

      console.log('[MemberOnboarding] Starting Google authentication...');

      // First complete Google OAuth with Supabase
      await signInWithGoogle();

      // The auth state change will trigger the onboarding process
      // via the useEffect below

    } catch (error) {
      console.error('Google signup error:', error);
      setError(error.message || 'An error occurred during Google signup');
      setLoading(false);
      setAuthStep('form');
    }
  };

  const handleEmailSignup = async (e) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      setError('Please enter your full name');
      return;
    }

    if (!formData.password || formData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setAuthMethod('email');
      setAuthStep('authenticating');

      console.log('[MemberOnboarding] Starting email signup with Supabase...');

      // First create Supabase account
      await signUpWithEmail(invitation.email, formData.password);

      // The auth state change will trigger the onboarding process
      // via the useEffect below

    } catch (error) {
      console.error('Email signup error:', error);
      setError(error.message || 'An error occurred during account creation');
      setLoading(false);
      setAuthStep('form');
    }
  };

  // Handle onboarding after Supabase authentication
  const handleOnboarding = async (supabaseToken) => {
    try {
      setAuthStep('onboarding');
      console.log('[MemberOnboarding] Starting onboarding process...');

      // Call the new onboard endpoint
      const result = await invitationAPI.onboardInvitedUser({
        invitation_token: token,
        supabase_token: supabaseToken,
        name: formData.name.trim()
      });

      console.log('[MemberOnboarding] Onboarding successful:', result);

      // Redirect to organization dashboard
      router.push(result.redirect_url || `/org/${result.organization.slug}`);

    } catch (error) {
      console.error('Onboarding error:', error);
      setError(error.message || 'An error occurred during account setup');
      setLoading(false);
      setAuthStep('form');
    }
  };

  // Watch for Supabase authentication completion
  useEffect(() => {
    if (accessToken && authStep === 'authenticating' && formData.name.trim()) {
      console.log('[MemberOnboarding] Supabase auth completed, starting onboarding...');
      handleOnboarding(accessToken);
    }
  }, [accessToken, authStep, formData.name, token]);

  return (
    <div className="space-y-6">
      {/* Organization & Role Info */}
      <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-lg p-4 border">
        <div className="flex items-center gap-3 mb-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Building2 className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{invitation.organization_name}</h3>
            <p className="text-sm text-gray-600">You're being invited to join this organization</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Shield className="h-4 w-4 text-gray-500" />
          <span className="text-sm text-gray-600">Role:</span>
          <Badge variant="secondary" className="capitalize">
            {invitation.role}
          </Badge>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Name Field (Always Required) */}
      <div className="space-y-2">
        <Label htmlFor="name" className="text-sm font-medium text-gray-700">
          Full Name *
        </Label>
        <div className="relative">
          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            id="name"
            name="name"
            type="text"
            placeholder="Enter your full name"
            value={formData.name}
            onChange={handleInputChange}
            className="pl-10 h-12"
            disabled={loading}
          />
        </div>
      </div>

      {/* Email Field (Read-only) */}
      <div className="space-y-2">
        <Label htmlFor="email" className="text-sm font-medium text-gray-700">
          Email Address
        </Label>
        <div className="relative">
          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            id="email"
            type="email"
            value={invitation.email}
            className="pl-10 h-12 bg-gray-50"
            disabled
            readOnly
          />
        </div>
        <p className="text-xs text-gray-500">This email address is pre-filled from your invitation</p>
      </div>

      {/* Auth Method Selection */}
      <div className="space-y-4">
        <div className="text-center">
          <p className="text-sm font-medium text-gray-700 mb-3">Choose how you'd like to sign up:</p>
        </div>

        {/* Google Signup Button */}
        <Button
          type="button"
          onClick={handleGoogleSignup}
          disabled={loading}
          className="w-full h-12 bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 font-medium"
        >
          {loading && authMethod === 'google' ? (
            <Loader2 className="h-5 w-5 animate-spin mr-2" />
          ) : (
            <svg className="h-5 w-5 mr-2" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
          )}
          {loading && authMethod === 'google' ?
            (authStep === 'authenticating' ? 'Authenticating...' :
             authStep === 'onboarding' ? 'Setting up account...' : 'Processing...') :
            'Continue with Google'}
        </Button>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Or sign up with email</span>
          </div>
        </div>

        {/* Email/Password Form */}
        <form onSubmit={handleEmailSignup} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm font-medium text-gray-700">
              Password *
            </Label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="password"
                name="password"
                type={showPassword ? "text" : "password"}
                placeholder="Create a password (min. 8 characters)"
                value={formData.password}
                onChange={handleInputChange}
                className="pl-10 pr-10 h-12"
                disabled={loading}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                disabled={loading}
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>

          <Button
            type="submit"
            disabled={loading}
            className="w-full h-12 bg-primary hover:bg-primary/90 text-white font-medium"
          >
            {loading && authMethod === 'email' ? (
              <Loader2 className="h-5 w-5 animate-spin mr-2" />
            ) : null}
            {loading && authMethod === 'email' ?
              (authStep === 'authenticating' ? 'Creating account...' :
               authStep === 'onboarding' ? 'Setting up account...' : 'Processing...') :
              'Create Account'}
          </Button>
        </form>
      </div>

      {/* Terms Notice */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          By creating an account, you agree to our Terms of Service and Privacy Policy
        </p>
      </div>
    </div>
  );
}

"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { Loader2 } from "lucide-react";

export function ProtectedRoute({ children }) {
  const { user, loading, fetchingUser, authError } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (loading || fetchingUser) return;

    // If there's an authentication error, redirect to login
    if (authError === 'unauthorized') {
      router.push('/login');
      return;
    }

    if (!user) {
      router.push('/login');
    }
  }, [user, loading, fetchingUser, authError, router]);

  if (loading || fetchingUser) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return children;
}

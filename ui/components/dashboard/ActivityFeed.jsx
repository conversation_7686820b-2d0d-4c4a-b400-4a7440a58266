import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Loader2, User, Mail, ExternalLink, RefreshCw } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import Link from "next/link";
import { useInfiniteActivities } from "@/hooks/useInfiniteActivities";
import { useEffect, useRef, useCallback } from "react";

const formatActivityTimestamp = (timestamp) => {
  try {
    const date = new Date(timestamp);
    return formatDistanceToNow(date, { addSuffix: true });
  } catch {
    return timestamp; // Fallback to original string if parsing fails
  }
};

const getStatusVariant = (status) => {
  switch (status) {
    case "New":
      return "bg-accent text-white";
    case "In Progress":
      return "bg-blue-100 text-blue-800";
    case "Closed":
      return "bg-gray-100 text-gray-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getPriorityVariant = (priority) => {
  switch (priority) {
    case "High":
      return "bg-red-100 text-red-800";
    case "Medium":
      return "bg-yellow-100 text-yellow-800";
    case "Low":
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getActivityIcon = (type) => {
  switch (type) {
    case "Enquiry Created":
      return "bg-green-500";
    case "Status Changed":
      return "bg-blue-500";
    case "Priority Changed":
      return "bg-yellow-500";
    case "Assignment Changed":
      return "bg-purple-500";
    case "Note Added":
      return "bg-indigo-500";
    case "Note Deleted":
      return "bg-red-500";
    default:
      return "bg-gray-500";
  }
};

const parseMetadata = (metadataString) => {
  if (!metadataString) return null;
  try {
    return JSON.parse(metadataString);
  } catch {
    return null;
  }
};

const renderMetadataDetails = (metadata, type) => {
  if (!metadata) return null;

  switch (type) {
    case "Status Changed":
      if (metadata.old_status && metadata.new_status) {
        return (
          <div className="text-xs text-gray-600 mt-1">
            <span className="font-medium">Changed from:</span> {metadata.old_status} → {metadata.new_status}
          </div>
        );
      }
      break;
    case "Priority Changed":
      if (metadata.old_priority && metadata.new_priority) {
        return (
          <div className="text-xs text-gray-600 mt-1">
            <span className="font-medium">Changed from:</span> {metadata.old_priority} → {metadata.new_priority}
          </div>
        );
      }
      break;
    case "Assignment Changed":
      if (metadata.old_assignee || metadata.new_assignee) {
        return (
          <div className="text-xs text-gray-600 mt-1">
            <span className="font-medium">Assignment:</span> {metadata.old_assignee || "Unassigned"} → {metadata.new_assignee || "Unassigned"}
          </div>
        );
      }
      break;
    case "Note Added":
      if (metadata.note_content) {
        return (
          <div className="text-xs text-gray-600 mt-1 p-2 bg-gray-100 rounded">
            <span className="font-medium">Note:</span> {metadata.note_content.length > 100 ? metadata.note_content.substring(0, 100) + "..." : metadata.note_content}
          </div>
        );
      }
      break;
  }
  return null;
};

export function ActivityFeed() {
  const {
    activities,
    loading,
    loadingMore,
    error,
    hasMore,
    loadMoreActivities,
    refreshActivities,
  } = useInfiniteActivities();

  // Ref for the scroll container
  const scrollContainerRef = useRef(null);
  const loadMoreRef = useRef(null);

  // Intersection Observer for infinite scroll
  const handleIntersection = useCallback((entries) => {
    const [entry] = entries;
    if (entry.isIntersecting && hasMore && !loadingMore && !loading) {
      loadMoreActivities();
    }
  }, [hasMore, loadingMore, loading, loadMoreActivities]);

  useEffect(() => {
    const observer = new IntersectionObserver(handleIntersection, {
      threshold: 0.1,
      rootMargin: '100px',
    });

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [handleIntersection]);
  if (loading) {
    return (
      <Card className="h-[600px] flex flex-col">
        <CardHeader className="flex-shrink-0">
          <CardTitle className="text-xl font-semibold text-gray-900">
            Activity Feed
          </CardTitle>
          <p className="text-sm text-gray-600">Recent updates across all enquiries</p>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="flex items-center gap-2">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span className="text-gray-600">Loading activity feed...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="h-[600px] flex flex-col">
        <CardHeader className="flex-shrink-0">
          <CardTitle className="text-xl font-semibold text-gray-900">
            Activity Feed
          </CardTitle>
          <p className="text-sm text-gray-600">Recent updates across all enquiries</p>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <p className="text-red-600">Failed to load activity feed</p>
            <p className="text-red-500 text-sm mt-1">{error}</p>
            <Button
              onClick={refreshActivities}
              variant="outline"
              className="mt-3"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (activities.length === 0) {
    return (
      <Card className="h-[600px] flex flex-col">
        <CardHeader className="flex-shrink-0">
          <CardTitle className="text-xl font-semibold text-gray-900">
            Activity Feed
          </CardTitle>
          <p className="text-sm text-gray-600">Recent updates across all enquiries</p>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <p className="text-gray-600">No recent activity</p>
            <p className="text-gray-500 text-sm mt-1">Activity will appear here when enquiries are updated</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-[600px] flex flex-col">
      <CardHeader className="flex-shrink-0">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl font-semibold text-gray-900">
              Activity Feed
            </CardTitle>
            <p className="text-sm text-gray-600">Recent updates across all enquiries</p>
          </div>
          <Button
            onClick={refreshActivities}
            variant="outline"
            size="sm"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="flex-1 overflow-hidden">
        <div
          ref={scrollContainerRef}
          className="h-full overflow-auto space-y-4 pr-2"
        >
          {activities.map((activity) => (
            <div key={activity.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
              <div className="flex items-start gap-4">
                {/* Activity indicator with colored dot based on activity type */}
                <div className={`w-3 h-3 rounded-full mt-1 flex-shrink-0 ${getActivityIcon(activity.type)}`}></div>

                {/* Activity content */}
                <div className="flex-1 min-w-0">
                  {/* Activity description */}
                  <p className="text-sm text-gray-900 font-medium leading-relaxed mb-2">
                    {activity.description}
                  </p>

                  {/* Metadata details */}
                  {renderMetadataDetails(parseMetadata(activity.metadata), activity.type)}

                  {/* Enquiry details section */}
                  {activity.enquiry_email && activity.enquiry_name !== "Unknown Enquiry" && activity.enquiry_email !== "" && (
                    <div className="bg-gray-50 rounded-lg p-3 mb-3">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          {activity.enquiry_slug ? (
                            <Link
                              href={`/enquiries/${activity.enquiry_slug}`}
                              className="font-semibold text-primary text-sm hover:text-primary/80 transition-colors hover:underline"
                            >
                              {activity.enquiry_email}
                            </Link>
                          ) : (
                            <span className="font-semibold text-gray-900 text-sm">
                              {activity.enquiry_email}
                            </span>
                          )}
                          {activity.enquiry_slug && (
                            <Link
                              href={`/enquiries/${activity.enquiry_slug}`}
                              className="text-gray-400 hover:text-primary transition-colors"
                              title="View enquiry details"
                            >
                              <ExternalLink className="h-3 w-3" />
                            </Link>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          {activity.enquiry_status && (
                            <Badge className={`text-xs ${getStatusVariant(activity.enquiry_status)}`}>
                              {activity.enquiry_status}
                            </Badge>
                          )}
                          {activity.enquiry_priority && (
                            <Badge className={`text-xs ${getPriorityVariant(activity.enquiry_priority)}`}>
                              {activity.enquiry_priority}
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Source, Form Type, and Name */}
                      <div className="grid grid-cols-2 gap-2 text-xs text-gray-600 mb-2">
                        <div className="space-y-1">
                          {activity.enquiry_source && (
                            <div className="flex items-center gap-1">
                              <span className="font-semibold">Source:</span>
                              <span className="truncate">{activity.enquiry_source}</span>
                            </div>
                          )}
                          {activity.enquiry_form_type && (
                            <div className="flex items-center gap-1">
                              <span className="font-semibold">Form:</span>
                              <span className="truncate text-xs bg-gray-100 text-gray-600 px-1.5 py-0.5 rounded">
                                {activity.enquiry_form_type}
                              </span>
                            </div>
                          )}
                        </div>
                        {activity.enquiry_name && activity.enquiry_name !== "Guest" && (
                          <div className="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            <span className="truncate">{activity.enquiry_name}</span>
                          </div>
                        )}
                      </div>

                      {/* Message excerpt */}
                      {activity.enquiry_message && (
                        <div className="text-xs text-gray-700 bg-white rounded p-2 border-l-2 border-gray-300">
                          <span className="font-medium text-gray-600">Message: </span>
                          <span className="italic">
                            {activity.enquiry_message.length > 120
                              ? activity.enquiry_message.substring(0, 120) + "..."
                              : activity.enquiry_message
                            }
                          </span>
                        </div>
                      )}
                    </div>
                  )}

                  {/* User and timestamp info */}
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center gap-2">
                      <User className="h-3 w-3" />
                      <span>
                        <span className="font-medium">{activity.user_name}</span>
                        {activity.user_role && activity.user_role !== "system" && (
                          <span className="text-gray-400"> ({activity.user_role})</span>
                        )}
                      </span>
                    </div>
                    <span className="text-gray-400">{formatActivityTimestamp(activity.timestamp)}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {/* Infinite scroll trigger */}
          {hasMore && (
            <div ref={loadMoreRef} className="py-4">
              {loadingMore ? (
                <div className="flex items-center justify-center">
                  <Loader2 className="h-5 w-5 animate-spin mr-2" />
                  <span className="text-gray-600">Loading more activities...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  <Button
                    onClick={loadMoreActivities}
                    variant="outline"
                    size="sm"
                    disabled={loadingMore}
                  >
                    Load More
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* End of activities indicator */}
          {!hasMore && activities.length > 0 && (
            <div className="py-4 text-center">
              <p className="text-gray-500 text-sm">You've reached the end of the activity feed</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

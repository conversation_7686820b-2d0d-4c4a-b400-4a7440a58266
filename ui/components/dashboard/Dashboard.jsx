"use client";

import { useState, useEffect } from "react";
import { CRMLayout } from "@/components/layout/CRMLayout";
import { StatsCards } from "./StatsCards";
import { ActivityFeed } from "./ActivityFeed";
import { WelcomeBanner } from "./WelcomeBanner";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { useDashboard } from "@/hooks/useDashboard";
import toast, { Toaster } from "react-hot-toast";

export function Dashboard() {
  const {
    stats,
    recentActivities,
    loading,
    error,
    refresh
  } = useDashboard();

  const [showWelcomeBanner, setShowWelcomeBanner] = useState(true);

  // Check if banner was previously dismissed
  useEffect(() => {
    const dismissed = localStorage.getItem('welcomeBannerDismissed');
    if (dismissed === 'true') {
      setShowWelcomeBanner(false);
    }
  }, []);

  const handleRefresh = () => {
    refresh();
    toast.success('Dashboard refreshed');
  };

  const handleBannerDismiss = () => {
    setShowWelcomeBanner(false);
  };

  return (
    <CRMLayout title="Dashboard">
      <Toaster position="top-right" />
      <div className="p-6">
        {/* Header with refresh button */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <p className="text-sm text-gray-600">Overview and activity feed for your organization</p>
          </div>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Welcome Banner for new organizations */}
        {showWelcomeBanner && (
          <WelcomeBanner
            stats={stats}
            onDismiss={handleBannerDismiss}
          />
        )}

        {/* Stats Cards */}
        <StatsCards
          stats={stats}
          loading={loading}
          error={error}
        />

        {/* Activity Feed Section */}
        <div className="mt-6">
          <ActivityFeed />
        </div>
      </div>
    </CRMLayout>
  );
}

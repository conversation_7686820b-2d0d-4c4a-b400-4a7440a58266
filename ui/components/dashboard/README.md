# Dashboard Components

This directory contains all the components for the QueryCRM.

## Components

### Dashboard.jsx
Main dashboard layout component that orchestrates all other components.

### Sidebar.jsx
Navigation sidebar with menu items. Features:
- Active state highlighting
- Icon support using Lucide React
- Responsive design
- Custom theme colors

### Header.jsx
Top header with search and user menu. Features:
- Search functionality
- Notification bell with indicator
- User dropdown menu
- Responsive layout

### StatsCards.jsx
Statistics cards showing key metrics. Features:
- Grid layout (responsive)
- Custom styling for primary card
- Dynamic data from API

### ActivityFeed.jsx
Main activity feed showing recent updates across all enquiries. Features:
- Timeline-style layout with activity indicators
- Hover effects and improved spacing
- Timestamp display with relative formatting
- Full-width layout as primary dashboard content
- Shows activity type, user, enquiry name, and description

## Design Patterns Used

1. **Component Composition**: Each component has a single responsibility
2. **Props Interface**: Clean prop interfaces for reusability
3. **Consistent Styling**: Uses Tailwind CSS with custom theme
4. **Responsive Design**: Mobile-first approach
5. **Accessibility**: Proper ARIA labels and semantic HTML

## Theme Integration

The dashboard uses a custom color scheme:
- Primary: #4F46E5 (Indigo)
- Accent: #10B981 (Emerald)
- Background: Gray scale for clean appearance

## Data Flow

Currently uses mock data from `lib/types.js`. In production, this should be replaced with:
- API calls
- State management (Redux/Zustand)
- Real-time updates

## Future Enhancements

1. Add loading states
2. Implement error handling
3. Add data filtering and sorting
4. Implement real-time updates
5. Add more interactive features

"use client";

import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  MessageSquare,
  Users,
  FileText,
  BarChart3,
  Settings
} from "lucide-react";
import { useOrgNavigation } from "@/hooks/useOrganization";
import { usePathname } from "next/navigation";
import Link from "next/link";

const iconMap = {
  LayoutDashboard,
  MessageSquare,
  Users,
  FileText,
  BarChart3,
  Settings
};

export function Sidebar({ className }) {
  const pathname = usePathname();
  const { navigationItems } = useOrgNavigation();

  const isActive = (href) => {
    if (href.endsWith('/')) {
      // For dashboard route, check exact match or if we're at the org root
      return pathname === href || pathname === href.slice(0, -1);
    }
    return pathname.startsWith(href);
  };

  return (
    <div className={cn("w-64 bg-slate-900 text-white h-screen flex flex-col", className)}>
      {/* Logo/Brand */}
      <div className="p-6 border-b border-slate-800">
        <h1 className="text-xl font-semibold">QueryCRM</h1>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = iconMap[item.icon];
            const active = isActive(item.href);
            return (
              <li key={item.id}>
                <Link
                  href={item.href}
                  className={cn(
                    "flex items-center gap-3 px-4 py-3 rounded-lg transition-colors duration-200",
                    active
                      ? "bg-primary text-white"
                      : "text-gray-300 hover:bg-slate-800 hover:text-white"
                  )}
                >
                  <Icon className="h-5 w-5" />
                  <span className="font-medium">{item.label}</span>
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>
    </div>
  );
}

import { Card, CardContent } from "@/components/ui/card";
import { Loader2 } from "lucide-react";

const getStatsConfig = (stats) => [
  {
    id: 'total-enquiries',
    title: 'Total Enquiries',
    value: stats?.total_enquiries || 0,
    bgColor: 'bg-primary',
    textColor: 'text-white'
  },
  {
    id: 'new-today',
    title: 'New Today',
    value: stats?.new_today || 0,
    bgColor: 'bg-white',
    textColor: 'text-gray-900'
  },
  {
    id: 'in-progress',
    title: 'In Progress',
    value: stats?.in_progress || 0,
    bgColor: 'bg-white',
    textColor: 'text-gray-900'
  },
  {
    id: 'closed',
    title: 'Closed',
    value: stats?.closed || 0,
    bgColor: 'bg-white',
    textColor: 'text-gray-900'
  }
];

export function StatsCards({ stats, loading = false, error = null }) {
  const statsConfig = getStatsConfig(stats);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="bg-white border-0 shadow-sm">
            <CardContent className="p-6">
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="col-span-full bg-red-50 border-red-200">
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-red-600 text-sm">Failed to load dashboard statistics</p>
              <p className="text-red-500 text-xs mt-1">{error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {statsConfig.map((stat) => (
        <Card key={stat.id} className={`${stat.bgColor} border-0 shadow-sm`}>
          <CardContent className="p-6">
            <div className="space-y-2">
              <h3 className={`text-sm font-medium ${stat.textColor} opacity-80`}>
                {stat.title}
              </h3>
              <p className={`text-3xl font-bold ${stat.textColor}`}>
                {stat.value}
              </p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

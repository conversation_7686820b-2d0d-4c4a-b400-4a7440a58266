"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { X, ExternalLink, Copy, CheckCircle, Code } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/lib/auth/AuthContext";
import { getAPIKey, copyToClipboard } from "@/lib/api/apiKey";
import toast from 'react-hot-toast';

export function WelcomeBanner({ stats, onDismiss }) {
  const [showModal, setShowModal] = useState(false);
  const [formId, setFormId] = useState(null);
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();

  // Only show banner if total enquiries is 0
  const shouldShow = stats && stats.total_enquiries === 0;

  if (!shouldShow) {
    return null;
  }

  const loadFormId = async () => {
    if (formId) return; // Already loaded
    
    try {
      setLoading(true);
      const response = await getAPIKey();
      setFormId(response.form_id);
    } catch (error) {
      console.error('Failed to load form ID:', error);
      toast.error('Failed to load form ID. Please check your permissions.');
    } finally {
      setLoading(false);
    }
  };

  const handleGetFormUrl = async () => {
    await loadFormId();
    setShowModal(true);
  };

  const handleCopyFormUrl = async () => {
    if (!formId) return;
    
    try {
      const formUrl = `https://querycrm.com/f/${formId}`;
      await copyToClipboard(formUrl);
      toast.success('Form URL copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy form URL:', error);
      toast.error('Failed to copy form URL');
    }
  };

  const handleDismiss = () => {
    // Store dismissal in localStorage
    localStorage.setItem('welcomeBannerDismissed', 'true');
    if (onDismiss) {
      onDismiss();
    }
  };

  return (
    <>
      <Card className="mb-6 bg-gradient-to-r from-primary/5 to-accent/5 border-primary/20">
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10">
                  <img
                    src="/logo.svg"
                    alt="QueryCRM Logo"
                    className="w-10 h-10"
                  />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Welcome to QueryCRM!
                  </h3>
                  <Badge variant="secondary" className="text-xs">
                    Getting Started
                  </Badge>
                </div>
              </div>
              
              <p className="text-gray-600 mb-4 max-w-2xl">
                Start collecting enquiries from your website in minutes! Get your unique form URL 
                and integrate it with any HTML form - no coding required.
              </p>
              
              <div className="flex items-center gap-3">
                <Button 
                  onClick={handleGetFormUrl}
                  className="bg-primary text-white hover:bg-primary/90"
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Loading...
                    </>
                  ) : (
                    <>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Get Your Form URL
                    </>
                  )}
                </Button>
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Form URL Setup Modal */}
      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Your Form Integration URL
            </DialogTitle>
            <DialogDescription>
              Copy this URL and use it in your website forms to start collecting enquiries instantly.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Form URL Display */}
            {formId && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <Label htmlFor="form-url" className="text-sm font-medium text-gray-900 mb-2 block">
                  Your Form Submission URL
                </Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="form-url"
                    type="text"
                    value={`https://querycrm.com/f/${formId}`}
                    readOnly
                    className="font-mono text-sm flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopyFormUrl}
                    className="shrink-0"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <p className="text-xs text-gray-600 mt-2">
                  This URL works with any HTML form - no API keys required!
                </p>
              </div>
            )}

            {/* Integration Examples */}
            <Tabs defaultValue="html" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="html">HTML Form</TabsTrigger>
                <TabsTrigger value="javascript">JavaScript</TabsTrigger>
              </TabsList>
              
              <TabsContent value="html" className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Simple HTML Form</h4>
                  <div className="bg-gray-900 text-gray-100 p-4 rounded-md font-mono text-sm overflow-x-auto">
{formId ? `<form action="https://querycrm.com/f/${formId}" method="POST">
  <input type="text" name="name" placeholder="Your Name" required>
  <input type="email" name="email" placeholder="Your Email" required>
  <input type="tel" name="phone" placeholder="Your Phone">
  <input type="tel" name="source" value="Your Source" hidden>
  <input type="tel" name="form_type" value="Your Form Type" hidden>
  <textarea name="message" placeholder="Your Message" required></textarea>
  <button type="submit">Send Message</button>
</form>` : 'Loading form URL...'}
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="javascript" className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">JavaScript/AJAX Submission</h4>
                  <div className="bg-gray-900 text-gray-100 p-4 rounded-md font-mono text-sm overflow-x-auto">
{formId ? `fetch('https://querycrm.com/f/${formId}', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    source: 'Your Source',
    form_type: 'Your Form Type',
    message: 'Hello, I am interested in your services.'
  })
})
.then(response => response.json())
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error));` : 'Loading form URL...'}
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            {/* Quick Tips */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Quick Tips:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Required fields: name, email, message (or description)</li>
                <li>• Optional fields: phone, any custom fields</li>
                <li>• Add hidden fields for source tracking</li>
                <li>• Works from any domain - no CORS issues</li>
              </ul>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

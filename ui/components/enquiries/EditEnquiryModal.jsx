"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Edit, Loader2, AlertCircle } from "lucide-react";
import { enquiryAPI } from "@/lib/api/enquiries";
import { userAPI } from "@/lib/api/user";
import { enquiryPermissions } from "@/lib/utils/permissions";
import { useAuth } from "@/lib/auth/AuthContext";
import toast from "react-hot-toast";

export function EditEnquiryModal({ 
  enquiry, 
  open, 
  onOpenChange, 
  onEnquiryUpdated 
}) {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState([]);
  const [usersLoading, setUsersLoading] = useState(false);
  
  const [formData, setFormData] = useState({
    status: "",
    priority: "",
    assigned_to_id: ""
  });

  // Initialize form data when enquiry changes
  useEffect(() => {
    if (enquiry) {
      setFormData({
        status: enquiry.status || "",
        priority: enquiry.priority || "",
        assigned_to_id: enquiry.assigned_to_id || "unassigned"
      });
    }
  }, [enquiry]);

  // Fetch users for assignment dropdown
  useEffect(() => {
    const fetchUsers = async () => {
      if (!open || !enquiryPermissions.canEditAssignment(user)) {
        return;
      }

      try {
        setUsersLoading(true);
        const allUsers = await userAPI.getAllUsers();
        setUsers(allUsers || []);
      } catch (error) {
        console.error('Failed to fetch users:', error);
        setUsers([]);
      } finally {
        setUsersLoading(false);
      }
    };

    fetchUsers();
  }, [open, user]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!enquiry?.id) {
      toast.error("No enquiry selected");
      return;
    }

    // Build update data (only include changed fields)
    const updateData = {};
    
    if (formData.status && formData.status !== enquiry.status) {
      updateData.status = formData.status;
    }
    
    if (formData.priority && formData.priority !== enquiry.priority && enquiryPermissions.canEditPriority(user)) {
      updateData.priority = formData.priority;
    }
    
    // Handle assignment changes (including unassigned)
    const currentAssignedId = enquiry.assigned_to_id || "unassigned";
    if (formData.assigned_to_id !== currentAssignedId) {
      // Convert "unassigned" back to null for API
      updateData.assigned_to_id = formData.assigned_to_id === "unassigned" ? null : formData.assigned_to_id;
    }

    // Check if there are any changes
    if (Object.keys(updateData).length === 0) {
      toast.error("No changes to save");
      return;
    }

    try {
      setLoading(true);
      
      console.log('[EditEnquiryModal] Updating enquiry:', enquiry.id, updateData);
      const updatedEnquiry = await enquiryAPI.updateEnquiry(enquiry.id, updateData);
      
      toast.success("Enquiry updated successfully!");
      
      // Close modal
      onOpenChange(false);
      
      // Notify parent component
      if (onEnquiryUpdated) {
        onEnquiryUpdated(updatedEnquiry);
      }
      
    } catch (error) {
      console.error('[EditEnquiryModal] Failed to update enquiry:', error);
      toast.error(error.message || 'Failed to update enquiry. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    // Reset form to original values
    if (enquiry) {
      setFormData({
        status: enquiry.status || "",
        priority: enquiry.priority || "",
        assigned_to_id: enquiry.assigned_to_id || "unassigned"
      });
    }
    onOpenChange(false);
  };

  // Get permission-based options
  const availableStatuses = enquiryPermissions.getAvailableStatuses(user, enquiry?.status);
  const availablePriorities = enquiryPermissions.getAvailablePriorities(user);
  const availableAssignees = enquiryPermissions.getAvailableAssignees(user, users);
  const fieldLabels = enquiryPermissions.getFieldLabels(user);
  const fieldHelpText = enquiryPermissions.getFieldHelpText(user);

  // Check permissions
  const canEditStatus = enquiryPermissions.canEditStatus(user, enquiry);
  const canEditPriority = enquiryPermissions.canEditPriority(user);
  const canEditAssignment = enquiryPermissions.canEditAssignment(user);

  if (!enquiry) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Edit Enquiry - {enquiry.name}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Status Field */}
          {canEditStatus && (
            <div className="space-y-2">
              <Label htmlFor="status">{fieldLabels.status}</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleInputChange("status", value)}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {availableStatuses.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">{fieldHelpText.status}</p>
            </div>
          )}

          {/* Priority Field */}
          {canEditPriority ? (
            <div className="space-y-2">
              <Label htmlFor="priority">{fieldLabels.priority}</Label>
              <Select
                value={formData.priority}
                onValueChange={(value) => handleInputChange("priority", value)}
                disabled={loading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  {availablePriorities.map((priority) => (
                    <SelectItem key={priority.value} value={priority.value}>
                      {priority.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">{fieldHelpText.priority}</p>
            </div>
          ) : (
            <div className="space-y-2">
              <Label className="text-gray-400">{fieldLabels.priority}</Label>
              <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-md">
                <AlertCircle className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">{enquiry.priority}</span>
                <span className="text-xs text-gray-400 ml-auto">Read-only</span>
              </div>
              <p className="text-xs text-gray-500">{fieldHelpText.priority}</p>
            </div>
          )}

          {/* Assignment Field */}
          {canEditAssignment && (
            <div className="space-y-2">
              <Label htmlFor="assigned_to">{fieldLabels.assignment}</Label>
              {usersLoading ? (
                <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-md">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm text-gray-600">Loading users...</span>
                </div>
              ) : (
                <Select
                  value={formData.assigned_to_id}
                  onValueChange={(value) => handleInputChange("assigned_to_id", value)}
                  disabled={loading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={user?.role === 'agent' ? "Select manager or admin" : "Select assignee"} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="unassigned">Unassigned</SelectItem>
                    {availableAssignees.map((assignee) => (
                      <SelectItem key={assignee.id} value={assignee.id}>
                        {assignee.name} ({assignee.role})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
              <p className="text-xs text-gray-500">{fieldHelpText.assignment}</p>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-primary text-gray-50 hover:bg-primary/90"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

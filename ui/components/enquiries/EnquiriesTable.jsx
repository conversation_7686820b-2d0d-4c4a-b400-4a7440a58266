import { Card, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Pagination } from "@/components/ui/pagination";
import { DeleteConfirmationDialog } from "@/components/ui/DeleteConfirmationDialog";
import { Loader2, Trash2, Shield, ShieldAlert, Mail, MailOpen } from "lucide-react";
import { formatDateShort } from "@/lib/utils/dateUtils";
import { enquiryPermissions } from "@/lib/utils/permissions";
import { useAuth } from "@/lib/auth/AuthContext";
import { useState } from "react";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";



// Helper function to check if enquiry is unread
const isUnread = (enquiry) => {
  return enquiry.is_read === false || enquiry.is_read === undefined;
};

// Get row styling for spam enquiries and read/unread status
const getRowStyling = (enquiry, tabType) => {
  const baseClasses = "border-gray-100 cursor-pointer group";

  // Handle spam enquiries
  if (tabType === "spam" || enquiry.is_spam) {
    return `${baseClasses} bg-red-50/30 border-red-100`;
  }

  // Handle read/unread status for genuine enquiries
  // Treat undefined or false as unread (for backward compatibility)
  if (isUnread(enquiry)) {
    // Unread enquiries - slightly different background and border
    return `${baseClasses} bg-blue-50/30 border-blue-100`;
  }

  // Read enquiries - default styling
  return baseClasses;
};

export function EnquiriesTable({
  enquiries = [],
  loading = false,
  error = null,
  pagination = { page: 1, limit: 10, total: 0 },
  onPageChange,
  onPageSizeChange,
  onEnquiryDeleted,
  onMarkAsSpam,
  onMarkAsNotSpam,
  onMarkAsRead,
  onMarkAsUnread,
  showSpamActions = false,
  tabType = "genuine"
}) {
  const { user } = useAuth();
  const router = useRouter();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [enquiryToDelete, setEnquiryToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [spamActionLoading, setSpamActionLoading] = useState(null); // Track which enquiry is being processed
  const [readActionLoading, setReadActionLoading] = useState(null); // Track which enquiry is being processed for read/unread

  const handleDeleteClick = (enquiry) => {
    // Check if user can delete enquiries
    if (enquiryPermissions.canDeleteEnquiry(user)) {
      setEnquiryToDelete(enquiry);
      setDeleteDialogOpen(true);
    } else {
      toast.error('Only administrators can delete enquiries');
    }
  };

  const handleDeleteConfirm = async () => {
    if (!enquiryToDelete) return;

    try {
      setDeleteLoading(true);

      await enquiryAPI.deleteEnquiry(enquiryToDelete.id);

      toast.success('Enquiry deleted successfully');

      // Close dialog and reset state
      setDeleteDialogOpen(false);
      setEnquiryToDelete(null);

      // Notify parent component
      if (onEnquiryDeleted) {
        onEnquiryDeleted(enquiryToDelete.id);
      }

    } catch (error) {
      console.error('[EnquiriesTable] Failed to delete enquiry:', error);
      toast.error(error.message || 'Failed to delete enquiry. Please try again.');
    } finally {
      setDeleteLoading(false);
    }
  };

  // Handle spam actions
  const handleSpamAction = async (enquiry, action) => {
    try {
      setSpamActionLoading(enquiry.id);

      if (action === 'mark-spam' && onMarkAsSpam) {
        await onMarkAsSpam(enquiry.id);
      } else if (action === 'mark-not-spam' && onMarkAsNotSpam) {
        await onMarkAsNotSpam(enquiry.id);
      }
    } finally {
      setSpamActionLoading(null);
    }
  };

  // Handle read/unread actions
  const handleReadAction = async (enquiry, action) => {
    try {
      setReadActionLoading(enquiry.id);

      if (action === 'mark-read' && onMarkAsRead) {
        await onMarkAsRead(enquiry.id);
      } else if (action === 'mark-unread' && onMarkAsUnread) {
        await onMarkAsUnread(enquiry.id);
      }
    } catch (error) {
      console.error('Read action failed:', error);
    } finally {
      setReadActionLoading(null);
    }
  };

  // Handle row click - navigate to enquiry details and mark as read
  const handleRowClick = async (enquiry, event) => {
    // Prevent navigation if clicking on action buttons
    if (event.target.closest('button') || event.target.closest('a')) {
      return;
    }

    try {
      // Mark as read if it's unread
      if (isUnread(enquiry) && onMarkAsRead) {
        await onMarkAsRead(enquiry.id);
      }

      // Navigate to enquiry details
      router.push(`/enquiries/${enquiry.slug}`);
    } catch (error) {
      console.error('Failed to mark enquiry as read:', error);
      // Still navigate even if marking as read fails
      router.push(`/enquiries/${enquiry.slug}`);
    }
  };

  // Loading state
  if (loading) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="flex items-center justify-center">
            <div className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span className="text-gray-600">Loading enquiries...</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="text-center">
            <p className="text-red-600 mb-2">Failed to load enquiries</p>
            <p className="text-gray-500 text-sm">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Empty state
  if (!enquiries || enquiries.length === 0) {
    return (
      <Card>
        <CardContent className="p-8">
          <div className="text-center">
            <p className="text-gray-600 mb-2">No enquiries found</p>
            <p className="text-gray-500 text-sm">Try adjusting your filters or create a new enquiry</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalPages = Math.ceil(pagination.total / pagination.limit);

  return (
    <div className="space-y-4">
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableBody>
              {enquiries.map((enquiry) => (
                <TableRow
                  key={enquiry.id}
                  className={getRowStyling(enquiry, tabType)}
                  onClick={(e) => handleRowClick(enquiry, e)}
                >
                  {/* Email Column */}
                  <TableCell className={`text-gray-600 ${isUnread(enquiry) ? 'font-semibold' : ''} py-2`}>
                    <div className="flex items-center gap-2">
                      {isUnread(enquiry) && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0" title="Unread enquiry" />
                      )}
                      {enquiry.email}
                    </div>
                  </TableCell>

                  {/* Source - Message Combined Column */}
                  <TableCell className="text-gray-600 py-2">
                    <div className="text-sm truncate">
                      <span className="font-semibold">{enquiry.source || 'N/A'}</span>
                      <span className="font-normal"> - {enquiry.description && enquiry.description.length > 60
                        ? enquiry.description.substring(0, 60) + '...'
                        : enquiry.description || 'No message'}</span>
                    </div>
                  </TableCell>

                  {/* Submitted Date Column with Overlay Actions */}
                  <TableCell className={`text-gray-600 ${isUnread(enquiry) ? 'font-semibold' : ''} relative py-2`}>
                    {/* Date Text - Hidden on hover */}
                    <span className="group-hover:opacity-0 transition-opacity">
                      {formatDateShort(enquiry.submitted_on)}
                    </span>

                    {/* Actions Overlay - Shown on hover */}
                    <div className="absolute inset-0 flex items-center justify-end gap-1 pr-4 opacity-0 group-hover:opacity-100 transition-opacity">
                      {/* Read/Unread Toggle Button */}
                      <Button
                        variant="ghost"
                        size="icon"
                        className={`h-7 w-7 ${isUnread(enquiry)
                          ? 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                          : 'text-gray-600 hover:text-gray-700 hover:bg-gray-100'
                        }`}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleReadAction(enquiry, isUnread(enquiry) ? 'mark-read' : 'mark-unread');
                        }}
                        disabled={readActionLoading === enquiry.id}
                        title={isUnread(enquiry) ? "Mark as Read" : "Mark as Unread"}
                      >
                        {readActionLoading === enquiry.id ? (
                          <Loader2 className="h-3 w-3 animate-spin" />
                        ) : isUnread(enquiry) ? (
                          <MailOpen className="h-3 w-3" />
                        ) : (
                          <Mail className="h-3 w-3" />
                        )}
                      </Button>

                      {/* Spam Management Buttons */}
                      {showSpamActions && enquiryPermissions.canManageSpam(user) && (
                        <>
                          {tabType === "genuine" ? (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-7 w-7 text-red-600 hover:text-red-700 hover:bg-red-50"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSpamAction(enquiry, 'mark-spam');
                              }}
                              disabled={spamActionLoading === enquiry.id}
                              title="Mark as Spam"
                            >
                              {spamActionLoading === enquiry.id ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <ShieldAlert className="h-3 w-3" />
                              )}
                            </Button>
                          ) : (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-7 w-7 text-green-600 hover:text-green-700 hover:bg-green-50"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSpamAction(enquiry, 'mark-not-spam');
                              }}
                              disabled={spamActionLoading === enquiry.id}
                              title="Mark as Not Spam"
                            >
                              {spamActionLoading === enquiry.id ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <Shield className="h-3 w-3" />
                              )}
                            </Button>
                          )}
                        </>
                      )}

                      {enquiryPermissions.canDeleteEnquiry(user) && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 text-destructive hover:text-destructive hover:bg-destructive/10"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteClick(enquiry);
                          }}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <Pagination
          currentPage={pagination.page}
          totalPages={totalPages}
          totalItems={pagination.total}
          pageSize={pagination.limit}
          onPageChange={onPageChange}
          onPageSizeChange={onPageSizeChange}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        title="Delete Enquiry"
        description={`Are you sure you want to delete the enquiry "${enquiryToDelete?.name}"? This action cannot be undone.`}
        confirmText="Delete Enquiry"
        loading={deleteLoading}
      />
    </div>
  );
}

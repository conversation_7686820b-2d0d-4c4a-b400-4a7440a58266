import { Card, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Activity,
  Clock,
  User,
  MessageSquare,
  RefreshCw,
  Plus,
  FileText,
  Loader2
} from "lucide-react";
import { formatDateTime } from "@/lib/utils/dateUtils";

const getActivityIcon = (type) => {
  switch (type.toLowerCase()) {
    case 'status changed':
      return RefreshCw;
    case 'note added':
      return MessageSquare;
    case 'enquiry created':
      return Plus;
    default:
      return FileText;
  }
};

const getActivityColor = (type) => {
  switch (type.toLowerCase()) {
    case 'status changed':
      return "text-blue-600 bg-blue-100";
    case 'note added':
      return "text-green-600 bg-green-100";
    case 'enquiry created':
      return "text-purple-600 bg-purple-100";
    default:
      return "text-gray-600 bg-gray-100";
  }
};

export function EnquiryActivity({ activities = [], loading = false }) {
  console.log("Rendering EnquiryActivity with activities:", activities);
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-4 w-4" />
          Activity Timeline
        </CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
            <p className="text-gray-500">Loading activities...</p>
          </div>
        ) : activities.length === 0 ? (
          <div className="text-center py-8">
            <Activity className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No activity yet</p>
            <p className="text-sm text-gray-400">Activity will appear here as actions are taken</p>
          </div>
        ) : (
          <div className="space-y-4">
            {activities.map((activity, index) => {
              const Icon = getActivityIcon(activity.type);
              const colorClass = getActivityColor(activity.type);

              return (
                <div key={activity.id}>
                  <div className="flex items-start gap-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${colorClass}`}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-sm font-medium text-gray-900">
                          {activity.type}
                        </span>
                        <span className="text-xs text-gray-500 flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {formatDateTime(activity.created_at)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-1">{activity.description}</p>
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <User className="h-3 w-3" />
                        {activity.user || 'System'}
                      </div>
                    </div>
                  </div>
                  {index < activities.length - 1 && <Separator className="mt-4" />}
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

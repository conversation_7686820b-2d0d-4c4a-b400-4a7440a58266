import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import toast from "react-hot-toast";
import {
  ArrowLeft,
  Edit,
  Mail,
  Phone,
  Calendar,
  User,
  Building,
  Flag,
  Globe,
  Trash2,
  FileText
} from "lucide-react";
import Link from "next/link";
import { formatDate } from "@/lib/utils/dateUtils";
import { enquiryPermissions } from "@/lib/utils/permissions";
import { useAuth } from "@/lib/auth/AuthContext";
import { EnquiryNotes } from "./EnquiryNotes";
import { EnquiryActivity } from "./EnquiryActivity";
import { EditEnquiryModal } from "./EditEnquiryModal";
import { DeleteConfirmationDialog } from "@/components/ui/DeleteConfirmationDialog";
import { MetadataDisplay } from "./MetadataDisplay";
import { enquiryAPI } from "@/lib/api/enquiries";
import { useRouter } from "next/navigation";

const getStatusVariant = (status) => {
  switch (status) {
    case "New":
      return "bg-accent text-white";
    case "In Progress":
      return "bg-blue-100 text-blue-800";
    case "Closed":
      return "bg-gray-100 text-gray-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getPriorityVariant = (priority) => {
  switch (priority.toLowerCase()) {
    case 'high':
      return "bg-red-100 text-red-800";
    case 'medium':
      return "bg-yellow-100 text-yellow-800";
    case 'low':
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export function EnquiryDetails({
  enquiry,
  notes = [],
  activities = [],
  notesLoading = false,
  activitiesLoading = false,
  onAddNote,
  onDeleteNote,
  onUpdateEnquiry,
  onDeleteEnquiry,
  onRefresh
}) {
  const { user } = useAuth();
  const router = useRouter();
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const handleEditClick = () => {
    // Check if user can edit this enquiry
    if (enquiryPermissions.canEditEnquiry(user, enquiry)) {
      setEditModalOpen(true);
    } else {
      toast.error('You do not have permission to edit this enquiry');
    }
  };

  const handleEnquiryUpdated = (updatedEnquiry) => {
    console.log('[EnquiryDetails] Enquiry updated:', updatedEnquiry);
    if (onUpdateEnquiry) {
      onUpdateEnquiry(updatedEnquiry);
    }
    if (onRefresh) {
      onRefresh();
    }
  };

  const handleDeleteClick = () => {
    // Check if user can delete enquiries
    if (enquiryPermissions.canDeleteEnquiry(user)) {
      setDeleteDialogOpen(true);
    } else {
      toast.error('Only administrators can delete enquiries');
    }
  };

  const handleDeleteConfirm = async () => {
    try {
      setDeleteLoading(true);

      if (onDeleteEnquiry) {
        await onDeleteEnquiry();
      } else {
        await enquiryAPI.deleteEnquiry(enquiry.id);
      }

      toast.success('Enquiry deleted successfully');

      // Navigate back to enquiries list
      router.push('/enquiries');

    } catch (error) {
      console.error('[EnquiryDetails] Failed to delete enquiry:', error);
      toast.error(error.message || 'Failed to delete enquiry. Please try again.');
    } finally {
      setDeleteLoading(false);
      setDeleteDialogOpen(false);
    }
  };
  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Link href="/enquiries">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">{enquiry.name}</h1>
            <p className="text-sm text-gray-600">Enquiry #{enquiry.id?.substring(0, 8) || 'N/A'}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge className={getStatusVariant(enquiry.status)}>
            {enquiry.status}
          </Badge>
          <Badge className={getPriorityVariant(enquiry.priority)}>
            {enquiry.priority}
          </Badge>
          <Button
            className="bg-primary text-gray-50 hover:bg-primary/90"
            onClick={handleEditClick}
            disabled={false}
          >
            <Edit className="h-4 w-4 mr-2 " />
            Edit {!user ? '(No User)' : !enquiryPermissions.canEditEnquiry(user, enquiry) ? '(No Permission)' : ''}
          </Button>
          {enquiryPermissions.canDeleteEnquiry(user) && (
            <Button
              variant="destructive"
              onClick={handleDeleteClick}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="notes">Notes ({notes.length})</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              <Card>
                <CardHeader>
                  <CardTitle>Enquiry Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                    <p className="text-gray-600">{enquiry.description || 'No description provided'}</p>
                  </div>
                  <Separator />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-1">Source</h4>
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-gray-400" />
                        <p className="text-gray-600">{enquiry.source || 'N/A'}</p>
                      </div>
                    </div>
                    {enquiry.form_type && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-1">Form Type</h4>
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-gray-400" />
                          <p className="text-gray-600">{enquiry.form_type}</p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Additional Information from Form */}
                  <MetadataDisplay
                    metadata={enquiry.metadata}
                    showCard={false}
                    className="mt-4"
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notes">
              <EnquiryNotes
                notes={notes}
                enquiryId={enquiry.id}
                loading={notesLoading}
                onAddNote={onAddNote}
                onDeleteNote={onDeleteNote}
              />
            </TabsContent>

            <TabsContent value="activity">
              <EnquiryActivity
                activities={activities}
                loading={activitiesLoading}
              />
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <Mail className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{enquiry.email}</p>
                  <p className="text-xs text-gray-500">Email</p>
                </div>
              </div>
              {enquiry.phone && (
                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{enquiry.phone}</p>
                    <p className="text-xs text-gray-500">Phone</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Assignment & Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                Assignment & Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <User className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {enquiry.assigned_to_id ?
                      enquiry.assigned_to_id.substring(0, 8) + '...' :
                      'Unassigned'
                    }
                  </p>
                  <p className="text-xs text-gray-500">Assigned To</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Calendar className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{formatDate(enquiry.submitted_on)}</p>
                  <p className="text-xs text-gray-500">Submitted On</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Flag className="h-4 w-4 text-gray-400" />
                <div>
                  <Badge className={getPriorityVariant(enquiry.priority)}>
                    {enquiry.priority} Priority
                  </Badge>
                </div>
              </div>
              {enquiry.form_type && (
                <div className="flex items-center gap-3">
                  <FileText className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{enquiry.form_type}</p>
                    <p className="text-xs text-gray-500">Form Type</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Modal */}
      <EditEnquiryModal
        enquiry={enquiry}
        open={editModalOpen}
        onOpenChange={setEditModalOpen}
        onEnquiryUpdated={handleEnquiryUpdated}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        title="Delete Enquiry"
        description={`Are you sure you want to delete the enquiry "${enquiry.name}"? This action cannot be undone.`}
        confirmText="Delete Enquiry"
        loading={deleteLoading}
      />
    </div>
  );
}

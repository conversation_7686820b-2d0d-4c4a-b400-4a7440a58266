/**
 * MetadataDisplay Component
 * 
 * A reusable component for displaying enquiry metadata in a user-friendly way.
 * Excludes form_id and other internal fields from display.
 */

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { FileText } from "lucide-react";

// Helper function to parse metadata
const parseMetadata = (metadata) => {
  if (!metadata) return null;
  
  try {
    // If metadata is already an object, use it directly
    if (typeof metadata === 'object' && metadata !== null) {
      return metadata;
    }
    
    // If metadata is a string, try to parse it as JSON
    if (typeof metadata === 'string') {
      return JSON.parse(metadata);
    }
    
    return null;
  } catch (error) {
    console.error('Failed to parse metadata:', error);
    return null;
  }
};

// Helper function to format field names for display
const formatFieldName = (fieldName) => {
  return fieldName
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Helper function to format field values for display
const formatFieldValue = (value) => {
  if (value === null || value === undefined) return 'N/A';
  if (typeof value === 'boolean') return value ? 'Yes' : 'No';
  if (typeof value === 'object') return JSON.stringify(value);
  return String(value);
};

// Helper function to check if metadata has displayable fields
export const hasDisplayableMetadata = (metadata) => {
  const parsed = parseMetadata(metadata);
  if (!parsed) return false;
  
  // Filter out form_id and other internal fields
  const filteredKeys = Object.keys(parsed).filter(key => 
    key !== 'form_id' && 
    !key.startsWith('_') && 
    key !== 'source' && 
    key !== 'name' && 
    key !== 'email' && 
    key !== 'phone' && 
    key !== 'message' && 
    key !== 'description'
  );
  
  return filteredKeys.length > 0;
};

/**
 * MetadataDisplay Component
 * @param {Object} props
 * @param {Object|string} props.metadata - The metadata object or JSON string
 * @param {string} props.title - Optional title for the metadata section
 * @param {boolean} props.showCard - Whether to wrap in a card (default: true)
 * @param {string} props.className - Additional CSS classes
 */
export function MetadataDisplay({ 
  metadata, 
  title = "Additional Information", 
  showCard = true,
  className = "" 
}) {
  const parsed = parseMetadata(metadata);
  if (!parsed) return null;
  
  // Filter out form_id and other internal fields
  const filteredMetadata = Object.entries(parsed).filter(([key]) => 
    key !== 'form_id' && 
    !key.startsWith('_') && 
    key !== 'source' && 
    key !== 'name' && 
    key !== 'email' && 
    key !== 'phone' && 
    key !== 'message' && 
    key !== 'description'
  );
  
  if (filteredMetadata.length === 0) return null;
  
  const content = (
    <div className={className}>
      <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
        <FileText className="h-4 w-4 text-gray-400" />
        {title}
      </h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {filteredMetadata.map(([key, value]) => (
          <div key={key} className="bg-gray-50 rounded-lg p-3">
            <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
              {formatFieldName(key)}
            </p>
            <p className="text-sm text-gray-900 font-medium">
              {formatFieldValue(value)}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
  
  if (!showCard) {
    return content;
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-4 w-4" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {filteredMetadata.map(([key, value]) => (
            <div key={key} className="bg-gray-50 rounded-lg p-3">
              <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                {formatFieldName(key)}
              </p>
              <p className="text-sm text-gray-900 font-medium">
                {formatFieldValue(value)}
              </p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Inline Metadata Display Component
 * For use in tables or compact layouts
 */
export function InlineMetadataDisplay({ metadata, maxFields = 2 }) {
  const parsed = parseMetadata(metadata);
  if (!parsed) return null;
  
  // Filter out form_id and other internal fields
  const filteredMetadata = Object.entries(parsed).filter(([key]) => 
    key !== 'form_id' && 
    !key.startsWith('_') && 
    key !== 'source' && 
    key !== 'name' && 
    key !== 'email' && 
    key !== 'phone' && 
    key !== 'message' && 
    key !== 'description'
  );
  
  if (filteredMetadata.length === 0) return null;
  
  const displayFields = filteredMetadata.slice(0, maxFields);
  const remainingCount = filteredMetadata.length - maxFields;
  
  return (
    <div className="text-xs text-gray-600 mt-1">
      {displayFields.map(([key, value], index) => (
        <span key={key}>
          <span className="font-medium">{formatFieldName(key)}:</span> {formatFieldValue(value)}
          {index < displayFields.length - 1 && ', '}
        </span>
      ))}
      {remainingCount > 0 && (
        <span className="text-gray-400"> +{remainingCount} more</span>
      )}
    </div>
  );
}

export default MetadataDisplay;

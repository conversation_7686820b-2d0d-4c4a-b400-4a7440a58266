# Enquiries Feature

This directory contains all components for the Enquiries management feature of the CRM system.

## Pages

### `/enquiries` - Enquiries Listing Page
- **Location**: `app/enquiries/page.js`
- **Features**:
  - Filterable table of all enquiries
  - Search functionality
  - Status, sector, and priority filters
  - Quick actions (view, edit)
  - "New Enquiry" button

### `/enquiries/[slug]` - Enquiry Details Page
- **Location**: `app/enquiries/[slug]/page.js`
- **Features**:
  - Detailed enquiry information
  - Tabbed interface (Overview, Notes, Activity)
  - Contact information sidebar
  - Assignment and status information
  - Interactive notes management
  - Activity timeline

## Components

### EnquiriesTable.jsx
Main table component for listing enquiries.
- **Features**:
  - Sortable columns
  - Status and priority badges
  - Clickable names linking to details
  - Action buttons (view, edit, more)
- **Props**: None (uses mock data)

### EnquiriesFilters.jsx
Filter and search component for the enquiries listing.
- **Features**:
  - Search input
  - Status dropdown filter
  - Sector dropdown filter
  - Priority dropdown filter
  - Clear filters functionality
- **Props**: None (stateless for now)

### EnquiryDetails.jsx
Main component for displaying enquiry details.
- **Features**:
  - Header with back button and actions
  - Status and priority badges
  - Tabbed content area
  - Contact information sidebar
  - Assignment details
- **Props**: `{ enquiry }` - enquiry object

### EnquiryNotes.jsx
Component for managing enquiry notes.
- **Features**:
  - Add new notes
  - View existing notes
  - Notes timeline
  - Author and timestamp display
- **Props**: `{ notes, enquiryId }`
- **State**: Uses React hooks for note management

### EnquiryActivity.jsx
Component for displaying enquiry activity timeline.
- **Features**:
  - Activity timeline with icons
  - Different activity types
  - User and timestamp information
  - Color-coded activity types
- **Props**: `{ activities }`

## Data Structure

### Enquiry Object
```javascript
{
  id: number,
  slug: string,
  name: string,
  email: string,
  phone: string,
  sector: 'College' | 'Business',
  assignedTo: string,
  status: 'New' | 'In Progress' | 'Closed',
  submittedOn: string,
  source: string,
  priority: 'High' | 'Medium' | 'Low',
  description: string,
  is_read: boolean,
  notes: Note[],
  activities: Activity[]
}
```

### Note Object
```javascript
{
  id: number,
  content: string,
  author: string,
  timestamp: string
}
```

### Activity Object
```javascript
{
  id: number,
  type: string,
  description: string,
  timestamp: string,
  user: string
}
```

## Routing

- **Static Routes**: `/enquiries`
- **Dynamic Routes**: `/enquiries/[slug]`
- **Static Generation**: Uses `generateStaticParams` for pre-rendering enquiry detail pages

## Styling

- Uses shadcn/ui components for consistent design
- Custom color scheme with status and priority badges
- Responsive design for mobile and desktop
- Tailwind CSS for styling

## Future Enhancements

1. **Real API Integration**
   - Replace mock data with API calls
   - Add loading states
   - Error handling

2. **Advanced Filtering**
   - Date range filters
   - Multiple selection filters
   - Saved filter presets

3. **Enhanced Notes**
   - Rich text editor
   - File attachments
   - @mentions

4. **Real-time Updates**
   - WebSocket integration
   - Live activity updates
   - Collaborative editing

5. **Export/Import**
   - CSV export
   - PDF reports
   - Bulk operations

## Dependencies

- Next.js 15 (App Router)
- shadcn/ui components
- Lucide React icons
- Tailwind CSS
- React hooks (useState for client components)

"use client";

import { useEffect } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { useRouter, usePathname } from 'next/navigation';
import { Loader2 } from 'lucide-react';

/**
 * Component to redirect old routes to organization-aware routes
 */
export function OrgRedirect({ children }) {
  const { user, loading, fetchingUser, authError } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (loading || fetchingUser) return;

    // If there's an authentication error, redirect to login
    if (authError === 'unauthorized') {
      router.replace('/login');
      return;
    }

    if (!user) {
      router.replace('/login');
      return;
    }

    if (!user.org_slug) {
      router.replace('/login');
      return;
    }

    // Check if we're on an old route that needs to be redirected
    const oldRoutes = ['/enquiries', '/notes', '/settings'];
    const isOldRoute = oldRoutes.some(route => pathname.startsWith(route));

    if (isOldRoute) {
      // Redirect to organization-aware route
      const newPath = `/org/${user.org_slug}${pathname}`;
      router.replace(newPath);
    }
  }, [user, loading, fetchingUser, authError, router, pathname]);

  if (loading || fetchingUser) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return children;
}

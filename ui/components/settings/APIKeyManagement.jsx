"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { 
  <PERSON><PERSON>, 
  Loader2,
  <PERSON>ert<PERSON>riangle,
  CheckCircle
} from "lucide-react";
import { getAPIKey, copyToClipboard } from "@/lib/api/apiKey";
import toast from 'react-hot-toast';

export function APIKeyManagement() {
  const [formId, setFormId] = useState('');
  const [initialLoading, setInitialLoading] = useState(true);
  const [showFormRegenerateDialog, setShowFormRegenerateDialog] = useState(false);
  const [showUsageDialog, setShowUsageDialog] = useState(false);
  
  useEffect(() => {
    loadAPIKey();
  }, []);

  const loadAPIKey = async () => {
    try {
      setInitialLoading(true);
      const response = await getAPIKey();
      setFormId(response.form_id);
    } catch (error) {
      console.error('Failed to load API key:', error);

      // Handle specific error cases
      if (error.message.includes('403') || error.message.includes('administrators')) {
        // User is not admin, don't show error toast
        console.log('User is not admin, Form ID not available');
        return;
      } else if (error.message.includes('401') || error.message.includes('Authentication')) {
        // Authentication issue
        toast.error('Please log in again to view Form ID');
        return;
      }

      // Show error for other cases
      const errorMessage = error.message || 'Failed to load Form ID';
      toast.error(errorMessage);
    } finally {
      setInitialLoading(false);
    }
  };

  const handleCopyFormUrl = async () => {
    try {
      // Use clean QueryCRM domain URL instead of exposing backend API
      const formUrl = `https://querycrm.com/f/${formId}`;
      await copyToClipboard(formUrl);
      toast.success('Form URL copied to clipboard');
    } catch (error) {
      console.error('Failed to copy form URL:', error);
      toast.error('Failed to copy form URL');
    }
  };

  // const handleRegenerateFormId = async () => {
  //   try {
  //     setLoading(true);
  //     const response = await regenerateFormID();
  //     setFormId(response.form_id);
  //     setShowFormRegenerateDialog(false);

  //     // Show success message
  //     toast.success(
  //       <div>
  //         <div className="font-medium">Form URL regenerated successfully!</div>
  //         <div className="text-sm text-gray-600 mt-1">
  //           Make sure to update any forms using the old URL.
  //         </div>
  //       </div>,
  //       { duration: 5000 }
  //     );

  //     // Show recently regenerated indicator
  //     setRecentlyRegeneratedForm(true);
  //     setTimeout(() => setRecentlyRegeneratedForm(false), 10000); // Hide after 10 seconds
  //   } catch (error) {
  //     console.error('Failed to regenerate form ID:', error);

  //     // Show specific error message
  //     const errorMessage = error.message || 'Failed to regenerate form ID';
  //     toast.error(errorMessage, { duration: 6000 });
  //   } finally {
  //     setLoading(false);
  //   }
  // };


  if (initialLoading) {
    return (
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div>
          <h4 className="font-medium text-gray-900">API Key</h4>
          <p className="text-sm text-gray-600">Loading...</p>
        </div>
        <Loader2 className="h-4 w-4 animate-spin" />
      </div>
    );
  }

  // Don't render if no API key (user is not admin)
  if (!formId) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Form URL Section */}
      {formId && (
        <div className="flex items-center justify-between p-4 border rounded-lg">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium text-gray-900">Form Submission URL</h4>
            </div>
            <p className="text-sm text-gray-600 mb-3">
              Use this URL for direct HTML form submissions (no API key required)
            </p>

            <div className="flex items-center gap-2">
              <div className="flex-1">
                <Label htmlFor="form-url" className="sr-only">Form URL</Label>
                <Input
                  id="form-url"
                  type="text"
                  value={`https://querycrm.com/f/${formId}`}
                  readOnly
                  className="font-mono text-sm"
                />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyFormUrl}
                className="shrink-0"
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex flex-col gap-2 ml-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowUsageDialog(true)}
            >
              View Examples
            </Button>
            {/* <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFormRegenerateDialog(true)}
              disabled={loading}
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Regenerate
            </Button> */}
          </div>
        </div>
      )}

      {/* Regenerate Form ID Confirmation Dialog */}
      <Dialog open={showFormRegenerateDialog} onOpenChange={setShowFormRegenerateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Regenerate Form URL
            </DialogTitle>
            <DialogDescription>
              <div className="space-y-3">
                <p>
                  This action will generate a new form URL and invalidate the current one.
                  Any forms using the current URL will stop working until updated.
                </p>

                <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 shrink-0" />
                    <div className="text-sm text-amber-800">
                      <div className="font-medium">Before proceeding:</div>
                      <ul className="mt-1 list-disc list-inside space-y-1">
                        <li>Make note of any forms using the current URL</li>
                        <li>Have access to update those forms immediately</li>
                        <li>Consider doing this during maintenance windows</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <p className="font-medium">
                  Are you sure you want to continue?
                </p>
              </div>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowFormRegenerateDialog(false)}>
              Cancel
            </Button>
            {/* <Button
              onClick={handleRegenerateFormId}
              disabled={loading}
              className="bg-red-600 hover:bg-red-700"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : null}
              Regenerate URL
            </Button> */}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Usage Instructions Dialog */}
      <Dialog open={showUsageDialog} onOpenChange={setShowUsageDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Integration Guide
            </DialogTitle>
            <DialogDescription>
              This guide covers form URL integration (for HTML forms).
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Method 1: HTML Form Submission (Recommended) */}
            {formId && (
              <div className="border rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Method 1: HTML Form Submission (Recommended)</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Simple HTML forms that work without JavaScript. Perfect for contact forms, lead generation, etc.
                </p>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">HTML Example</h4>
                    <div className="bg-gray-50 p-3 rounded-md font-mono text-sm overflow-x-auto">
{`<form action="${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/f/${formId}" method="POST">
  <input type="text" name="name" placeholder="Your Name" required>
  <input type="email" name="email" placeholder="Your Email" required>
  <input type="tel" name="phone" placeholder="Your Phone">
  <input type="tel" name="source" value="Your Source" hidden>
  <input type="tel" name="form_type" value="Your Form Type" hidden>

  <textarea name="message" placeholder="Your Message" required></textarea>
  <button type="submit">Send Message</button>
</form>`}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">JavaScript/AJAX Example</h4>
                    <div className="bg-gray-50 p-3 rounded-md font-mono text-sm overflow-x-auto">
{`fetch('https://querycrm.com/f/${formId}', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    message: 'Hello, I am interested in your services.',
    source: 'Your Source',
    form_type: 'Your Form Type',
    custom_field: 'custom_value'
  })
})
.then(response => response.json())
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error));`}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Common Information */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Required Fields (Both Methods)</h4>
              <ul className="text-sm text-blue-800 list-disc list-inside space-y-1">
                <li><code>name</code> - Customer's full name</li>
                <li><code>email</code> - Customer's email address</li>
                <li><code>message</code> or <code>description</code> - Enquiry message</li>
              </ul>
            </div>
          </div>
          
          <DialogFooter>
            <Button onClick={() => setShowUsageDialog(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default APIKeyManagement;

"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { enquiryStatuses, enquiryPriorities } from "@/lib/types";
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save,
  Settings,
  Flag,
  Circle
} from "lucide-react";

export function EnquiryConfiguration() {
  const [statuses, setStatuses] = useState(enquiryStatuses);
  const [priorities, setPriorities] = useState(enquiryPriorities);
  const [isAddingStatus, setIsAddingStatus] = useState(false);
  const [isAddingPriority, setIsAddingPriority] = useState(false);
  const [newStatus, setNewStatus] = useState({
    name: "",
    color: "#3B82F6",
    description: "",
    isActive: true
  });
  const [newPriority, setNewPriority] = useState({
    name: "",
    color: "#F59E0B",
    description: "",
    isActive: true
  });

  const handleAddStatus = () => {
    const status = {
      id: newStatus.name.toLowerCase().replace(/\s+/g, '-'),
      ...newStatus,
      isDefault: false
    };
    setStatuses(prev => [...prev, status]);
    setNewStatus({ name: "", color: "#3B82F6", description: "", isActive: true });
    setIsAddingStatus(false);
  };

  const handleAddPriority = () => {
    const priority = {
      id: newPriority.name.toLowerCase().replace(/\s+/g, '-'),
      ...newPriority,
      isDefault: false
    };
    setPriorities(prev => [...prev, priority]);
    setNewPriority({ name: "", color: "#F59E0B", description: "", isActive: true });
    setIsAddingPriority(false);
  };

  const toggleStatusActive = (id) => {
    setStatuses(prev => prev.map(status => 
      status.id === id ? { ...status, isActive: !status.isActive } : status
    ));
  };

  const togglePriorityActive = (id) => {
    setPriorities(prev => prev.map(priority => 
      priority.id === id ? { ...priority, isActive: !priority.isActive } : priority
    ));
  };

  return (
    <div className="space-y-6">
      {/* Enquiry Statuses */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Circle className="h-5 w-5" />
              Enquiry Statuses
            </div>
            <Dialog open={isAddingStatus} onOpenChange={setIsAddingStatus}>
              <DialogTrigger asChild>
                <Button className="bg-primary hover:bg-primary/90 text-gray-50">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Status
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Status</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="statusName">Status Name</Label>
                    <Input
                      id="statusName"
                      value={newStatus.name}
                      onChange={(e) => setNewStatus(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Under Review"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="statusColor">Color</Label>
                    <div className="flex items-center gap-2">
                      <input
                        type="color"
                        id="statusColor"
                        value={newStatus.color}
                        onChange={(e) => setNewStatus(prev => ({ ...prev, color: e.target.value }))}
                        className="w-12 h-10 rounded border"
                      />
                      <Input
                        value={newStatus.color}
                        onChange={(e) => setNewStatus(prev => ({ ...prev, color: e.target.value }))}
                        placeholder="#3B82F6"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="statusDescription">Description</Label>
                    <Textarea
                      id="statusDescription"
                      value={newStatus.description}
                      onChange={(e) => setNewStatus(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Describe when this status should be used..."
                      rows={3}
                    />
                  </div>
                  <div className="flex justify-end gap-2 pt-4">
                    <Button variant="outline" onClick={() => setIsAddingStatus(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleAddStatus} className="bg-primary hover:bg-primary/90">
                      Add Status
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {statuses.map((status) => (
              <div key={status.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div 
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: status.color }}
                  ></div>
                  <div>
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium text-gray-900">{status.name}</h4>
                      {status.isDefault && (
                        <Badge variant="outline" className="text-xs">Default</Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{status.description}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={status.isActive}
                    onCheckedChange={() => toggleStatusActive(status.id)}
                  />
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Edit className="h-4 w-4" />
                  </Button>
                  {!status.isDefault && (
                    <Button variant="ghost" size="icon" className="h-8 w-8 text-red-600">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Enquiry Priorities */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Flag className="h-5 w-5" />
              Enquiry Priorities
            </div>
            <Dialog open={isAddingPriority} onOpenChange={setIsAddingPriority}>
              <DialogTrigger asChild>
                <Button className="bg-primary text-gray-50 hover:bg-primary/90">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Priority
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Priority</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="priorityName">Priority Name</Label>
                    <Input
                      id="priorityName"
                      value={newPriority.name}
                      onChange={(e) => setNewPriority(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Critical"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="priorityColor">Color</Label>
                    <div className="flex items-center gap-2">
                      <input
                        type="color"
                        id="priorityColor"
                        value={newPriority.color}
                        onChange={(e) => setNewPriority(prev => ({ ...prev, color: e.target.value }))}
                        className="w-12 h-10 rounded border"
                      />
                      <Input
                        value={newPriority.color}
                        onChange={(e) => setNewPriority(prev => ({ ...prev, color: e.target.value }))}
                        placeholder="#F59E0B"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="priorityDescription">Description</Label>
                    <Textarea
                      id="priorityDescription"
                      value={newPriority.description}
                      onChange={(e) => setNewPriority(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Describe when this priority should be used..."
                      rows={3}
                    />
                  </div>
                  <div className="flex justify-end gap-2 pt-4">
                    <Button variant="outline" onClick={() => setIsAddingPriority(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleAddPriority} className="bg-primary hover:bg-primary/90">
                      Add Priority
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {priorities.map((priority) => (
              <div key={priority.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div 
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: priority.color }}
                  ></div>
                  <div>
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium text-gray-900">{priority.name}</h4>
                      {priority.isDefault && (
                        <Badge variant="outline" className="text-xs">Default</Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{priority.description}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    checked={priority.isActive}
                    onCheckedChange={() => togglePriorityActive(priority.id)}
                  />
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Edit className="h-4 w-4" />
                  </Button>
                  {!priority.isDefault && (
                    <Button variant="ghost" size="icon" className="h-8 w-8 text-red-600">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* General Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            General Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">Auto-assign New Enquiries</h4>
              <p className="text-sm text-gray-600">Automatically assign new enquiries to available agents</p>
            </div>
            <Switch defaultChecked />
          </div>
          
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">Email Notifications</h4>
              <p className="text-sm text-gray-600">Send email notifications for status changes</p>
            </div>
            <Switch defaultChecked />
          </div>

          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">Require Priority Assignment</h4>
              <p className="text-sm text-gray-600">Make priority selection mandatory for new enquiries</p>
            </div>
            <Switch />
          </div>

          <div className="pt-4">
            <Button className="bg-primary text-gray-50 hover:bg-primary/90">
              <Save className="h-4 w-4 mr-2" />
              Save Configuration
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

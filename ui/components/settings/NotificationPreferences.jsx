"use client";

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { notificationSettings } from "@/lib/types";
import { Mail, Bell, Save, Smartphone, Volume2 } from "lucide-react";

export function NotificationPreferences() {
  const [emailSettings, setEmailSettings] = useState(
    notificationSettings.email
  );
  const [inAppSettings, setInAppSettings] = useState(
    notificationSettings.inApp
  );

  const handleEmailToggle = (setting) => {
    setEmailSettings((prev) => ({
      ...prev,
      [setting]: !prev[setting],
    }));
  };

  const handleInAppToggle = (setting) => {
    setInAppSettings((prev) => ({
      ...prev,
      [setting]: !prev[setting],
    }));
  };

  const handleSave = () => {
    // In a real app, this would make an API call
    console.log("Saving notification preferences:", {
      email: emailSettings,
      inApp: inAppSettings,
    });
  };

  return (
    <div className="space-y-6">
      {/* Email Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Notifications
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="email-new-enquiry">New Enquiry</Label>
              <p className="text-sm text-gray-600">
                Get notified when a new enquiry is submitted
              </p>
            </div>
            <Switch
              id="email-new-enquiry"
              checked={emailSettings.newEnquiry}
              onCheckedChange={() => handleEmailToggle("newEnquiry")}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="email-status-update">Status Updates</Label>
              <p className="text-sm text-gray-600">
                Get notified when enquiry status changes
              </p>
            </div>
            <Switch
              id="email-status-update"
              checked={emailSettings.statusUpdate}
              onCheckedChange={() => handleEmailToggle("statusUpdate")}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="email-assignment">Assignment Notifications</Label>
              <p className="text-sm text-gray-600">
                Get notified when enquiries are assigned to you
              </p>
            </div>
            <Switch
              id="email-assignment"
              checked={emailSettings.assignment}
              onCheckedChange={() => handleEmailToggle("assignment")}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="email-daily-digest">Daily Digest</Label>
              <p className="text-sm text-gray-600">
                Receive a daily summary of your enquiries
              </p>
            </div>
            <Switch
              id="email-daily-digest"
              checked={emailSettings.dailyDigest}
              onCheckedChange={() => handleEmailToggle("dailyDigest")}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="email-weekly-report">Weekly Report</Label>
              <p className="text-sm text-gray-600">
                Receive a weekly performance report
              </p>
            </div>
            <Switch
              id="email-weekly-report"
              checked={emailSettings.weeklyReport}
              onCheckedChange={() => handleEmailToggle("weeklyReport")}
            />
          </div>
        </CardContent>
      </Card>

      {/* In-App Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            In-App Notifications
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="app-new-enquiry">New Enquiry</Label>
              <p className="text-sm text-gray-600">
                Show in-app notifications for new enquiries
              </p>
            </div>
            <Switch
              id="app-new-enquiry"
              checked={inAppSettings.newEnquiry}
              onCheckedChange={() => handleInAppToggle("newEnquiry")}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="app-status-update">Status Updates</Label>
              <p className="text-sm text-gray-600">
                Show notifications for status changes
              </p>
            </div>
            <Switch
              id="app-status-update"
              checked={inAppSettings.statusUpdate}
              onCheckedChange={() => handleInAppToggle("statusUpdate")}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="app-assignment">Assignment Notifications</Label>
              <p className="text-sm text-gray-600">
                Show notifications for new assignments
              </p>
            </div>
            <Switch
              id="app-assignment"
              checked={inAppSettings.assignment}
              onCheckedChange={() => handleInAppToggle("assignment")}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="app-mentions">Mentions</Label>
              <p className="text-sm text-gray-600">
                Show notifications when you're mentioned in notes
              </p>
            </div>
            <Switch
              id="app-mentions"
              checked={inAppSettings.mentions}
              onCheckedChange={() => handleInAppToggle("mentions")}
            />
          </div>
        </CardContent>
      </Card>

      {/* Notification Delivery */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5" />
            Notification Delivery
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="push-notifications">Push Notifications</Label>
              <p className="text-sm text-gray-600">
                Enable browser push notifications
              </p>
            </div>
            <Switch id="push-notifications" defaultChecked />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="sound-notifications">Sound Notifications</Label>
              <p className="text-sm text-gray-600">
                Play sound for important notifications
              </p>
            </div>
            <Switch id="sound-notifications" defaultChecked />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="do-not-disturb">Do Not Disturb</Label>
              <p className="text-sm text-gray-600">
                Pause notifications during focus hours (9 AM - 5 PM)
              </p>
            </div>
            <Switch id="do-not-disturb" />
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          className=" text-gray-50 bg-primary hover:bg-primary/90"
        >
          <Save className="h-4 w-4 mr-2" />
          Save Preferences
        </Button>
      </div>
    </div>
  );
}

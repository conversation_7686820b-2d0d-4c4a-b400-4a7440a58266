"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { UserProfileSettings } from "./UserProfileSettings";
import { TeamManagement } from "./TeamManagement";
import { EnquiryConfiguration } from "./EnquiryConfiguration";
import { NotificationPreferences } from "./NotificationPreferences";
import { ThemeSettings } from "./ThemeSettings";
import { 
  User, 
  Users, 
  Settings, 
  Bell, 
  Palette 
} from "lucide-react";

export function SettingsTabs() {
  return (
    <Tabs defaultValue="profile" className="space-y-6">
      <TabsList className="grid w-full grid-cols-5">
        <TabsTrigger value="profile" className="flex items-center gap-2">
          <User className="h-4 w-4" />
          <span className="hidden sm:inline">Profile</span>
        </TabsTrigger>
        <TabsTrigger value="team" className="flex items-center gap-2">
          <Users className="h-4 w-4" />
          <span className="hidden sm:inline">Team</span>
        </TabsTrigger>
        {/* <TabsTrigger value="enquiry" className="flex items-center gap-2">
          <Settings className="h-4 w-4" />
          <span className="hidden sm:inline">Enquiry</span>
        </TabsTrigger>
        <TabsTrigger value="notifications" className="flex items-center gap-2">
          <Bell className="h-4 w-4" />
          <span className="hidden sm:inline">Notifications</span>
        </TabsTrigger>
        <TabsTrigger value="theme" className="flex items-center gap-2">
          <Palette className="h-4 w-4" />
          <span className="hidden sm:inline">Theme</span>
        </TabsTrigger> */}
      </TabsList>

      <TabsContent value="profile">
        <UserProfileSettings />
      </TabsContent>

      <TabsContent value="team">
        <TeamManagement />
      </TabsContent>

      <TabsContent value="enquiry">
        <EnquiryConfiguration />
      </TabsContent>

      <TabsContent value="notifications">
        <NotificationPreferences />
      </TabsContent>

      <TabsContent value="theme">
        <ThemeSettings />
      </TabsContent>
    </Tabs>
  );
}

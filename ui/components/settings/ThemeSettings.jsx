"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { 
  Palette, 
  Sun, 
  Moon, 
  Monitor,
  Save,
  Eye,
  Zap
} from "lucide-react";

export function ThemeSettings() {
  const [theme, setTheme] = useState("light");
  const [accentColor, setAccentColor] = useState("indigo");
  const [compactMode, setCompactMode] = useState(false);
  const [animations, setAnimations] = useState(true);

  const themeOptions = [
    {
      id: "light",
      name: "Light",
      description: "Clean and bright interface",
      icon: Sun,
      preview: "bg-white border-gray-200"
    },
    {
      id: "dark",
      name: "Dark",
      description: "Easy on the eyes in low light",
      icon: Moon,
      preview: "bg-gray-900 border-gray-700"
    },
    {
      id: "system",
      name: "System",
      description: "Follows your system preference",
      icon: Monitor,
      preview: "bg-gradient-to-r from-white to-gray-900 border-gray-400"
    }
  ];

  const accentColors = [
    { id: "indigo", name: "Indigo", color: "#4F46E5" },
    { id: "blue", name: "Blue", color: "#3B82F6" },
    { id: "emerald", name: "Emerald", color: "#10B981" },
    { id: "purple", name: "Purple", color: "#8B5CF6" },
    { id: "pink", name: "Pink", color: "#EC4899" },
    { id: "orange", name: "Orange", color: "#F97316" }
  ];

  const handleSave = () => {
    // In a real app, this would apply the theme and save preferences
    console.log('Saving theme preferences:', {
      theme,
      accentColor,
      compactMode,
      animations
    });
  };

  return (
    <div className="space-y-6">
      {/* Theme Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Appearance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Label className="text-base font-medium">Theme</Label>
            <RadioGroup value={theme} onValueChange={setTheme} className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {themeOptions.map((option) => {
                const Icon = option.icon;
                return (
                  <div key={option.id} className="relative">
                    <RadioGroupItem
                      value={option.id}
                      id={option.id}
                      className="peer sr-only"
                    />
                    <Label
                      htmlFor={option.id}
                      className="flex flex-col items-center justify-center rounded-lg border-2 border-gray-200 p-4 hover:bg-gray-50 peer-data-[state=checked]:border-primary cursor-pointer"
                    >
                      <div className={`w-16 h-12 rounded-lg border-2 ${option.preview} mb-3`}></div>
                      <Icon className="h-5 w-5 mb-2" />
                      <span className="font-medium">{option.name}</span>
                      <span className="text-sm text-gray-600 text-center">{option.description}</span>
                    </Label>
                  </div>
                );
              })}
            </RadioGroup>
          </div>
        </CardContent>
      </Card>

      {/* Accent Color */}
      <Card>
        <CardHeader>
          <CardTitle>Accent Color</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Label className="text-base font-medium">Choose your accent color</Label>
            <div className="grid grid-cols-3 md:grid-cols-6 gap-3">
              {accentColors.map((color) => (
                <button
                  key={color.id}
                  onClick={() => setAccentColor(color.id)}
                  className={`flex flex-col items-center p-3 rounded-lg border-2 transition-colors ${
                    accentColor === color.id 
                      ? 'border-gray-900 bg-gray-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div 
                    className="w-8 h-8 rounded-full mb-2"
                    style={{ backgroundColor: color.color }}
                  ></div>
                  <span className="text-sm font-medium">{color.name}</span>
                </button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Display Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Display Options
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="compact-mode">Compact Mode</Label>
              <p className="text-sm text-gray-600">
                Reduce spacing and padding for more content
              </p>
            </div>
            <Switch
              id="compact-mode"
              checked={compactMode}
              onCheckedChange={setCompactMode}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="animations">Animations</Label>
              <p className="text-sm text-gray-600">
                Enable smooth transitions and animations
              </p>
            </div>
            <Switch
              id="animations"
              checked={animations}
              onCheckedChange={setAnimations}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="high-contrast">High Contrast</Label>
              <p className="text-sm text-gray-600">
                Increase contrast for better accessibility
              </p>
            </div>
            <Switch id="high-contrast" />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="reduce-motion">Reduce Motion</Label>
              <p className="text-sm text-gray-600">
                Minimize animations for motion sensitivity
              </p>
            </div>
            <Switch id="reduce-motion" />
          </div>
        </CardContent>
      </Card>

      {/* Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Performance
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="auto-refresh">Auto Refresh</Label>
              <p className="text-sm text-gray-600">
                Automatically refresh data every 30 seconds
              </p>
            </div>
            <Switch id="auto-refresh" defaultChecked />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="lazy-loading">Lazy Loading</Label>
              <p className="text-sm text-gray-600">
                Load content as you scroll for better performance
              </p>
            </div>
            <Switch id="lazy-loading" defaultChecked />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="offline-mode">Offline Mode</Label>
              <p className="text-sm text-gray-600">
                Cache data for offline access
              </p>
            </div>
            <Switch id="offline-mode" />
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button onClick={handleSave} className="bg-primary hover:bg-primary/90">
          <Save className="h-4 w-4 mr-2" />
          Apply Theme
        </Button>
      </div>
    </div>
  );
}

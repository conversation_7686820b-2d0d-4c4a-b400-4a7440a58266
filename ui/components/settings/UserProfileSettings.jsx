"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { useAuth } from "@/lib/auth/AuthContext";
import { Camera, Save, Key, Loader2 } from "lucide-react";
import { userProfileAPI, transformFormDataToAPI, transformAPIDataToForm } from "@/lib/api/userProfile";
import { APIKeyManagement } from "./APIKeyManagement";
import toast, { Toaster } from 'react-hot-toast';

export function UserProfileSettings() {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    jobTitle: '',
    department: '',
    bio: '',
    timezone: '',
    language: 'English (US)'
  });

  // Load user profile data on component mount
  useEffect(() => {
    const loadProfile = async () => {
      console.log('[UserProfile] Starting profile load...');
      console.log('[UserProfile] Current user:', {
        hasUser: !!user,
        userId: user?.id,
        email: user?.email,
        userMetadata: user?.user_metadata
      });

      try {
        setInitialLoading(true);
        console.log('[UserProfile] Calling userProfileAPI.getProfile()...');

        // Try to load profile from API
        const profileData = await userProfileAPI.getProfile();
        console.log('[UserProfile] Profile data received:', profileData);

        const transformedData = transformAPIDataToForm(profileData);
        console.log('[UserProfile] Transformed data:', transformedData);

        setFormData(prev => ({ ...prev, ...transformedData }));
        console.log('[UserProfile] Form data updated successfully');

      } catch (error) {
        console.error('[UserProfile] Failed to load profile:', error);
        console.error('[UserProfile] Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });

        toast.error('Failed to load profile data. Using demo data.');

        // Fallback to demo data
        const demoData = {
          fullName: user?.user_metadata?.full_name || 'Demo User',
          email: user?.email || '<EMAIL>',
          phone: '+****************',
          jobTitle: 'CRM Administrator',
          department: 'Sales & Marketing',
          bio: 'Experienced CRM administrator with a passion for optimizing customer relationships and driving business growth.',
          timezone: 'America/New_York',
          language: 'English (US)'
        };
        console.log('[UserProfile] Using demo data:', demoData);
        setFormData(demoData);
      } finally {
        setInitialLoading(false);
        console.log('[UserProfile] Profile load completed');
      }
    };

    if (user) {
      console.log('[UserProfile] User available, loading profile...');
      loadProfile();
    } else {
      console.log('[UserProfile] No user available, skipping profile load');
    }
  }, [user]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    try {
      setLoading(true);

      const apiData = transformFormDataToAPI(formData);
      await userProfileAPI.updateProfile(apiData);

      toast.success('Profile updated successfully!');
      setIsEditing(false);

    } catch (error) {
      console.error('Failed to save profile:', error);
      toast.error(error.message || 'Failed to update profile. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getUserInitials = () => {
    return formData.fullName.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  if (initialLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Loading profile...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Toaster position="top-right" />
      {/* Profile Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Profile Information
            <Button
              variant={isEditing ? "default" : "outline"}
              onClick={() => isEditing ? handleSave() : setIsEditing(true)}
              disabled={loading || initialLoading}
              className={isEditing ? "bg-primary text-gray-50 hover:bg-primary/90" : ""}
            >
              {isEditing ? (
                <>
                  {loading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  {loading ? 'Saving...' : 'Save Changes'}
                </>
              ) : (
                'Edit Profile'
              )}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Avatar Section */}
          <div className="flex items-center gap-4">
            <Avatar className="h-20 w-20">
              {user?.user_metadata?.avatar_url ? (
                <AvatarImage src={user.user_metadata.avatar_url} alt={formData.fullName} />
              ) : null}
              <AvatarFallback className="bg-primary text-white text-lg">
                {getUserInitials()}
              </AvatarFallback>
            </Avatar>
            <div>
              <Button variant="outline" size="sm" disabled={!isEditing}>
                <Camera className="h-4 w-4 mr-2" />
                Change Photo
              </Button>
              <p className="text-xs text-gray-500 mt-1">
                JPG, PNG or GIF. Max size 2MB.
              </p>
            </div>
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="fullName">Full Name</Label>
              <Input
                id="fullName"
                value={formData.fullName}
                disabled={true}
                className="bg-gray-50"
              />
              <p className="text-xs text-gray-500">Name cannot be changed here</p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                disabled={true}
                className="bg-gray-50"
              />
              <p className="text-xs text-gray-500">Email cannot be changed here</p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                disabled={!isEditing}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="jobTitle">Job Title</Label>
              <Input
                id="jobTitle"
                value={formData.jobTitle}
                onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                disabled={!isEditing}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="department">Department</Label>
              <Input
                id="department"
                value={formData.department}
                onChange={(e) => handleInputChange('department', e.target.value)}
                disabled={!isEditing}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="timezone">Timezone</Label>
              <Input
                id="timezone"
                value={formData.timezone}
                onChange={(e) => handleInputChange('timezone', e.target.value)}
                disabled={!isEditing}
              />
            </div>
          </div>

          {/* Bio */}
          <div className="space-y-2">
            <Label htmlFor="bio">Bio</Label>
            <Textarea
              id="bio"
              rows={3}
              value={formData.bio}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              disabled={!isEditing}
              placeholder="Tell us about yourself..."
            />
          </div>
        </CardContent>
      </Card>

      {/* Security Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Security Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">Password</h4>
              <p className="text-sm text-gray-600">Last changed 30 days ago</p>
            </div>
            <Button variant="outline">Change Password</Button>
          </div>
          
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h4 className="font-medium text-gray-900">Two-Factor Authentication</h4>
              <p className="text-sm text-gray-600">Add an extra layer of security</p>
            </div>
            <Button variant="outline">Enable 2FA</Button>
          </div> */}

          {/* API Key Management - Only visible to admins */}
          {user?.role === 'admin' && (
            <APIKeyManagement />
          )}
        </CardContent>
      </Card>
    </div>
  );
}

import { useState, useRef, useEffect } from "react";
import { GripHorizontal } from "lucide-react";

export function ResizableContainer({ 
  children, 
  initialHeight = 384, 
  minHeight = 200, 
  maxHeight = 800,
  className = ""
}) {
  const [height, setHeight] = useState(initialHeight);
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef(null);
  const startY = useRef(0);
  const startHeight = useRef(0);

  const handleMouseDown = (e) => {
    setIsResizing(true);
    startY.current = e.clientY;
    startHeight.current = height;
    
    // Prevent text selection during resize
    document.body.style.userSelect = 'none';
    document.body.style.cursor = 'ns-resize';
  };

  const handleMouseMove = (e) => {
    if (!isResizing) return;
    
    const deltaY = e.clientY - startY.current;
    const newHeight = Math.min(
      Math.max(startHeight.current + deltaY, minHeight),
      maxHeight
    );
    
    setHeight(newHeight);
  };

  const handleMouseUp = () => {
    setIsResizing(false);
    document.body.style.userSelect = '';
    document.body.style.cursor = '';
  };

  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isResizing]);

  return (
    <div 
      ref={containerRef}
      className={`flex flex-col ${className}`}
      style={{ height: `${height}px` }}
    >
      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {children}
      </div>
      
      {/* Resize Handle */}
      <div 
        className="h-3 flex items-center justify-center cursor-ns-resize bg-gray-50 hover:bg-gray-100 border-t border-gray-200 group transition-colors"
        onMouseDown={handleMouseDown}
      >
        <GripHorizontal className="h-3 w-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
      </div>
    </div>
  );
}

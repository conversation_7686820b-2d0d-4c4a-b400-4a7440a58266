"use client";

import { useState, useEffect, useCallback } from 'react';
import { dashboardAPI } from '@/lib/api/dashboard';

/**
 * Custom hook for managing dashboard data
 * Handles API calls, loading states, and error handling
 */
export function useDashboard(options = {}) {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Default options
  const defaultOptions = {
    recentActivitiesLimit: 20, // Increased since it's now the main content
    activitiesDays: 14, // Increased to show more activity history
    ...options
  };

  /**
   * Fetch dashboard data from API
   */
  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('[useDashboard] Fetching dashboard data with options:', defaultOptions);

      const data = await dashboardAPI.getDashboardOverview(defaultOptions);
      
      console.log('[useDashboard] Dashboard data received:', data);
      setDashboardData(data);

    } catch (err) {
      console.error('[useDashboard] Failed to fetch dashboard data:', err);
      setError(err.message || 'Failed to load dashboard data');
      setDashboardData(null);
    } finally {
      setLoading(false);
    }
  }, [defaultOptions.recentActivitiesLimit, defaultOptions.activitiesDays]);

  /**
   * Refresh dashboard data
   */
  const refresh = useCallback(() => {
    console.log('[useDashboard] Refreshing dashboard data');
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Fetch data on mount and when options change
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  // Extract data for easier access
  const stats = dashboardData?.stats || null;
  const recentEnquiries = dashboardData?.recent_enquiries || [];
  const recentActivities = dashboardData?.recent_activities || [];

  return {
    // Data
    dashboardData,
    stats,
    recentEnquiries,
    recentActivities,
    
    // States
    loading,
    error,
    
    // Actions
    refresh
  };
}

import { useState, useEffect, useCallback } from 'react';
import { enquiryAPI } from '@/lib/api/enquiries';

/**
 * Custom hook for managing enquiry data and filters
 * Handles API calls, loading states, and error handling
 */
export function useEnquiries() {
  const [enquiries, setEnquiries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0
  });

  // Filter state
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    priority: '',
    assigned_to_id: ''
  });

  /**
   * Fetch enquiries from API with current filters and pagination
   */
  const fetchEnquiries = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('[useEnquiries] Fetching enquiries with filters:', filters);
      console.log('[useEnquiries] Pagination:', pagination);

      // Prepare API filters (only include non-empty values)
      const apiFilters = {
        page: pagination.page,
        limit: pagination.limit
      };

      if (filters.search?.trim()) {
        apiFilters.search = filters.search.trim();
      }
      if (filters.status && filters.status !== 'all') {
        apiFilters.status = filters.status;
      }
      if (filters.priority && filters.priority !== 'all') {
        apiFilters.priority = filters.priority;
      }
      if (filters.assigned_to_id && filters.assigned_to_id !== 'all') {
        apiFilters.assigned_to_id = filters.assigned_to_id;
      }

      const response = await enquiryAPI.getEnquiries(apiFilters);
      
      console.log('[useEnquiries] API response:', response);

      setEnquiries(response.data || []);
      setPagination(prev => ({
        ...prev,
        total: response.total || 0
      }));

    } catch (err) {
      console.error('[useEnquiries] Failed to fetch enquiries:', err);
      setError(err.message || 'Failed to load enquiries');
      setEnquiries([]);
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.page, pagination.limit]);

  /**
   * Update filters and reset to first page
   */
  const updateFilters = useCallback((newFilters) => {
    console.log('[useEnquiries] Updating filters:', newFilters);
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
  }, []);

  /**
   * Clear all filters
   */
  const clearFilters = useCallback(() => {
    console.log('[useEnquiries] Clearing all filters');
    setFilters({
      search: '',
      status: '',
      priority: '',
      assigned_to_id: ''
    });
    setPagination(prev => ({ ...prev, page: 1 }));
  }, []);

  /**
   * Change page
   */
  const changePage = useCallback((newPage) => {
    console.log('[useEnquiries] Changing to page:', newPage);
    setPagination(prev => ({ ...prev, page: newPage }));
  }, []);

  /**
   * Change page size
   */
  const changePageSize = useCallback((newLimit) => {
    console.log('[useEnquiries] Changing page size to:', newLimit);
    setPagination(prev => ({ 
      ...prev, 
      limit: newLimit, 
      page: 1 // Reset to first page when changing page size
    }));
  }, []);

  /**
   * Delete enquiry from local state (optimistic update)
   */
  const deleteEnquiry = useCallback((enquiryId) => {
    console.log('[useEnquiries] Removing enquiry from local state:', enquiryId);
    setEnquiries(prev => prev.filter(enquiry => enquiry.id !== enquiryId));
    setPagination(prev => ({
      ...prev,
      total: Math.max(0, prev.total - 1)
    }));
  }, []);

  /**
   * Refresh data (useful after creating/updating enquiries)
   */
  const refresh = useCallback(() => {
    console.log('[useEnquiries] Refreshing enquiries data');
    fetchEnquiries();
  }, [fetchEnquiries]);

  // Fetch data when filters or pagination change
  useEffect(() => {
    fetchEnquiries();
  }, [fetchEnquiries]);

  return {
    // Data
    enquiries,
    loading,
    error,

    // Pagination
    pagination,
    changePage,
    changePageSize,

    // Filters
    filters,
    updateFilters,
    clearFilters,

    // Actions
    refresh,
    deleteEnquiry
  };
}

import { useState, useEffect, useCallback } from 'react';
import { enquiryAPI } from '@/lib/api/enquiries';

/**
 * Custom hook for managing enquiry details data
 * Handles API calls for enquiry, notes, and activities
 */
export function useEnquiryDetails(slug) {
  const [enquiry, setEnquiry] = useState(null);
  const [notes, setNotes] = useState([]);
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [notesLoading, setNotesLoading] = useState(false);
  const [activitiesLoading, setActivitiesLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Fetch enquiry details by slug
   */
  const fetchEnquiry = useCallback(async () => {
    if (!slug) return;

    try {
      setLoading(true);
      setError(null);

      console.log('[useEnquiryDetails] Fetching enquiry by slug:', slug);
      const enquiryData = await enquiryAPI.getEnquiryBySlug(slug);

      console.log('[useEnquiryDetails] Enquiry data received:', enquiryData);

      // Automatically mark as read if it's currently unread
      if (enquiryData && enquiryData.is_read === false) {
        try {
          console.log('[useEnquiryDetails] Marking enquiry as read:', enquiryData.id);
          const updatedEnquiry = await enquiryAPI.markAsRead(enquiryData.id);
          setEnquiry(updatedEnquiry);
        } catch (err) {
          console.error('[useEnquiryDetails] Failed to mark as read:', err);
          // Still set the original enquiry data even if marking as read fails
          setEnquiry(enquiryData);
        }
      } else {
        setEnquiry(enquiryData);
      }

    } catch (err) {
      console.error('[useEnquiryDetails] Failed to fetch enquiry:', err);
      setError(err.message || 'Failed to load enquiry details');
      setEnquiry(null);
    } finally {
      setLoading(false);
    }
  }, [slug]);

  /**
   * Fetch notes for the enquiry
   */
  const fetchNotes = useCallback(async () => {
    if (!enquiry?.id) return;

    try {
      setNotesLoading(true);
      console.log('[useEnquiryDetails] Fetching notes for enquiry:', enquiry.id);
      
      const notesData = await enquiryAPI.getNotes(enquiry.id);
      console.log('[useEnquiryDetails] Notes data received:', notesData);
      
      setNotes(Array.isArray(notesData) ? notesData : []);

    } catch (err) {
      console.error('[useEnquiryDetails] Failed to fetch notes:', err);
      setNotes([]);
    } finally {
      setNotesLoading(false);
    }
  }, [enquiry?.id]);

  /**
   * Fetch activities for the enquiry
   */
  const fetchActivities = useCallback(async () => {
    if (!enquiry?.id) return;

    try {
      setActivitiesLoading(true);
      console.log('[useEnquiryDetails] Fetching activities for enquiry:', enquiry.id);
      
      const activitiesData = await enquiryAPI.getActivities(enquiry.id);
      console.log('[useEnquiryDetails] Activities data received:', activitiesData);
      
      setActivities(Array.isArray(activitiesData) ? activitiesData : []);

    } catch (err) {
      console.error('[useEnquiryDetails] Failed to fetch activities:', err);
      setActivities([]);
    } finally {
      setActivitiesLoading(false);
    }
  }, [enquiry?.id]);

  /**
   * Add a new note
   */
  const addNote = useCallback(async (content) => {
    if (!enquiry?.id || !content?.trim()) return;

    try {
      console.log('[useEnquiryDetails] Adding note:', content);
      
      // Note: user_id should come from auth context, but for now we'll let backend handle it
      const noteData = {
        content: content.trim()
      };

      const newNote = await enquiryAPI.createNote(enquiry.id, noteData);
      console.log('[useEnquiryDetails] Note added successfully:', newNote);
      
      // Refresh notes and activities to show the new note and activity
      await Promise.all([fetchNotes(), fetchActivities()]);
      
      return newNote;

    } catch (err) {
      console.error('[useEnquiryDetails] Failed to add note:', err);
      throw err;
    }
  }, [enquiry?.id, fetchNotes, fetchActivities]);

  /**
   * Delete a note
   */
  const deleteNote = useCallback(async (noteId) => {
    if (!enquiry?.id || !noteId) return;

    try {
      console.log('[useEnquiryDetails] Deleting note:', noteId);
      
      await enquiryAPI.deleteNote(enquiry.id, noteId);
      console.log('[useEnquiryDetails] Note deleted successfully');
      
      // Refresh notes and activities to reflect the deletion
      await Promise.all([fetchNotes(), fetchActivities()]);

    } catch (err) {
      console.error('[useEnquiryDetails] Failed to delete note:', err);
      throw err;
    }
  }, [enquiry?.id, fetchNotes, fetchActivities]);

  /**
   * Update enquiry
   */
  const updateEnquiry = useCallback(async (updateData) => {
    if (!enquiry?.id) return;

    try {
      console.log('[useEnquiryDetails] Updating enquiry:', enquiry.id, updateData);

      const updatedEnquiry = await enquiryAPI.updateEnquiry(enquiry.id, updateData);
      console.log('[useEnquiryDetails] Enquiry updated successfully:', updatedEnquiry);

      // Update local state
      setEnquiry(updatedEnquiry);

      // Refresh activities to show the update activity
      await fetchActivities();

      return updatedEnquiry;

    } catch (err) {
      console.error('[useEnquiryDetails] Failed to update enquiry:', err);
      throw err;
    }
  }, [enquiry?.id, fetchActivities]);

  /**
   * Mark enquiry as read
   */
  const markAsRead = useCallback(async () => {
    if (!enquiry?.id) return;

    try {
      console.log('[useEnquiryDetails] Marking enquiry as read:', enquiry.id);
      const updatedEnquiry = await enquiryAPI.markAsRead(enquiry.id);
      setEnquiry(updatedEnquiry);
      return updatedEnquiry;
    } catch (err) {
      console.error('[useEnquiryDetails] Failed to mark as read:', err);
      throw err;
    }
  }, [enquiry?.id]);

  /**
   * Mark enquiry as unread
   */
  const markAsUnread = useCallback(async () => {
    if (!enquiry?.id) return;

    try {
      console.log('[useEnquiryDetails] Marking enquiry as unread:', enquiry.id);
      const updatedEnquiry = await enquiryAPI.markAsUnread(enquiry.id);
      setEnquiry(updatedEnquiry);
      return updatedEnquiry;
    } catch (err) {
      console.error('[useEnquiryDetails] Failed to mark as unread:', err);
      throw err;
    }
  }, [enquiry?.id]);

  /**
   * Delete enquiry
   */
  const deleteEnquiry = useCallback(async () => {
    if (!enquiry?.id) return;

    try {
      console.log('[useEnquiryDetails] Deleting enquiry:', enquiry.id);
      await enquiryAPI.deleteEnquiry(enquiry.id);
      console.log('[useEnquiryDetails] Enquiry deleted successfully');
    } catch (err) {
      console.error('[useEnquiryDetails] Failed to delete enquiry:', err);
      throw err; // Re-throw to let component handle the error
    }
  }, [enquiry?.id]);

  /**
   * Refresh all data
   */
  const refresh = useCallback(async () => {
    console.log('[useEnquiryDetails] Refreshing all data');
    await fetchEnquiry();
  }, [fetchEnquiry]);

  // Fetch enquiry when slug changes
  useEffect(() => {
    fetchEnquiry();
  }, [fetchEnquiry]);

  // Fetch notes and activities when enquiry is loaded
  useEffect(() => {
    if (enquiry?.id) {
      Promise.all([fetchNotes(), fetchActivities()]);
    }
  }, [enquiry?.id, fetchNotes, fetchActivities]);

  return {
    // Data
    enquiry,
    notes,
    activities,

    // Loading states
    loading,
    notesLoading,
    activitiesLoading,
    error,

    // Actions
    addNote,
    deleteNote,
    updateEnquiry,
    deleteEnquiry,
    markAsRead,
    markAsUnread,
    refresh,

    // Manual fetch functions (if needed)
    fetchNotes,
    fetchActivities
  };
}

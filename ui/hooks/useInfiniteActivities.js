"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { dashboardAPI } from '@/lib/api/dashboard';

/**
 * Custom hook for infinite scroll activities
 * Handles pagination, loading states, and infinite scroll behavior
 */
export function useInfiniteActivities(options = {}) {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState(null);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  
  // Default options
  const defaultOptions = {
    limit: 20,
    days: 90,
    ...options
  };

  // Ref to track if we're currently fetching to prevent duplicate requests
  const fetchingRef = useRef(false);

  // Load initial activities
  const loadInitialActivities = useCallback(async () => {
    if (fetchingRef.current) return;
    
    try {
      fetchingRef.current = true;
      setLoading(true);
      setError(null);
      
      const response = await dashboardAPI.getActivities({
        page: 1,
        limit: defaultOptions.limit,
        days: defaultOptions.days
      });
      
      setActivities(response.activities || []);
      setHasMore(response.pagination?.has_more || false);
      setPage(1);
      
    } catch (err) {
      console.error('[useInfiniteActivities] Failed to load initial activities:', err);
      setError(err.message || 'Failed to load activities');
      setActivities([]);
    } finally {
      setLoading(false);
      fetchingRef.current = false;
    }
  }, [defaultOptions.limit, defaultOptions.days]);

  // Load more activities for infinite scroll
  const loadMoreActivities = useCallback(async () => {
    if (fetchingRef.current || !hasMore || loadingMore) return;
    
    try {
      fetchingRef.current = true;
      setLoadingMore(true);
      setError(null);
      
      const nextPage = page + 1;
      const response = await dashboardAPI.getActivities({
        page: nextPage,
        limit: defaultOptions.limit,
        days: defaultOptions.days
      });
      
      // Append new activities to existing ones
      setActivities(prev => [...prev, ...(response.activities || [])]);
      setHasMore(response.pagination?.has_more || false);
      setPage(nextPage);
      
    } catch (err) {
      console.error('[useInfiniteActivities] Failed to load more activities:', err);
      setError(err.message || 'Failed to load more activities');
    } finally {
      setLoadingMore(false);
      fetchingRef.current = false;
    }
  }, [page, hasMore, loadingMore, defaultOptions.limit, defaultOptions.days]);

  // Refresh activities (reset to first page)
  const refreshActivities = useCallback(async () => {
    setPage(1);
    setHasMore(true);
    await loadInitialActivities();
  }, [loadInitialActivities]);

  // Load initial data on mount
  useEffect(() => {
    loadInitialActivities();
  }, [loadInitialActivities]);

  return {
    activities,
    loading,
    loadingMore,
    error,
    hasMore,
    loadMoreActivities,
    refreshActivities,
  };
}

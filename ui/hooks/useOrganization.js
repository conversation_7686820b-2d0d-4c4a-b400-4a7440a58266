"use client";

import { useOrganization as useOrgContext } from '@/lib/context/OrganizationContext';

/**
 * Hook for organization-aware navigation and utilities
 */
export const useOrganization = () => {
  return useOrgContext();
};

/**
 * Hook for generating organization-aware navigation items
 */
export const useOrgNavigation = () => {
  const { getOrgUrl, orgSlug } = useOrganization();

  const navigationItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: 'LayoutDashboard',
      href: getOrgUrl('/')
    },
    {
      id: 'enquiries',
      label: 'Enquiries',
      icon: 'MessageSquare',
      href: getOrgUrl('/enquiries')
    },
    // {
    //   id: 'notes',
    //   label: 'Notes',
    //   icon: 'FileText',
    //   href: getOrgUrl('/notes')
    // },
    {
      id: 'settings',
      label: 'Settings',
      icon: 'Settings',
      href: getOrgUrl('/settings')
    }
  ];

  return {
    navigationItems,
    orgSlug,
    getOrgUrl
  };
};

import { useState, useEffect, useCallback } from 'react';

/**
 * Custom hook for managing unread enquiry counts
 * @returns {Object} Hook state and functions
 */
export const useUnreadCounts = () => {
  const [counts, setCounts] = useState({
    unread_genuine: 0,
    unread_spam: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  /**
   * Fetch unread counts from API
   */
  const fetchCounts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const { enquiryAPI } = await import('@/lib/api/enquiries');
      const data = await enquiryAPI.getUnreadCounts();

      console.log('[useUnreadCounts] Received counts data:', data);
      setCounts(data);
    } catch (err) {
      console.error('[useUnreadCounts] Failed to fetch counts:', err);
      setError(err.message || 'Failed to fetch unread counts');
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Refresh counts (alias for fetchCounts for consistency with other hooks)
   */
  const refresh = useCallback(() => {
    return fetchCounts();
  }, [fetchCounts]);

  // Initial fetch on mount
  useEffect(() => {
    fetchCounts();
  }, [fetchCounts]);

  return {
    counts,
    loading,
    error,
    refresh,
    fetchCounts,
  };
};

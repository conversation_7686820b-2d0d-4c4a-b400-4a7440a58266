/**
 * API Key Management Service
 */

import { getAuthHeaders } from './utils';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

/**
 * Get organization's API key and form ID (admin only)
 */
export async function getAPIKey() {
  try {
    const headers = await getAuthHeaders();

    const response = await fetch(`${API_BASE_URL}/api/v1/organizations/api-key`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // Handle specific error cases
      if (response.status === 403) {
        throw new Error('Only administrators can view API keys');
      } else if (response.status === 404) {
        throw new Error('Organization not found');
      } else if (response.status === 401) {
        throw new Error('Authentication required. Please log in again.');
      }

      throw new Error(errorData.message || `Failed to fetch API key: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching API key:', error);
    throw error;
  }
}

/**
 * Regenerate organization's API key (admin only)
 */
export async function regenerateAPIKey() {
  try {
    const headers = await getAuthHeaders();

    const response = await fetch(`${API_BASE_URL}/api/v1/organizations/api-key/regenerate`, {
      method: 'POST',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // Handle specific error cases
      if (response.status === 403) {
        throw new Error('Only administrators can regenerate API keys');
      } else if (response.status === 404) {
        throw new Error('Organization not found');
      } else if (response.status === 401) {
        throw new Error('Authentication required. Please log in again.');
      }

      throw new Error(errorData.message || `Failed to regenerate API key: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error regenerating API key:', error);
    throw error;
  }
}

/**
 * Regenerate organization's form ID (admin only)
 */
export async function regenerateFormID() {
  try {
    const headers = await getAuthHeaders();

    const response = await fetch(`${API_BASE_URL}/api/v1/organizations/form-id/regenerate`, {
      method: 'POST',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      // Handle specific error cases
      if (response.status === 403) {
        throw new Error('Only administrators can regenerate form IDs');
      } else if (response.status === 404) {
        throw new Error('Organization not found');
      } else if (response.status === 401) {
        throw new Error('Authentication required. Please log in again.');
      }

      throw new Error(errorData.message || `Failed to regenerate form ID: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error regenerating form ID:', error);
    throw error;
  }
}

/**
 * Copy text to clipboard
 */
export function copyToClipboard(text) {
  if (navigator.clipboard && window.isSecureContext) {
    return navigator.clipboard.writeText(text);
  } else {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    return new Promise((resolve, reject) => {
      if (document.execCommand('copy')) {
        textArea.remove();
        resolve();
      } else {
        textArea.remove();
        reject(new Error('Failed to copy to clipboard'));
      }
    });
  }
}

/**
 * Dashboard API Service
 * 
 * This module provides API functions for dashboard data with authentication.
 * All endpoints require JWT authentication via Authorization header.
 */

import { makeOrganizationValidatedRequest, handleResponse } from './utils';

// Using shared utilities from utils.js for organization validation

/**
 * Dashboard API endpoints
 */
export const dashboardAPI = {
  /**
   * Get dashboard overview data
   * @param {Object} options - Query options
   * @param {number} options.recentEnquiriesLimit - Limit for recent enquiries (default: 10, max: 50)
   * @param {number} options.recentActivitiesLimit - Limit for recent activities (default: 10, max: 50)
   * @param {number} options.activitiesDays - Days to look back for activities (default: 7, max: 90)
   * @param {string} orgSlug - Organization slug for validation
   * @returns {Promise<Object>} Dashboard overview data
   */
  async getDashboardOverview(options = {}, orgSlug = null) {
    console.log('[DashboardAPI] Starting getDashboardOverview request...');
    console.log('[DashboardAPI] Options:', options);

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (options.recentEnquiriesLimit) {
      queryParams.append('recent_enquiries_limit', options.recentEnquiriesLimit.toString());
    }
    if (options.recentActivitiesLimit) {
      queryParams.append('recent_activities_limit', options.recentActivitiesLimit.toString());
    }
    if (options.activitiesDays) {
      queryParams.append('activities_days', options.activitiesDays.toString());
    }

    const endpoint = `/api/v1/dashboard/overview?${queryParams.toString()}`;
    console.log('[DashboardAPI] Making organization-validated request...');

    const response = await makeOrganizationValidatedRequest(endpoint, {
      method: 'GET',
    }, orgSlug);

    const data = await handleResponse(response);
    console.log('[DashboardAPI] getDashboardOverview successful');
    console.log('[DashboardAPI] Response data:', data);
    return data;
  },

  /**
   * Get paginated activities for infinite scroll
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (default: 1)
   * @param {number} options.limit - Items per page (default: 20, max: 100)
   * @param {number} options.days - Days to look back (default: 90, max: 365)
   * @param {string} orgSlug - Organization slug for validation
   * @returns {Promise<Object>} Paginated activities data
   */
  async getActivities(options = {}, orgSlug = null) {
    console.log('[DashboardAPI] Starting getActivities request...');
    console.log('[DashboardAPI] Options:', options);

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (options.page) {
      queryParams.append('page', options.page.toString());
    }
    if (options.limit) {
      queryParams.append('limit', options.limit.toString());
    }
    if (options.days) {
      queryParams.append('days', options.days.toString());
    }

    const endpoint = `/api/v1/dashboard/activities?${queryParams.toString()}`;
    console.log('[DashboardAPI] Making organization-validated request...');

    const response = await makeOrganizationValidatedRequest(endpoint, {
      method: 'GET',
    }, orgSlug);

    const data = await handleResponse(response);
    console.log('[DashboardAPI] getActivities successful');
    console.log('[DashboardAPI] Response data:', data);
    return data;
  }
};

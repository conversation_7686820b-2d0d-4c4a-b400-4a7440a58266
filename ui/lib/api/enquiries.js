/**
 * Enquiry Management API
 * 
 * This module provides API functions for managing enquiries with authentication.
 * All endpoints require JWT authentication via Authorization header.
 */

import { createClient } from '@/lib/supabase/client';
import { makeOrganizationValidatedRequest, handleResponse, validateOrganizationContext } from './utils';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

/**
 * Get authentication headers with JWT token
 * @returns {Promise<Object>} Headers object with Authorization
 */
async function getAuthHeaders() {
  const supabase = createClient();
  const { data } = await supabase.auth.getSession();
  const token = data?.session?.access_token;
  
  if (!token) {
    throw new Error('No authentication token available');
  }

  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
}

/**
 * Enquiry API endpoints
 */
export const enquiryAPI = {
  /**
   * Get all enquiries (role-based access)
   * @param {Object} filters - Query filters
   * @param {string} orgSlug - Organization slug for validation
   * @returns {Promise<Object>} Enquiries data with pagination
   */
  async getEnquiries(filters = {}, orgSlug = null) {
    console.log('[EnquiryAPI] Starting getEnquiries request...');

    // Build query parameters
    const queryParams = new URLSearchParams();
    if (filters.search) queryParams.append('search', filters.search);
    if (filters.status) queryParams.append('status', filters.status);
    if (filters.priority) queryParams.append('priority', filters.priority);
    if (filters.assigned_to_id) queryParams.append('assigned_to_id', filters.assigned_to_id);
    if (filters.page) queryParams.append('page', filters.page.toString());
    if (filters.limit) queryParams.append('limit', filters.limit.toString());

    const endpoint = `/api/v1/enquiries?${queryParams.toString()}`;
    console.log('[EnquiryAPI] Making organization-validated request...');

    const response = await makeOrganizationValidatedRequest(endpoint, {
      method: 'GET',
    }, orgSlug);

    const data = await handleResponse(response);
    console.log('[EnquiryAPI] getEnquiries successful');
    return data;
  },

  /**
   * Get single enquiry by ID
   * @param {string} id - Enquiry ID
   * @param {string} orgSlug - Organization slug for validation
   * @returns {Promise<Object>} Enquiry data
   */
  async getEnquiry(id, orgSlug = null) {
    console.log('[EnquiryAPI] Starting getEnquiry request for ID:', id);

    const endpoint = `/api/v1/enquiries/${id}`;
    console.log('[EnquiryAPI] Making organization-validated request...');

    const response = await makeOrganizationValidatedRequest(endpoint, {
      method: 'GET',
    }, orgSlug);

    const data = await handleResponse(response);
    console.log('[EnquiryAPI] getEnquiry successful');
    return data;
  },

  /**
   * Get single enquiry by slug
   * @param {string} slug - Enquiry slug
   * @param {string} orgSlug - Organization slug for validation
   * @returns {Promise<Object>} Enquiry data
   */
  async getEnquiryBySlug(slug, orgSlug = null) {
    console.log('[EnquiryAPI] Starting getEnquiryBySlug request for slug:', slug);

    const endpoint = `/api/v1/enquiries/slug/${slug}`;
    console.log('[EnquiryAPI] Making organization-validated request...');

    const response = await makeOrganizationValidatedRequest(endpoint, {
      method: 'GET',
    }, orgSlug);

    const data = await handleResponse(response);
    console.log('[EnquiryAPI] getEnquiryBySlug successful');
    return data;
  },

  /**
   * Create new enquiry
   * @param {Object} enquiryData - Enquiry data
   * @param {string} orgSlug - Organization slug for validation
   * @returns {Promise<Object>} Created enquiry
   */
  async createEnquiry(enquiryData, orgSlug = null) {
    console.log('[EnquiryAPI] Starting createEnquiry request...');
    console.log('[EnquiryAPI] Enquiry data:', enquiryData);

    const endpoint = `/api/v1/enquiries`;
    console.log('[EnquiryAPI] Making organization-validated request...');

    const response = await makeOrganizationValidatedRequest(endpoint, {
      method: 'POST',
      body: JSON.stringify(enquiryData),
    }, orgSlug);

    const data = await handleResponse(response);
    console.log('[EnquiryAPI] createEnquiry successful');
    return data;
  },

  /**
   * Update enquiry
   * @param {string} id - Enquiry ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated enquiry
   */
  async updateEnquiry(id, updateData) {
    console.log('[EnquiryAPI] Starting updateEnquiry request for ID:', id);
    console.log('[EnquiryAPI] Update data:', updateData);

    const url = `${API_BASE_URL}/api/v1/enquiries/${id}`;
    console.log('[EnquiryAPI] Request URL:', url);

    const headers = await getAuthHeaders();
    console.log('[EnquiryAPI] Making fetch request...');

    const response = await fetch(url, {
      method: 'PUT',
      headers,
      body: JSON.stringify(updateData),
    });

    const data = await handleResponse(response);
    console.log('[EnquiryAPI] updateEnquiry successful');
    return data;
  },

  /**
   * Mark enquiry as read using dedicated endpoint
   * @param {string} id - Enquiry ID
   * @returns {Promise<Object>} Updated enquiry
   */
  async markAsRead(id) {
    console.log('[EnquiryAPI] Marking enquiry as read:', id);

    const url = `${API_BASE_URL}/api/v1/enquiries/${id}/mark-read`;
    console.log('[EnquiryAPI] Request URL:', url);

    const headers = await getAuthHeaders();
    console.log('[EnquiryAPI] Making fetch request...');

    const response = await fetch(url, {
      method: 'PUT',
      headers,
    });

    const data = await handleResponse(response);
    console.log('[EnquiryAPI] markAsRead successful');
    return data;
  },

  /**
   * Mark enquiry as unread using dedicated endpoint
   * @param {string} id - Enquiry ID
   * @returns {Promise<Object>} Updated enquiry
   */
  async markAsUnread(id) {
    console.log('[EnquiryAPI] Marking enquiry as unread:', id);

    const url = `${API_BASE_URL}/api/v1/enquiries/${id}/mark-unread`;
    console.log('[EnquiryAPI] Request URL:', url);

    const headers = await getAuthHeaders();
    console.log('[EnquiryAPI] Making fetch request...');

    const response = await fetch(url, {
      method: 'PUT',
      headers,
    });

    const data = await handleResponse(response);
    console.log('[EnquiryAPI] markAsUnread successful');
    return data;
  },

  /**
   * Delete enquiry (admin only)
   * @param {string} id - Enquiry ID
   * @returns {Promise<Object>} Success message
   */
  async deleteEnquiry(id) {
    console.log('[EnquiryAPI] Starting deleteEnquiry request for ID:', id);

    const url = `${API_BASE_URL}/api/v1/enquiries/${id}`;
    console.log('[EnquiryAPI] Request URL:', url);

    const headers = await getAuthHeaders();
    console.log('[EnquiryAPI] Making fetch request...');

    const response = await fetch(url, {
      method: 'DELETE',
      headers,
    });

    const data = await handleResponse(response);
    console.log('[EnquiryAPI] deleteEnquiry successful');
    return data;
  },

  /**
   * Delete enquiry (admin only)
   * @param {string} id - Enquiry ID
   * @returns {Promise<Object>} Success response
   */
  async deleteEnquiry(id) {
    console.log('[EnquiryAPI] Starting deleteEnquiry request for ID:', id);

    const url = `${API_BASE_URL}/api/v1/enquiries/${id}`;
    console.log('[EnquiryAPI] Request URL:', url);

    const headers = await getAuthHeaders();
    console.log('[EnquiryAPI] Making fetch request...');

    const response = await fetch(url, {
      method: 'DELETE',
      headers,
    });

    const data = await handleResponse(response);
    console.log('[EnquiryAPI] deleteEnquiry successful');
    return data;
  },

  /**
   * Get notes for an enquiry
   * @param {string} enquiryId - Enquiry ID
   * @returns {Promise<Array>} Notes array
   */
  async getNotes(enquiryId) {
    console.log('[EnquiryAPI] Starting getNotes request for enquiry:', enquiryId);
    const url = `${API_BASE_URL}/api/v1/enquiries/${enquiryId}/notes`;
    console.log('[EnquiryAPI] Request URL:', url);

    const headers = await getAuthHeaders();
    console.log('[EnquiryAPI] Making fetch request...');

    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    const data = await handleResponse(response);
    console.log('[EnquiryAPI] getNotes successful');
    return data;
  },

  /**
   * Create a new note for an enquiry
   * @param {string} enquiryId - Enquiry ID
   * @param {Object} noteData - Note data
   * @returns {Promise<Object>} Created note
   */
  async createNote(enquiryId, noteData) {
    console.log('[EnquiryAPI] Starting createNote request for enquiry:', enquiryId);
    console.log('[EnquiryAPI] Note data:', noteData);

    const url = `${API_BASE_URL}/api/v1/enquiries/${enquiryId}/notes`;
    console.log('[EnquiryAPI] Request URL:', url);

    const headers = await getAuthHeaders();
    console.log('[EnquiryAPI] Making fetch request...');

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(noteData),
    });

    const data = await handleResponse(response);
    console.log('[EnquiryAPI] createNote successful');
    return data;
  },

  /**
   * Delete a note from an enquiry
   * @param {string} enquiryId - Enquiry ID
   * @param {string} noteId - Note ID
   * @returns {Promise<Object>} Success response
   */
  async deleteNote(enquiryId, noteId) {
    console.log('[EnquiryAPI] Starting deleteNote request for enquiry:', enquiryId, 'note:', noteId);

    const url = `${API_BASE_URL}/api/v1/enquiries/${enquiryId}/notes/${noteId}`;
    console.log('[EnquiryAPI] Request URL:', url);

    const headers = await getAuthHeaders();
    console.log('[EnquiryAPI] Making fetch request...');

    const response = await fetch(url, {
      method: 'DELETE',
      headers,
    });

    const data = await handleResponse(response);
    console.log('[EnquiryAPI] deleteNote successful');
    return data;
  },

  /**
   * Get activities for an enquiry
   * @param {string} enquiryId - Enquiry ID
   * @returns {Promise<Array>} Activities array
   */
  async getActivities(enquiryId) {
    console.log('[EnquiryAPI] Starting getActivities request for enquiry:', enquiryId);
    const url = `${API_BASE_URL}/api/v1/enquiries/${enquiryId}/activities`;
    console.log('[EnquiryAPI] Request URL:', url);

    const headers = await getAuthHeaders();
    console.log('[EnquiryAPI] Making fetch request...');

    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    const data = await handleResponse(response);
    console.log('[EnquiryAPI] getActivities successful');
    return data;
  },

  /**
   * Get unread counts for genuine and spam enquiries
   * @returns {Promise<Object>} Unread counts
   */
  async getUnreadCounts() {
    console.log('[EnquiryAPI] Starting getUnreadCounts request...');

    const url = `${API_BASE_URL}/api/v1/enquiries/counts?t=${Date.now()}`;
    console.log('[EnquiryAPI] Request URL:', url);

    const headers = await getAuthHeaders();
    console.log('[EnquiryAPI] Making fetch request...');

    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    const data = await handleResponse(response);
    console.log('[EnquiryAPI] getUnreadCounts successful, data:', data);
    return data;
  }
};

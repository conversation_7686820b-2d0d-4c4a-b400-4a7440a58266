/**
 * Invitation API Service
 * Handles invitation verification and acceptance for member onboarding
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

/**
 * Invitation API endpoints
 */
export const invitationAPI = {
  /**
   * Verify invitation token
   * @param {string} token - Invitation token
   * @returns {Promise<Object>} Verification result
   */
  async verifyInvitation(token) {
    console.log('[InvitationAPI] Starting verifyInvitation request with token:', token?.substring(0, 8) + '...');
    
    if (!token) {
      throw new Error('Invitation token is required');
    }

    const url = `${API_BASE_URL}/api/v1/invitations/verify?token=${encodeURIComponent(token)}`;
    console.log('[InvitationAPI] Request URL:', url);

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('[InvitationAPI] Response status:', response.status);

      const data = await response.json();
      console.log('[InvitationAPI] Response data:', data);

      // Return the data regardless of status - let the component handle different states
      return {
        status: response.status,
        ...data
      };
    } catch (error) {
      console.error('[InvitationAPI] Network error:', error);
      throw new Error('Network error. Please check your connection and try again.');
    }
  },

  /**
   * Accept invitation
   * @param {Object} acceptData - Acceptance data
   * @param {string} acceptData.token - Invitation token
   * @param {string} acceptData.name - User's full name
   * @param {string} [acceptData.password] - Password (optional for Google signin)
   * @returns {Promise<Object>} Acceptance result
   */
  async acceptInvitation(acceptData) {
    console.log('[InvitationAPI] Starting acceptInvitation request');
    
    if (!acceptData.token) {
      throw new Error('Invitation token is required');
    }

    if (!acceptData.name) {
      throw new Error('Name is required');
    }

    const url = `${API_BASE_URL}/api/v1/invitations/accept`;
    console.log('[InvitationAPI] Request URL:', url);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(acceptData),
      });

      console.log('[InvitationAPI] Response status:', response.status);

      const data = await response.json();
      console.log('[InvitationAPI] Response data:', data);

      if (!response.ok) {
        // Extract error message from response
        const errorMessage = data.message || `HTTP error! status: ${response.status}`;
        throw new Error(errorMessage);
      }

      return data;
    } catch (error) {
      console.error('[InvitationAPI] Error:', error);
      
      if (error.message.includes('fetch')) {
        throw new Error('Network error. Please check your connection and try again.');
      }
      
      throw error;
    }
  },

  /**
   * Onboard invited user after Supabase authentication
   * @param {Object} onboardData - Onboarding data
   * @param {string} onboardData.invitation_token - Invitation token
   * @param {string} onboardData.supabase_token - Supabase JWT token
   * @param {string} onboardData.name - User's full name
   * @returns {Promise<Object>} Onboarding result
   */
  async onboardInvitedUser(onboardData) {
    console.log('[InvitationAPI] Starting onboardInvitedUser request');

    if (!onboardData.invitation_token) {
      throw new Error('Invitation token is required');
    }

    if (!onboardData.supabase_token) {
      throw new Error('Supabase token is required');
    }

    if (!onboardData.name) {
      throw new Error('Name is required');
    }

    const url = `${API_BASE_URL}/api/v1/invitations/onboard`;
    console.log('[InvitationAPI] Request URL:', url);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(onboardData),
      });

      console.log('[InvitationAPI] Response status:', response.status);

      const data = await response.json();
      console.log('[InvitationAPI] Response data:', data);

      if (!response.ok) {
        // Extract error message from response
        const errorMessage = data.message || `HTTP error! status: ${response.status}`;
        throw new Error(errorMessage);
      }

      return data;
    } catch (error) {
      console.error('[InvitationAPI] Error:', error);

      if (error.message.includes('fetch')) {
        throw new Error('Network error. Please check your connection and try again.');
      }

      throw error;
    }
  }
};

/**
 * Error types for invitation API
 */
export const INVITATION_ERRORS = {
  MISSING_TOKEN: 'missing_token',
  INVITATION_NOT_FOUND: 'invitation_not_found',
  INVITATION_EXPIRED: 'invitation_expired',
  INVITATION_ALREADY_ACCEPTED: 'invitation_already_accepted',
  INVITATION_CANCELLED: 'invitation_cancelled',
  INVITATION_INVALID_STATUS: 'invitation_invalid_status',
  RATE_LIMIT_EXCEEDED: 'rate_limit_exceeded',
  SERVER_ERROR: 'server_error',
  NETWORK_ERROR: 'network_error'
};

/**
 * Get user-friendly error message for invitation errors
 * @param {string} errorCode - Error code from API
 * @param {Object} errorData - Additional error data
 * @returns {string} User-friendly error message
 */
export function getInvitationErrorMessage(errorCode, errorData = {}) {
  switch (errorCode) {
    case INVITATION_ERRORS.MISSING_TOKEN:
      return 'Invalid invitation link. The invitation token is missing.';
    
    case INVITATION_ERRORS.INVITATION_NOT_FOUND:
      return 'Invalid invitation link. This invitation does not exist or has been removed.';
    
    case INVITATION_ERRORS.INVITATION_EXPIRED:
      return `This invitation has expired${errorData.expired_at ? ` on ${new Date(errorData.expired_at).toLocaleDateString()}` : ''}. Please contact your administrator for a new invitation.`;
    
    case INVITATION_ERRORS.INVITATION_ALREADY_ACCEPTED:
      return `This invitation has already been accepted${errorData.accepted_at ? ` on ${new Date(errorData.accepted_at).toLocaleDateString()}` : ''}. You can sign in to your account.`;
    
    case INVITATION_ERRORS.INVITATION_CANCELLED:
      return 'This invitation has been cancelled. Please contact your administrator if you believe this is an error.';
    
    case INVITATION_ERRORS.INVITATION_INVALID_STATUS:
      return 'This invitation is no longer valid. Please contact your administrator for assistance.';
    
    case INVITATION_ERRORS.RATE_LIMIT_EXCEEDED:
      return 'Too many requests. Please wait a moment and try again.';
    
    case INVITATION_ERRORS.SERVER_ERROR:
      return 'A server error occurred. Please try again later or contact support if the problem persists.';
    
    case INVITATION_ERRORS.NETWORK_ERROR:
      return 'Network error. Please check your internet connection and try again.';
    
    default:
      return errorData.message || 'An unexpected error occurred. Please try again or contact support.';
  }
}

import { createClient } from '@/lib/supabase/client';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

/**
 * Get authentication headers with JWT token
 * @returns {Promise<Object>} Headers object with Authorization
 */
async function getAuthHeaders() {
  const supabase = createClient();
  const { data } = await supabase.auth.getSession();
  const token = data?.session?.access_token;

  if (!token) {
    throw new Error('No authentication token available');
  }

  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
}

/**
 * Mark an enquiry as spam
 * @param {string} enquiryId - The ID of the enquiry to mark as spam
 * @returns {Promise<Object>} Response from the API
 */
export async function markEnquiryAsSpam(enquiryId) {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/v1/spam/enquiries/${enquiryId}/mark-spam`, {
      method: 'POST',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error marking enquiry as spam:', error);
    throw error;
  }
}

/**
 * Mark an enquiry as not spam (genuine)
 * @param {string} enquiryId - The ID of the enquiry to mark as not spam
 * @returns {Promise<Object>} Response from the API
 */
export async function markEnquiryAsNotSpam(enquiryId) {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/v1/spam/enquiries/${enquiryId}/mark-not-spam`, {
      method: 'POST',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error marking enquiry as not spam:', error);
    throw error;
  }
}

/**
 * Get spam statistics for the organization
 * @returns {Promise<Object>} Spam statistics
 */
export async function getSpamStats() {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/v1/spam/stats`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching spam stats:', error);
    throw error;
  }
}

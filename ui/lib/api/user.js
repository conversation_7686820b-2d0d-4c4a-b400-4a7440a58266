/**
 * User API Service
 */

import { createClient } from '@/lib/supabase/client';
import { makeOrganizationValidatedRequest, handleResponse } from './utils';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

/**
 * Get authorization headers with JWT token
 */
async function getAuthHeaders() {
  const supabase = createClient();
  const { data } = await supabase.auth.getSession();
  const token = data?.session?.access_token;

  if (!token) {
    throw new Error('User is not authenticated. Please sign in.');
  }

  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
}

/**
 * User API endpoints
 */
export const userAPI = {
  /**
   * Get current authenticated user details from users table
   * @returns {Promise<Object>} Complete user data including role, status, etc.
   */
  async getMe() {
    const url = `${API_BASE_URL}/api/v1/users/me`;
    const headers = await getAuthHeaders();

    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(error || `HTTP ${response.status}`);
    }

    return await response.json();
  },

  /**
   * Get all users (for assignment dropdown)
   * @param {string} orgSlug - Organization slug for validation
   * @returns {Promise<Array>} Array of all users
   */
  async getAllUsers(orgSlug = null) {
    const endpoint = `/api/v1/users`;

    const response = await makeOrganizationValidatedRequest(endpoint, {
      method: 'GET',
    }, orgSlug);

    const data = await handleResponse(response);

    // Backend returns simple format: { id, name, role }
    // No need to transform, just return as-is
    return Array.isArray(data) ? data : [];
  },
};

/**
 * Helper function to transform backend user data to frontend format
 * @param {Object} apiData - Data from the API
 * @returns {Object} Frontend-compatible user data
 */
export function transformUserData(apiData) {
  return {
    id: apiData.id,
    name: apiData.name,
    email: apiData.email,
    role: apiData.role,
    status: apiData.status,
    phone: apiData.phone,
    jobTitle: apiData.job_title,
    department: apiData.department,
    bio: apiData.bio,
    timezone: apiData.timezone,
    avatar: apiData.avatar,
    lastLogin: apiData.last_login,
    joinedDate: apiData.joined_date,
    createdAt: apiData.created_at,
    updatedAt: apiData.updated_at,
    // Organization fields from the relationship
    organization_id: apiData.organization_id,
    org_slug: apiData.organization?.slug,
    org_name: apiData.organization?.name,
  };
}

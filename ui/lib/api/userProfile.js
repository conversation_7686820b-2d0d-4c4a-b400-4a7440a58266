/**
 * User Profile API Service
 */

import { createClient } from '@/lib/supabase/client';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

/**
 * Get authorization headers with JWT token
 */
async function getAuthHeaders() {
  console.log('[API] Getting auth headers...');

  try {
    console.log('[API] Creating Supabase client...');
    const supabase = createClient();
    console.log('[API] Supabase client created successfully');

    console.log('[API] Getting session from Supabase...');
    const { data: { session }, error } = await supabase.auth.getSession();

    if (error) {
      console.error('[API] Error getting session:', error);
    }

    console.log('[API] Session data:', {
      hasSession: !!session,
      hasAccessToken: !!session?.access_token,
      hasUser: !!session?.user,
      userId: session?.user?.id,
      userEmail: session?.user?.email,
      tokenLength: session?.access_token?.length,
      expiresAt: session?.expires_at,
      tokenType: session?.token_type
    });

    if (session?.access_token) {
      console.log('[API] Found access token, creating authenticated headers');
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
      };
      console.log('[API] Headers created with Authorization:', `Bearer ${session.access_token.substring(0, 20)}...`);
      return headers;
    } else {
      console.warn('[API] No access token found in session');
      throw new Error('No authentication token available');
    }
  } catch (error) {
    console.error('[API] Error in getAuthHeaders:', error);
    console.error('[API] Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack
    });
    throw error;
  }
}

/**
 * User Profile API endpoints
 */
export const userProfileAPI = {
  /**
   * Get user profile (uses JWT token to identify user)
   * @returns {Promise<Object>} User profile data
   */
  async getProfile() {
    console.log('[API] Starting getProfile request...');
    const url = `${API_BASE_URL}/api/v1/profile`;
    console.log('[API] Request URL:', url);

    const headers = await getAuthHeaders();
    console.log('[API] Final headers for request:', {
      ...headers,
      Authorization: headers.Authorization ? `Bearer ${headers.Authorization.substring(7, 27)}...` : 'Not present'
    });

    console.log('[API] Making fetch request...');
    const response = await fetch(url, {
      method: 'GET',
      headers,
    });

    console.log('[API] Response received:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
      headers: Object.fromEntries(response.headers.entries())
    });

    if (!response.ok) {
      const error = await response.text();
      console.error('[API] Error response body:', error);
      throw new Error(error || `HTTP ${response.status}`);
    }

    const data = await response.json();
    console.log('[API] Success response data:', data);
    return data;
  },

  /**
   * Update user profile (uses JWT token to identify user)
   * @param {Object} profileData - Profile data to update
   * @param {string} [profileData.phone] - User's phone number
   * @param {string} [profileData.job_title] - User's job title
   * @param {string} [profileData.department] - User's department
   * @param {string} [profileData.timezone] - User's timezone
   * @param {string} [profileData.bio] - User's biography
   * @returns {Promise<Object>} Updated user profile data
   */
  async updateProfile(profileData) {
    // Filter out empty strings and undefined values
    const cleanData = {};
    Object.entries(profileData).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        cleanData[key] = value;
      }
    });

    const url = `${API_BASE_URL}/api/v1/profile`;
    const headers = await getAuthHeaders();

    const response = await fetch(url, {
      method: 'PUT',
      headers,
      body: JSON.stringify(cleanData),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(error || `HTTP ${response.status}`);
    }

    return response.json();
  },
};

/**
 * Helper function to transform frontend form data to backend API format
 * @param {Object} formData - Form data from the UI
 * @returns {Object} API-compatible data
 */
export function transformFormDataToAPI(formData) {
  return {
    phone: formData.phone,
    job_title: formData.jobTitle,
    department: formData.department,
    timezone: formData.timezone,
    bio: formData.bio,
  };
}

/**
 * Helper function to transform backend API data to frontend form format
 * @param {Object} apiData - Data from the API
 * @returns {Object} Form-compatible data
 */
export function transformAPIDataToForm(apiData) {
  return {
    fullName: apiData.name || '',
    email: apiData.email || '',
    phone: apiData.phone || '',
    jobTitle: apiData.job_title || '',
    department: apiData.department || '',
    bio: apiData.bio || '',
    timezone: apiData.timezone || '',
    // These are not editable but shown in the form
    role: apiData.role || '',
    status: apiData.status || '',
    lastLogin: apiData.last_login || '',
    joinedDate: apiData.joined_date || '',
  };
}

/**
 * API Utilities for Organization Validation and Security
 * 
 * This module provides shared utilities for API calls with organization validation.
 */

import { createClient } from '@/lib/supabase/client';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

/**
 * Get authentication headers with JW<PERSON> token
 * @returns {Promise<Object>} Headers object with Authorization
 */
export async function getAuthHeaders() {
  const supabase = createClient();
  const { data } = await supabase.auth.getSession();
  const token = data?.session?.access_token;
  
  if (!token) {
    throw new Error('No authentication token available');
  }

  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
  };
}

/**
 * Get user's organization data from auth context
 * @returns {Promise<Object>} User organization data
 */
export async function getUserOrganization() {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${API_BASE_URL}/api/v1/users/me`, {
      method: 'GET',
      headers,
    });
    
    if (!response.ok) {
      throw new Error('Failed to get user organization');
    }
    
    const userData = await response.json();
    return {
      id: userData.organization_id,
      slug: userData.org_slug,
      name: userData.org_name,
    };
  } catch (error) {
    console.error('Error getting user organization:', error);
    throw error;
  }
}

/**
 * Validate organization context before making API calls
 * @param {string} requestOrgSlug - Organization slug from URL/request
 * @returns {Promise<Object>} Validated organization data
 */
export async function validateOrganizationContext(requestOrgSlug = null) {
  const userOrg = await getUserOrganization();
  
  // If org slug is provided in request, validate it matches user's org
  if (requestOrgSlug && requestOrgSlug !== userOrg.slug) {
    throw new Error('Organization access denied: URL organization does not match user organization');
  }
  
  return userOrg;
}

/**
 * Make an organization-validated API request
 * @param {string} endpoint - API endpoint (relative to base URL)
 * @param {Object} options - Fetch options
 * @param {string} orgSlug - Organization slug to validate (optional)
 * @returns {Promise<Response>} Fetch response
 */
export async function makeOrganizationValidatedRequest(endpoint, options = {}, orgSlug = null) {
  // Validate organization context
  const userOrg = await validateOrganizationContext(orgSlug);

  // Get auth headers
  const headers = await getAuthHeaders();

  // Merge headers
  const finalOptions = {
    ...options,
    headers: {
      ...headers,
      ...options.headers,
    },
  };

  // CRITICAL: Add organization ID to query params for backend validation
  // This ensures backend knows which organization is being requested
  const url = new URL(`${API_BASE_URL}${endpoint}`);
  url.searchParams.append('org_id', userOrg.id);

  console.log(`[API] Making organization-validated request to: ${url.toString()}`);
  console.log(`[API] User organization: ${userOrg.name} (${userOrg.slug})`);
  console.log(`[API] Organization ID being sent: ${userOrg.id}`);

  return fetch(url.toString(), finalOptions);
}

/**
 * Handle API response and errors
 * @param {Response} response - Fetch response object
 * @returns {Promise<Object>} Parsed JSON response
 */
export async function handleResponse(response) {
  const contentType = response.headers.get('content-type');
  
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    
    if (contentType && contentType.includes('application/json')) {
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorData.error || errorMessage;
      } catch (e) {
        // If JSON parsing fails, use the default error message
      }
    }
    
    throw new Error(errorMessage);
  }
  
  if (contentType && contentType.includes('application/json')) {
    return await response.json();
  }
  
  return {};
}

/**
 * Add organization validation to request body
 * @param {Object} requestBody - Original request body
 * @param {Object} userOrg - User organization data
 * @returns {Object} Request body with organization validation
 */
export function addOrganizationToRequestBody(requestBody, userOrg) {
  return {
    ...requestBody,
    organization_id: userOrg.id,
  };
}

/**
 * Validate that a resource belongs to the user's organization
 * @param {Object} resource - Resource object from API
 * @param {Object} userOrg - User organization data
 * @throws {Error} If resource doesn't belong to user's organization
 */
export function validateResourceOrganization(resource, userOrg) {
  if (resource.organization_id && resource.organization_id !== userOrg.id) {
    throw new Error('Access denied: Resource does not belong to your organization');
  }
}

"use client";

import { createContext, useContext, useEffect, useState } from 'react';
import { createClient } from '@/lib/supabase/client';
import { userAPI, transformUserData } from '@/lib/api/user';

const AuthContext = createContext({});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [accessToken, setAccessToken] = useState(null);
  const [completeUser, setCompleteUser] = useState(null);
  const [authError, setAuthError] = useState(null);
  const [fetchingUser, setFetchingUser] = useState(false);

  const supabase = createClient();

  // Function to fetch complete user details from our backend
  const fetchCompleteUserDetails = async (userId) => {
    if (!userId || !accessToken) {
      setCompleteUser(null);
      setAuthError(null);
      setFetchingUser(false);
      return;
    }

    try {
      setFetchingUser(true);
      const userData = await userAPI.getMe();
      const transformedUser = transformUserData(userData);
      setCompleteUser(transformedUser);
      setAuthError(null);
    } catch (error) {
      console.error('Failed to fetch complete user details:', error);

      // Check if it's a 401 error (authentication failure)
      if (error.message.includes('401') || error.message.includes('Unauthorized')) {
        console.log('Authentication error detected, user needs to re-login');
        setAuthError('unauthorized');
        setCompleteUser(null);
        // Sign out the user from Supabase as well
        await supabase.auth.signOut();
      } else if (error.message.includes('404') || error.message.includes('User not found')) {
        console.log('User not found in backend, needs onboarding');
        setAuthError('not_found');
        setCompleteUser(null);
      } else {
        console.log('Other error, treating as need for onboarding');
        setAuthError('other');
        setCompleteUser(null);
      }
    } finally {
      setFetchingUser(false);
    }
  };

  // Get session and access token
  useEffect(() => {
    const getSession = async () => {
      const { data } = await supabase.auth.getSession();
      const token = data?.session?.access_token;
      if (token) {
        setAccessToken(token);
      }
    };
    getSession();
  }, [supabase.auth]);

  // Get user
  useEffect(() => {
    const getUser = async () => {
      const { data, error } = await supabase.auth.getUser();
      if (error) {
        console.error(error);
        setUser(null);
      } else {
        setUser(data.user);
      }
      setLoading(false);
    };
    getUser();
  }, [supabase.auth]);

  // Fetch complete user details when user and token are available
  useEffect(() => {
    if (user && accessToken) {
      fetchCompleteUserDetails(user.id);
    } else {
      setCompleteUser(null);
      setFetchingUser(false);
    }
  }, [user, accessToken]);

  // Listen for auth changes
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_event, session) => {
        const supabaseUser = session?.user ?? null;
        const token = session?.access_token ?? null;

        setUser(supabaseUser);
        setAccessToken(token);

        if (!supabaseUser) {
          setCompleteUser(null);
          setAuthError(null);
          setFetchingUser(false);
          setLoading(false);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [supabase.auth]);

  const signInWithGoogle = async () => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
    });
    if (error) {
      console.error('Error signing in with Google:', error);
      throw error;
    }
  };

  const signInWithEmail = async (email, password) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    if (error) {
      console.error('Error signing in with email:', error);
      throw error;
    }
    return data;
  };

  const signUpWithEmail = async (email, password) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    });
    if (error) {
      console.error('Error signing up with email:', error);
      throw error;
    }
    return data;
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  };

  const value = {
    user: completeUser || user, // Use completeUser if available, fallback to basic user
    basicUser: user, // Keep basic Supabase user for reference
    completeUser,
    accessToken,
    loading,
    fetchingUser,
    authError,
    signInWithGoogle,
    signInWithEmail,
    signUpWithEmail,
    signOut,
    supabase
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

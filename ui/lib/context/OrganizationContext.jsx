"use client";

import { createContext, useContext, useEffect, useState } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { useRouter, usePathname } from 'next/navigation';

const OrganizationContext = createContext({});

export const useOrganization = () => {
  const context = useContext(OrganizationContext);
  if (!context) {
    throw new Error('useOrganization must be used within an OrganizationProvider');
  }
  return context;
};

export const OrganizationProvider = ({ children, orgSlug }) => {
  const { user, loading: authLoading, fetchingUser } = useAuth();
  const [organization, setOrganization] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const router = useRouter();
  const pathname = usePathname();

  // Validate organization slug against user's organization
  useEffect(() => {
    if (authLoading || fetchingUser) return;

    if (!user) {
      setError('User not authenticated');
      setLoading(false);
      return;
    }

    // Check if user has organization data
    if (!user.org_slug) {
      setError('User has no organization assigned');
      setLoading(false);
      return;
    }

    // Validate that the URL slug matches user's organization
    if (orgSlug && orgSlug !== user.org_slug) {
      setError('Access denied to this organization');
      setLoading(false);
      // Redirect to user's correct organization
      const newPath = pathname.replace(`/org/${orgSlug}`, `/org/${user.org_slug}`);
      router.replace(newPath);
      return;
    }

    // Set organization data from user context
    setOrganization({
      id: user.organization_id,
      slug: user.org_slug,
      name: user.org_name,
    });
    setError(null);
    setLoading(false);
  }, [user, authLoading, fetchingUser, orgSlug, pathname, router]);

  // Generate organization-aware URLs
  const getOrgUrl = (path = '') => {
    if (!organization?.slug) return '/login';
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    return `/org/${organization.slug}${cleanPath}`;
  };

  // Navigate to organization-aware route
  const navigateToOrg = (path = '') => {
    const url = getOrgUrl(path);
    router.push(url);
  };

  const value = {
    organization,
    loading: loading || authLoading || fetchingUser,
    error,
    getOrgUrl,
    navigateToOrg,
    orgSlug: organization?.slug,
    orgName: organization?.name,
    orgId: organization?.id,
  };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
};

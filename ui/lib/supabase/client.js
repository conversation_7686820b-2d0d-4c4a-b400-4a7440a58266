import { createBrowserClient } from '@supabase/ssr'

export function createClient() {
  console.log('[Supabase] Creating Supabase client...');

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  console.log('[Supabase] Environment variables:', {
    hasUrl: !!supabaseUrl,
    hasKey: !!supabaseAnonKey,
    url: supabaseUrl,
    keyLength: supabaseAnonKey?.length
  });

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('[Supabase] Missing environment variables');
    throw new Error('Missing Supabase environment variables. Please check your .env.local file.')
  }

  console.log('[Supabase] Creating browser client...');
  const client = createBrowserClient(supabaseUrl, supabaseAnonKey);
  console.log('[Supabase] Browser client created successfully');

  return client;
}

// Types and interfaces for the QueryCRM

export const LeadStatus = {
  NEW: 'New',
  IN_PROGRESS: 'In Progress',
  CLOSED: 'Closed'
};

export const Sector = {
  COLLEGE: 'College',
  BUSINESS: 'Business'
};

export const ActivityType = {
  NOTE_ADDED: 'Note added to Lead',
  STATUS_CHANGED: 'Status changed to In Progress'
};

// Mock data for development
export const mockLeads = [
  {
    id: 1,
    name: '<PERSON>',
    sector: Sector.COLLEGE,
    assignedTo: '<PERSON>',
    status: LeadStatus.IN_PROGRESS,
    submittedOn: 'April 24, 2024'
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    sector: Sector.BUSINESS,
    assignedTo: '<PERSON>',
    status: LeadStatus.NEW,
    submittedOn: 'April 24, 2024'
  },
  {
    id: 3,
    name: '<PERSON><PERSON>',
    sector: Sector.COLLEGE,
    assignedTo: '<PERSON>',
    status: LeadStatus.NEW,
    submittedOn: 'April 23, 2024'
  },
  {
    id: 4,
    name: '<PERSON>',
    sector: Sector.COLLEGE,
    assignedTo: 'April <PERSON>',
    status: LeadStatus.IN_PROGRESS,
    submittedOn: 'April 23, 2024'
  },
  {
    id: 5,
    name: '<PERSON><PERSON>',
    sector: Sector.BUSINESS,
    assignedTo: 'April 23 2024',
    status: LeadStatus.NEW,
    submittedOn: 'April 23, 2024'
  },
  {
    id: 6,
    name: 'Jacob Jones',
    sector: Sector.COLLEGE,
    assignedTo: 'Business',
    status: LeadStatus.NEW,
    submittedOn: 'April 23, 2024'
  },
  {
    id: 7,
    name: 'Dianne Russell',
    sector: Sector.COLLEGE,
    assignedTo: 'Raph Edw...',
    status: LeadStatus.NEW,
    submittedOn: 'April 23, 2024'
  },
  {
    id: 8,
    name: 'Kristin Watson',
    sector: Sector.COLLEGE,
    assignedTo: 'April 2024',
    status: LeadStatus.CLOSED,
    submittedOn: 'April 23, 2024'
  }
];

export const mockStats = {
  totalLeads: 150,
  newToday: 25,
  inProgress: 60,
  closed: 15
};

export const mockActivities = [
  {
    id: 1,
    type: ActivityType.NOTE_ADDED,
    timestamp: '2 hours ago'
  },
  {
    id: 2,
    type: ActivityType.STATUS_CHANGED,
    timestamp: '3 hours ago'
  },
  {
    id: 3,
    type: ActivityType.NOTE_ADDED,
    timestamp: '5 hours ago'
  },
  {
    id: 4,
    type: ActivityType.STATUS_CHANGED,
    timestamp: '1 day ago'
  }
];

// Extended enquiry data for detailed pages
export const mockEnquiries = [
  {
    id: 1,
    slug: 'jacob-jones-college-enquiry',
    name: 'Jacob Jones',
    email: '<EMAIL>',
    phone: '+****************',
    sector: Sector.COLLEGE,
    assignedTo: 'Sarah Brown',
    status: LeadStatus.IN_PROGRESS,
    submittedOn: 'April 24, 2024',
    source: 'Website Form',
    priority: 'High',
    description: 'Interested in computer science program. Looking for information about admission requirements and scholarship opportunities.',
    notes: [
      {
        id: 1,
        content: 'Initial contact made. Student is very interested in our CS program.',
        author: 'Sarah Brown',
        timestamp: 'April 24, 2024 10:30 AM'
      },
      {
        id: 2,
        content: 'Sent program brochure and admission requirements.',
        author: 'Sarah Brown',
        timestamp: 'April 24, 2024 2:15 PM'
      }
    ],
    activities: [
      {
        id: 1,
        type: 'Status Changed',
        description: 'Status changed from New to In Progress',
        timestamp: 'April 24, 2024 10:00 AM',
        user: 'Sarah Brown'
      },
      {
        id: 2,
        type: 'Note Added',
        description: 'Added initial contact note',
        timestamp: 'April 24, 2024 10:30 AM',
        user: 'Sarah Brown'
      }
    ]
  },
  {
    id: 2,
    slug: 'ariene-mccoy-business-enquiry',
    name: 'Ariene McCoy',
    email: '<EMAIL>',
    phone: '+****************',
    sector: Sector.BUSINESS,
    assignedTo: 'Katherine Howard',
    status: LeadStatus.NEW,
    submittedOn: 'April 24, 2024',
    source: 'LinkedIn',
    priority: 'Medium',
    description: 'Business owner looking for partnership opportunities and training programs for employees.',
    notes: [
      {
        id: 1,
        content: 'Business enquiry received. Need to schedule initial consultation.',
        author: 'Katherine Howard',
        timestamp: 'April 24, 2024 9:00 AM'
      }
    ],
    activities: [
      {
        id: 1,
        type: 'Enquiry Created',
        description: 'New enquiry submitted via LinkedIn',
        timestamp: 'April 24, 2024 8:45 AM',
        user: 'System'
      }
    ]
  },
  {
    id: 3,
    slug: 'dianne-russell-college-enquiry',
    name: 'Dianne Russell',
    email: '<EMAIL>',
    phone: '+****************',
    sector: Sector.COLLEGE,
    assignedTo: 'Sarah Brown',
    status: LeadStatus.NEW,
    submittedOn: 'April 23, 2024',
    source: 'Phone Call',
    priority: 'Low',
    description: 'Parent enquiring about undergraduate programs for their child.',
    notes: [],
    activities: [
      {
        id: 1,
        type: 'Enquiry Created',
        description: 'New enquiry received via phone call',
        timestamp: 'April 23, 2024 3:30 PM',
        user: 'System'
      }
    ]
  }
];



// Global notes system
export const mockGlobalNotes = [
  {
    id: 1,
    title: 'Follow-up with Jacob Jones',
    content: 'Student is very interested in our CS program. Sent program brochure and admission requirements. Need to schedule campus visit.',
    author: 'Sarah Brown',
    category: 'Follow-up',
    tags: ['CS Program', 'Campus Visit'],
    relatedTo: {
      type: 'lead',
      id: 1,
      name: 'Jacob Jones'
    },
    priority: 'High',
    createdAt: 'April 24, 2024 2:15 PM',
    updatedAt: 'April 24, 2024 2:15 PM',
    isPrivate: false,
    attachments: []
  },
  {
    id: 2,
    title: 'Business Partnership Discussion',
    content: 'Had initial conversation with Ariene McCoy about potential partnership. She is interested in employee training programs. Need to prepare proposal.',
    author: 'Katherine Howard',
    category: 'Business Development',
    tags: ['Partnership', 'Training', 'Proposal'],
    relatedTo: {
      type: 'lead',
      id: 2,
      name: 'Ariene McCoy'
    },
    priority: 'Medium',
    createdAt: 'April 24, 2024 9:30 AM',
    updatedAt: 'April 24, 2024 9:30 AM',
    isPrivate: false,
    attachments: []
  },
  {
    id: 3,
    title: 'Weekly Team Meeting Notes',
    content: 'Discussed lead conversion strategies and upcoming marketing campaigns. Need to focus on graduate programs promotion.',
    author: 'Sarah Brown',
    category: 'Meeting',
    tags: ['Team Meeting', 'Strategy', 'Marketing'],
    relatedTo: null,
    priority: 'Low',
    createdAt: 'April 23, 2024 3:00 PM',
    updatedAt: 'April 23, 2024 3:00 PM',
    isPrivate: false,
    attachments: []
  },
  {
    id: 4,
    title: 'Graduate Program Requirements Update',
    content: 'Updated admission requirements for graduate programs. New GPA threshold and additional documentation needed.',
    author: 'April Edwards',
    category: 'Administrative',
    tags: ['Graduate', 'Requirements', 'Documentation'],
    relatedTo: null,
    priority: 'Medium',
    createdAt: 'April 23, 2024 1:00 PM',
    updatedAt: 'April 23, 2024 1:00 PM',
    isPrivate: true,
    attachments: ['grad_requirements_2024.pdf']
  }
];

// Note categories
export const noteCategories = [
  'Follow-up',
  'Meeting',
  'Business Development',
  'Administrative',
  'Research',
  'Customer Service',
  'Marketing',
  'Technical'
];

// Team members for assignment
export const teamMembers = [
  {
    id: 1,
    name: 'Sarah Brown',
    email: '<EMAIL>',
    role: 'Lead Manager',
    avatar: 'SB'
  },
  {
    id: 2,
    name: 'Katherine Howard',
    email: '<EMAIL>',
    role: 'Business Development',
    avatar: 'KH'
  },
  {
    id: 3,
    name: 'April Edwards',
    email: '<EMAIL>',
    role: 'Academic Advisor',
    avatar: 'AE'
  },
  {
    id: 4,
    name: 'John Smith',
    email: '<EMAIL>',
    role: 'Sales Representative',
    avatar: 'JS'
  }
];

// Settings configuration
export const userRoles = [
  {
    id: 'admin',
    name: 'Administrator',
    description: 'Full access to all features and settings',
    permissions: ['read', 'write', 'delete', 'manage_users', 'manage_settings']
  },
  {
    id: 'manager',
    name: 'Manager',
    description: 'Can manage leads and view reports',
    permissions: ['read', 'write', 'manage_leads']
  },
  {
    id: 'agent',
    name: 'Agent',
    description: 'Can view and update assigned leads',
    permissions: ['read', 'write']
  },
  {
    id: 'viewer',
    name: 'Viewer',
    description: 'Read-only access to data',
    permissions: ['read']
  }
];

export const mockTeamMembers = [
  {
    id: 1,
    name: 'Sarah Brown',
    email: '<EMAIL>',
    role: 'admin',
    avatar: 'SB',
    status: 'active',
    lastLogin: '2024-04-24 10:30 AM',
    joinedDate: '2024-01-15'
  },
  {
    id: 2,
    name: 'Katherine Howard',
    email: '<EMAIL>',
    role: 'manager',
    avatar: 'KH',
    status: 'active',
    lastLogin: '2024-04-24 09:15 AM',
    joinedDate: '2024-02-01'
  },
  {
    id: 3,
    name: 'April Edwards',
    email: '<EMAIL>',
    role: 'agent',
    avatar: 'AE',
    status: 'active',
    lastLogin: '2024-04-23 04:45 PM',
    joinedDate: '2024-02-15'
  },
  {
    id: 4,
    name: 'John Smith',
    email: '<EMAIL>',
    role: 'agent',
    avatar: 'JS',
    status: 'inactive',
    lastLogin: '2024-04-20 02:30 PM',
    joinedDate: '2024-03-01'
  },
  {
    id: 5,
    name: 'Demo User',
    email: '<EMAIL>',
    role: 'admin',
    avatar: 'DU',
    status: 'active',
    lastLogin: 'Now',
    joinedDate: '2024-04-24'
  }
];

export const enquiryStatuses = [
  {
    id: 'new',
    name: 'New',
    color: '#10B981',
    description: 'Newly received enquiries',
    isDefault: true,
    isActive: true
  },
  {
    id: 'in-progress',
    name: 'In Progress',
    color: '#3B82F6',
    description: 'Enquiries being actively worked on',
    isDefault: false,
    isActive: true
  },
  {
    id: 'closed',
    name: 'Closed',
    color: '#6B7280',
    description: 'Completed or resolved enquiries',
    isDefault: false,
    isActive: true
  },
  {
    id: 'on-hold',
    name: 'On Hold',
    color: '#F59E0B',
    description: 'Temporarily paused enquiries',
    isDefault: false,
    isActive: true
  }
];

export const enquiryPriorities = [
  {
    id: 'low',
    name: 'Low',
    color: '#10B981',
    description: 'Non-urgent enquiries',
    isDefault: true,
    isActive: true
  },
  {
    id: 'medium',
    name: 'Medium',
    color: '#F59E0B',
    description: 'Standard priority enquiries',
    isDefault: false,
    isActive: true
  },
  {
    id: 'high',
    name: 'High',
    color: '#EF4444',
    description: 'Urgent enquiries requiring immediate attention',
    isDefault: false,
    isActive: true
  }
];

export const notificationSettings = {
  email: {
    newEnquiry: true,
    statusUpdate: true,
    assignment: true,
    dailyDigest: false,
    weeklyReport: true
  },
  inApp: {
    newEnquiry: true,
    statusUpdate: true,
    assignment: true,
    mentions: true
  }
};



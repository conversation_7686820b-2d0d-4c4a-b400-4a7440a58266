/**
 * Date utility functions for formatting API dates
 */

/**
 * Format ISO date string to readable format
 * @param {string} isoDate - ISO date string from API (e.g., "2024-01-15T10:30:00Z")
 * @returns {string} Formatted date (e.g., "January 15, 2024")
 */
export function formatDate(isoDate) {
  if (!isoDate) return 'N/A';
  
  try {
    const date = new Date(isoDate);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
}

/**
 * Format ISO date string to short format
 * @param {string} isoDate - ISO date string from API
 * @returns {string} Short formatted date (e.g., "Jan 15, 2024")
 */
export function formatDateShort(isoDate) {
  if (!isoDate) return 'N/A';
  
  try {
    const date = new Date(isoDate);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
}

/**
 * Format ISO date string to include time
 * @param {string} isoDate - ISO date string from API
 * @returns {string} Formatted date with time (e.g., "Jan 15, 2024 10:30 AM")
 */
export function formatDateTime(isoDate) {
  if (!isoDate) return 'N/A';
  
  try {
    const date = new Date(isoDate);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
}

/**
 * Get relative time from now (e.g., "2 days ago")
 * @param {string} isoDate - ISO date string from API
 * @returns {string} Relative time string
 */
export function getRelativeTime(isoDate) {
  if (!isoDate) return 'N/A';
  
  try {
    const date = new Date(isoDate);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      return formatDateShort(isoDate);
    }
  } catch (error) {
    console.error('Error calculating relative time:', error);
    return 'Invalid Date';
  }
}

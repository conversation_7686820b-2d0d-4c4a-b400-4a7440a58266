/**
 * Permission utilities for enquiry management
 * Handles role-based access control for enquiry operations
 */

/**
 * Enquiry permission checks based on user role and enquiry data
 */
export const enquiryPermissions = {
  /**
   * Check if user can edit enquiry status
   * @param {Object} user - Current user object
   * @param {Object} enquiry - Enquiry object
   * @returns {boolean}
   */
  canEditStatus: (user, enquiry) => {
    if (!user) return false;
    
    // Admin and Manager can edit any enquiry status
    if (user.role === 'admin' || user.role === 'manager') {
      return true;
    }
    
    // Agents can only edit status of enquiries assigned to them
    if (user.role === 'agent') {
      return enquiry?.assigned_to_id === user.id;
    }
    
    return false;
  },

  /**
   * Check if user can edit enquiry priority
   * @param {Object} user - Current user object
   * @returns {boolean}
   */
  canEditPriority: (user) => {
    if (!user) return false;
    return user.role === 'admin' || user.role === 'manager';
  },

  /**
   * Check if user can edit enquiry assignment
   * @param {Object} user - Current user object
   * @returns {boolean}
   */
  canEditAssignment: (user) => {
    if (!user) return false;
    // All authenticated users can assign (with role-based restrictions)
    return true;
  },

  /**
   * Check if user can close enquiries (set status to "Closed")
   * @param {Object} user - Current user object
   * @returns {boolean}
   */
  canCloseEnquiry: (user) => {
    if (!user) return false;
    return user.role === 'admin' || user.role === 'manager';
  },

  /**
   * Get available status options based on user role and current status
   * @param {Object} user - Current user object
   * @returns {Array} Array of available status options
   */
  getAvailableStatuses: (user) => {
    const allStatuses = [
      { value: 'New', label: 'New' },
      { value: 'In Progress', label: 'In Progress' },
      { value: 'Closed', label: 'Closed' }
    ];

    if (!user) return [];

    // Admin and Manager can use all statuses
    if (user.role === 'admin' || user.role === 'manager') {
      return allStatuses;
    }

    // Agents cannot set status to "Closed"
    if (user.role === 'agent') {
      return allStatuses.filter(status => status.value !== 'Closed');
    }

    return allStatuses;
  },

  /**
   * Get available priority options based on user role
   * @param {Object} user - Current user object
   * @returns {Array} Array of available priority options
   */
  getAvailablePriorities: (user) => {
    const allPriorities = [
      { value: 'High', label: 'High' },
      { value: 'Medium', label: 'Medium' },
      { value: 'Low', label: 'Low' }
    ];

    if (!user) return [];

    // Only Admin and Manager can edit priority
    if (user.role === 'admin' || user.role === 'manager') {
      return allPriorities;
    }

    return []; // Agents cannot edit priority
  },

  /**
   * Get available assignees based on user role
   * @param {Object} user - Current user object
   * @param {Array} allUsers - Array of all available users
   * @returns {Array} Array of users that can be assigned to
   */
  getAvailableAssignees: (user, allUsers = []) => {
    if (!user || !Array.isArray(allUsers)) return [];

    // Admin and Manager can assign to anyone
    if (user.role === 'admin' || user.role === 'manager') {
      return allUsers;
    }

    // Agents can only assign to Admin and Manager users (escalation)
    if (user.role === 'agent') {
      return allUsers.filter(u => u.role === 'admin' || u.role === 'manager');
    }

    return [];
  },

  /**
   * Get field labels based on user role
   * @param {Object} user - Current user object
   * @returns {Object} Object with field labels
   */
  getFieldLabels: (user) => {
    if (!user) return {};

    const isAgent = user.role === 'agent';

    return {
      status: 'Status',
      priority: isAgent ? 'Priority (Manager Only)' : 'Priority',
      assignment: isAgent ? 'Escalate to' : 'Assign to'
    };
  },

  /**
   * Get help text for fields based on user role
   * @param {Object} user - Current user object
   * @returns {Object} Object with help text for fields
   */
  getFieldHelpText: (user) => {
    if (!user) return {};

    const isAgent = user.role === 'agent';

    return {
      status: isAgent ? 'You can update status but cannot close enquiries' : 'Update the enquiry status',
      priority: isAgent ? 'Only managers and administrators can change priority' : 'Set the enquiry priority level',
      assignment: isAgent ? 'Escalate this enquiry to a manager or administrator' : 'Assign this enquiry to a team member'
    };
  },

  /**
   * Check if user can edit a specific enquiry
   * @param {Object} user - Current user object
   * @param {Object} enquiry - Enquiry object
   * @returns {boolean}
   */
  canEditEnquiry: (user, enquiry) => {
    if (!user || !enquiry) return false;

    // Admin and Manager can edit any enquiry
    if (user.role === 'admin' || user.role === 'manager') {
      return true;
    }

    // Agents can only edit enquiries assigned to them
    if (user.role === 'agent') {
      return enquiry.assigned_to_id === user.id;
    }

    return false;
  },

  /**
   * Check if user can delete enquiries
   * @param {Object} user - Current user object
   * @returns {boolean}
   */
  canDeleteEnquiry: (user) => {
    if (!user) return false;
    // Only admin users can delete enquiries
    return user.role === 'admin';
  },

  /**
   * Check if user can manage spam (mark as spam/not spam)
   * @param {Object} user - Current user object
   * @returns {boolean}
   */
  canManageSpam: (user) => {
    if (!user) return false;
    // Only admin and manager users can manage spam
    return user.role === 'admin' || user.role === 'manager';
  },

  /**
   * Get permission summary for debugging
   * @param {Object} user - Current user object
   * @param {Object} enquiry - Enquiry object
   * @returns {Object} Permission summary object
   */
  getPermissionSummary: (user, enquiry) => {
    return {
      canEditStatus: enquiryPermissions.canEditStatus(user, enquiry),
      canEditPriority: enquiryPermissions.canEditPriority(user),
      canEditAssignment: enquiryPermissions.canEditAssignment(user),
      canCloseEnquiry: enquiryPermissions.canCloseEnquiry(user),
      canEditEnquiry: enquiryPermissions.canEditEnquiry(user, enquiry),
      canDeleteEnquiry: enquiryPermissions.canDeleteEnquiry(user),
      canManageSpam: enquiryPermissions.canManageSpam(user),
      userRole: user?.role || 'unknown',
      isAssigned: enquiry?.assigned_to_id === user?.id
    };
  }
};

/**
 * Role checking utilities
 */
export const roleUtils = {
  isAdmin: (user) => user?.role === 'admin',
  isManager: (user) => user?.role === 'manager', 
  isAgent: (user) => user?.role === 'agent',
  isAdminOrManager: (user) => user?.role === 'admin' || user?.role === 'manager',
  hasRole: (user, role) => user?.role === role,
  hasAnyRole: (user, roles) => roles.includes(user?.role)
};

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>University Enquiry Form</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .form-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2d3748;
            font-size: 2.2em;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .header p {
            color: #718096;
            font-size: 1.1em;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #4a5568;
            font-size: 0.95em;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }
        
        select {
            cursor: pointer;
        }
        
        textarea {
            height: 120px;
            resize: vertical;
            font-family: inherit;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px 32px;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 10px;
            position: relative;
            overflow: hidden;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        
        .submit-btn:active {
            transform: translateY(0);
        }
        
        .submit-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }
        
        .privacy-note {
            background: #f7fafc;
            border-left: 4px solid #667eea;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 0.9em;
            color: #4a5568;
        }
        
        .required {
            color: #e53e3e;
        }
        
        .hidden {
            display: none;
        }
        
        /* Toast Notification Styles */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
            max-width: 400px;
        }
        
        .toast {
            background: white;
            border-radius: 12px;
            padding: 16px 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            border-left: 4px solid;
            display: flex;
            align-items: center;
            gap: 12px;
            transform: translateX(100%);
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            opacity: 0;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }
        
        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        .toast.success {
            border-left-color: #48bb78;
        }
        
        .toast.error {
            border-left-color: #f56565;
        }
        
        .toast-icon {
            font-size: 20px;
            font-weight: bold;
            min-width: 20px;
        }
        
        .toast.success .toast-icon {
            color: #48bb78;
        }
        
        .toast.error .toast-icon {
            color: #f56565;
        }
        
        .toast-content {
            flex: 1;
        }
        
        .toast-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }
        
        .toast-message {
            color: #718096;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .toast-close {
            background: none;
            border: none;
            color: #a0aec0;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
            font-size: 18px;
            line-height: 1;
        }
        
        .toast-close:hover {
            color: #4a5568;
            background: #f7fafc;
        }
        
        .toast-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width linear;
        }
        
        .toast.success .toast-progress {
            background: #48bb78;
        }
        
        .toast.error .toast-progress {
            background: #f56565;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .form-container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .header h1 {
                font-size: 1.8em;
            }
            
            .toast-container {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
            }
            
            .toast {
                margin: 0;
            }
        }
        
        /* Loading spinner */
        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

    <div class="form-container">
        <div class="header">
            <h1>University Enquiry</h1>
            <p>We're here to help you find your perfect program</p>
        </div>
        
        <form id="universityForm">
            <!-- Hidden field to combine first and last name -->
            <input type="hidden" id="fullName" name="name">
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="firstName">First Name <span class="required">*</span></label>
                    <input type="text" id="firstName" name="first_name" required placeholder="John">
                </div>
                
                <div class="form-group">
                    <label for="lastName">Last Name <span class="required">*</span></label>
                    <input type="text" id="lastName" name="last_name" required placeholder="Doe">
                </div>
                
                <div class="form-group">
                    <label for="email">Email Address <span class="required">*</span></label>
                    <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="phone">Phone Number</label>
                    <input type="tel" id="phone" name="phone" placeholder="+****************">
                </div>
                
                <div class="form-group">
                    <label for="country">Country <span class="required">*</span></label>
                    <select id="country" name="country" required>
                        <option value="">Select your country</option>
                        <option value="US">United States</option>
                        <option value="CA">Canada</option>
                        <option value="UK">United Kingdom</option>
                        <option value="AU">Australia</option>
                        <option value="IN">India</option>
                        <option value="CN">China</option>
                        <option value="DE">Germany</option>
                        <option value="FR">France</option>
                        <option value="JP">Japan</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="studyLevel">Study Level <span class="required">*</span></label>
                    <select id="studyLevel" name="study_level" required>
                        <option value="">Select study level</option>
                        <option value="undergraduate">Undergraduate</option>
                        <option value="graduate">Graduate/Masters</option>
                        <option value="phd">PhD/Doctorate</option>
                        <option value="certificate">Certificate Program</option>
                        <option value="diploma">Diploma</option>
                    </select>
                </div>
                
                <div class="form-group full-width">
                    <label for="programInterest">Program of Interest</label>
                    <select id="programInterest" name="program_interest">
                        <option value="">Select a program (optional)</option>
                        <option value="business">Business & Management</option>
                        <option value="engineering">Engineering</option>
                        <option value="computer-science">Computer Science</option>
                        <option value="medicine">Medicine & Health Sciences</option>
                        <option value="arts">Arts & Humanities</option>
                        <option value="sciences">Natural Sciences</option>
                        <option value="social-sciences">Social Sciences</option>
                        <option value="education">Education</option>
                        <option value="law">Law</option>
                        <option value="other">Other</option>
                        <option value="undecided">Still deciding</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="startDate">Preferred Start Date</label>
                    <select id="startDate" name="start_date">
                        <option value="">Select term</option>
                        <option value="fall-2025">Fall 2025</option>
                        <option value="spring-2026">Spring 2026</option>
                        <option value="summer-2026">Summer 2026</option>
                        <option value="fall-2026">Fall 2026</option>
                        <option value="flexible">Flexible</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="howHeard">How did you hear about us?</label>
                    <select id="howHeard" name="source">
                        <option value="">Select source</option>
                        <option value="Google Search">Google Search</option>
                        <option value="Social Media">Social Media</option>
                        <option value="Education Fair">Education Fair</option>
                        <option value="Friend/Family Referral">Friend/Family Referral</option>
                        <option value="Education Agent">Education Agent</option>
                        <option value="University Website">University Website</option>
                        <option value="Advertisement">Advertisement</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                
                <div class="form-group full-width">
                    <label for="message">Questions or Additional Information <span class="required">*</span></label>
                    <textarea id="message" name="message" required placeholder="Tell us about your academic goals, specific questions about programs, admission requirements, scholarships, or anything else you'd like to know..."></textarea>
                </div>
            </div>
            
            <!-- Hidden fields -->
            <input type="text" name="_honeypot" class="hidden">
            
            <button type="submit" class="submit-btn" id="submitBtn">
                Submit Enquiry
            </button>
            
            <div class="privacy-note">
                <strong>Privacy Notice:</strong> Your information will be used to respond to your enquiry and provide relevant program information. We respect your privacy and will not share your details with third parties without your consent.
            </div>
        </form>
    </div>

    <script>
        // Toast notification system
        function createToast(type, title, message, duration = 5000) {
            const toastContainer = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            const toastId = 'toast-' + Date.now();
            
            toast.className = `toast ${type}`;
            toast.id = toastId;
            
            const icon = type === 'success' ? '✓' : '✕';
            
            toast.innerHTML = `
                <div class="toast-icon">${icon}</div>
                <div class="toast-content">
                    <div class="toast-title">${title}</div>
                    <div class="toast-message">${message}</div>
                </div>
                <button class="toast-close" onclick="removeToast('${toastId}')">&times;</button>
                <div class="toast-progress" style="width: 100%; transition-duration: ${duration}ms;"></div>
            `;
            
            toastContainer.appendChild(toast);
            
            // Trigger animation
            setTimeout(() => {
                toast.classList.add('show');
                // Start progress bar animation
                setTimeout(() => {
                    const progress = toast.querySelector('.toast-progress');
                    if (progress) {
                        progress.style.width = '0%';
                    }
                }, 100);
            }, 100);
            
            // Auto remove toast
            setTimeout(() => {
                removeToast(toastId);
            }, duration);
            
            return toastId;
        }
        
        function removeToast(toastId) {
            const toast = document.getElementById(toastId);
            if (toast) {
                toast.style.transform = 'translateX(100%)';
                toast.style.opacity = '0';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 400);
            }
        }

        // Function to combine first and last name
        function updateFullName() {
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;
            document.getElementById('fullName').value = `${firstName} ${lastName}`.trim();
        }

        // Update full name when first or last name changes
        document.getElementById('firstName').addEventListener('input', updateFullName);
        document.getElementById('lastName').addEventListener('input', updateFullName);

        // Form submission handler
        document.getElementById('universityForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Update full name before submission
            updateFullName();
            
            const formData = new FormData(this);
            const data = {};
            
            // Convert FormData to JSON object
            for (let [key, value] of formData.entries()) {
                if (key !== '_honeypot' && value.trim() !== '') {
                    data[key] = value;
                }
            }
            
            const submitBtn = document.getElementById('submitBtn');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.innerHTML = '<span class="spinner"></span>Submitting...';
            submitBtn.disabled = true;
            
            // Simulate API call (replace with actual API endpoint)
            fetch('http://localhost:8080/api/public/forms/9p0a5po58v', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(result => {
                // Success toast
                createToast(
                    'success',
                    'Enquiry Submitted Successfully!',
                    'Thank you for your interest. We\'ll get back to you within 24-48 hours.',
                    6000
                );
                
                // Reset form
                document.getElementById('universityForm').reset();
            })
            .catch(error => {
                console.error('Error:', error);
                // Error toast
                createToast(
                    'error',
                    'Submission Failed',
                    'Please try again or contact us directly if the problem persists.',
                    6000
                );
            })
            .finally(() => {
                // Restore button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // Demo buttons for testing (remove in production)
        document.addEventListener('DOMContentLoaded', function() {
            // You can test the toasts by calling these functions in console:
            // createToast('success', 'Success!', 'This is a success message');
            // createToast('error', 'Error!', 'This is an error message');
        });
    </script>
</body>
</html>